{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid:830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Test Airflow func",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true,
            "envFile":"/home/<USER>/work/airflow/airflow/dev.env",
            "env": {
                 "PYTHONPATH":"/home/<USER>/work/airflow/airflow/lib:/home/<USER>/work/airflow/airflow/:/home/<USER>/work/airflow/airflow/dags",

                 "grant_type":"authorization_code",

                 "client_id":"<EMAIL>",

                 "client_secret":"h96CSV68kIbuu_9AG3OXiwkgd0L8skPq-dtLAz_p",

                 "redirect_uri":"https://test.dev.mynsystems.com/integrations",

                 "Authorization":"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6Im9CMUhZMktlZ185NEtmS3hTYmlTOGw5QjVRdyIsImtpZCI6Im9CMUhZMktlZ185NEtmS3hTYmlTOGw5QjVRdyJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dqOzvDa13AMTqvMYf55haTjiQIUVtMptZ3DVFstphowqJzAo-dsz1dmx_jdDi80YBF8CjJq9drGv5uodEnESCoJWHR1SgUbtzute7I2IDZX79A31nP3fC-X6NZwwxK67uVLnr5T9XiHBeZrKbsz-eFotICiGai3TLJU0N7XS1OnVmaNpaZutLfgxo9Fdx3mWNNKCU2G0RIbtFYe_SQHOk4fm52omS_HGwV1XVzzdOrAvdEfiiDJ1oC4jlFZ5tuMf9ffMhL9rYkr2v3VD07b4RoPWoesvy-KTK1kbCZ2tMAP4O1xjZ2Lz3_HGmgdwlofTAHSBauS5eqilbinZKqYGyQ",

                 "postgres_doc":"postgresql://pguser:<EMAIL>:5432/reports",

                 "SENTRY_URL":"https://<EMAIL>/6587080",

                 "SENTRY_DSN":"https://<EMAIL>/6587080",

                 "GOOGLE_ANALYTICS_BASE64_ENCODED_PRIVATE_KEY":"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",

                 "GOOGLE_ANALYTICS_KEY_ID":"7dc30fdb441b4456f33ef312b78a46731286dc37",

                 "GOOGLE_ANALYTICS_CLIENT_EMAIL":"<EMAIL>",

                 "GOOGLE_ANALYTICS_SCOPE":"https://www.googleapis.com/auth/analytics.readonly",

                 "GOOGLE_ANALYTICS_AUDIENCE":"https://oauth2.googleapis.com/token",

                 "LINKEDIN_REPORTS_EMAIL": "<EMAIL>",

                 "LINKEDIN_REPORTS_PASSWORD": "3gW58%JW!",

                 "GOOGLE_CLIENT_SECRET_ID": "pczfzuIysGjFGNPxLLkM-esV",

                 "GOOGLE_CLIENT_ID": "947509003710-tck3j30up0klj47fc5r08amr0dgmqk48.apps.googleusercontent.com",

                 "GOOGLE_REFRESH_TOKEN": "1//04lTPG807sd49CgYIARAAGAQSNwF-L9IravTbDBSngWtNIm2o51HDWxh4PQikVPVUPyQDvKiapa5_5U4FXSIfftxT5HG4mHcDsdM",
                 
                 "GCM_PAID_SEARCH_GOOGLE_CLIENT_SECRET_ID": "pczfzuIysGjFGNPxLLkM-esV",

                 "GCM_PAID_SEARCH_GOOGLE_CLIENT_ID": "947509003710-tck3j30up0klj47fc5r08amr0dgmqk48.apps.googleusercontent.com",

                 "GCM_PAID_SEARCH_GOOGLE_REFRESH_TOKEN": "1//04lTPG807sd49CgYIARAAGAQSNwF-L9IravTbDBSngWtNIm2o51HDWxh4PQikVPVUPyQDvKiapa5_5U4FXSIfftxT5HG4mHcDsdM",
                
                 "GCM_PAID_SEARCH_WAIT_SLEEP":"4",

                 "DV360_GOOGLE_CLIENT_ID":"947509003710-tck3j30up0klj47fc5r08amr0dgmqk48.apps.googleusercontent.com",

                "DV360_GOOGLE_REFRESH_TOKEN":"1//04lTPG807sd49CgYIARAAGAQSNwF-L9IravTbDBSngWtNIm2o51HDWxh4PQikVPVUPyQDvKiapa5_5U4FXSIfftxT5HG4mHcDsdM",

                "DV360_GOOGLE_CLIENT_SECRET_ID":"pczfzuIysGjFGNPxLLkM-esV"
            }
        }
    ]
}