{
    "python.analysis.extraPaths": [

      "./lib",
      "./dags"
    ],
    "python.autoComplete.extraPaths": [
      "./lib",
      "./dags"
      ],
      
    "python.envFile": "${workspaceFolder}/dev.env",
    "workbench.colorCustomizations": {
      "activityBar.activeBackground": "#984753",
      "activityBar.background": "#984753",
      "activityBar.foreground": "#e7e7e7",
      "activityBar.inactiveForeground": "#e7e7e799",
      "activityBarBadge.background": "#5fae51",
      "activityBarBadge.foreground": "#15202b",
      "commandCenter.border": "#e7e7e799",
      "sash.hoverBorder": "#984753",
      "statusBar.background": "#753740",
      "statusBar.foreground": "#e7e7e7",
      "statusBarItem.hoverBackground": "#984753",
      "statusBarItem.remoteBackground": "#753740",
      "statusBarItem.remoteForeground": "#e7e7e7",
      "titleBar.activeBackground": "#753740",
      "titleBar.activeForeground": "#e7e7e7",
      "titleBar.inactiveBackground": "#75374099",
      "titleBar.inactiveForeground": "#e7e7e799"
    },
    "peacock.color": "#753740",
    "terminal.integrated.env.linux": {
       "CORE_DB_NAME":"TEST_CORE",
       "STAGE_DB_NAME":"TEST_STAGE",
       "TRANSFORM_DB_NAME":"TEST_TRANSFORM",
       "DATAMART_DB_NAME":"TEST_DATAMART",
    },
    "dbt.enableNewLineagePanel": true,
    "dbt.perspectiveTheme": "Monokai"
    }