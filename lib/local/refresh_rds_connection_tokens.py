#!/usr/bin/env python
import os
import boto3
from airflow.models import Connection
from airflow.settings import Session

from local.constants import AIRFLOW_CONNECTIONS

AWS_REGION = os.getenv("AWS_REGION", "eu-west-1")


def refresh_token(conn_id):
    session = Session()
    conn = session.query(Connection).filter(Connection.conn_id == conn_id).first()
    if not conn:
        print(f"Connection {conn_id} not found.")
        return

    extra = conn.extra_dejson
    region = extra.get("region", AWS_REGION)
    client = boto3.client("rds", region_name=region)
    new_token = client.generate_db_auth_token(
        DBHostname=conn.host,
        Port=conn.port,
        DBUsername=conn.login,
        Region=region
    )
    conn.password = new_token
    session.commit()
    print(f"Updated token for connection: {conn_id}")


def refresh_all_tokens():
    for cid in AIRFLOW_CONNECTIONS:
        refresh_token(cid)


if __name__ == "__main__":
    refresh_all_tokens()
