#!/usr/bin/env python
import os
import json
from airflow.models import Connection
from airflow.settings import Session
import boto3
from local.constants import AIRFLOW_CONNECTIONS
from myn_airflow_lib.resolvers import EnvironmentResolver

# Postgres connection parameters from env
RDS_HOST = os.getenv("RDS_HOST", "localhost")
RDS_PORT = os.getenv("RDS_PORT", "5432")
RDS_USER = os.getenv("RDS_USER", "ro")
AWS_REGION = os.getenv("AWS_REGION", "eu-west-1")
USE_IAM = os.getenv("USE_AWS_IAM", "false").lower() == "true"
env_resolver = EnvironmentResolver()


def get_db_password():
    if os.getenv("USE_AWS_IAM", "false").lower() == "true":
        client = boto3.client('rds', region_name=AWS_REGION)
        token = client.generate_db_auth_token(DBHostname=RDS_HOST,
                                              Port=int(RDS_PORT),
                                              DBUsername=RDS_USER,
                                              Region=AWS_REGION)
        return token
    else:
        return os.environ["RDS_PASSWORD"]


def add_connection(conn_id, db_name):
    session = Session()
    if session.query(Connection).filter(Connection.conn_id == conn_id).first():
        print(f"Connection {conn_id} already exists.")
        return

    password = get_db_password()
    conn = Connection(
        conn_id=conn_id,
        conn_type="postgres",
        host=RDS_HOST,
        login=RDS_USER,
        password=password,
        port=int(RDS_PORT),
        schema=db_name
    )

    if USE_IAM:
        extra = {"iam": True}
        conn.set_extra(json.dumps(extra))

    session.add(conn)
    session.commit()
    print(f"Added connection: {conn_id}")


def add_snowflake_connection():
    session = Session()
    conn_id = "snowflake_dbt"
    if session.query(Connection).filter(Connection.conn_id == conn_id).first():
        print(f"Connection {conn_id} already exists.")
        return

    sf_user = os.getenv("SNOWFLAKE_USER")
    sf_account = os.getenv("SNOWFLAKE_ACCOUNT")
    sf_role = os.getenv("SNOWFLAKE_ROLE")
    sf_key_path = "/opt/airflow/keys/rsa_key_snowflake.p8"

    conn = Connection(
        conn_id=conn_id,
        conn_type="snowflake",
        login=sf_user,
    )

    # Set additional parameters via extra
    extra = {
        "account": sf_account,
        "role": sf_role,
        "authenticator": "private_key",
        "private_key_content": None
    }

    if sf_key_path and os.path.exists(sf_key_path):
        with open(sf_key_path, 'r') as f:
            extra["private_key_content"] = f.read().strip()

    conn.set_extra(json.dumps(extra))
    session.add(conn)
    session.commit()
    print(f"Added Snowflake connection: {conn_id}")


if __name__ == "__main__":
    # Add Postgres connections from your defined mapping
    for _conn_id, _db_name in AIRFLOW_CONNECTIONS.items():
        add_connection(_conn_id, _db_name)

    # Add the Snowflake connection
    add_snowflake_connection()
