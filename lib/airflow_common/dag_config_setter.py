from dataclasses import dataclass
from typing import Any, Optional, Type, List
from myn_airflow_lib.constants import MAIN_DAG_FLOW_NAME, IMPLY_FLOW_NAME
from myn_airflow_lib.commons import get_main_dags
from airflow.models.param import Param


@dataclass
class ParamSet:
    key : str
    type_ : str
    default_value: Any
    items : Optional[str] = None
    desc : str = ''

    @property
    def param_value(self) -> dict:
        """Returns a dictionary representation of the parameter for Airflow DAGs."""
        param_args = {"default": self.default_value, "type": self.type_}
        
        if self.items:
            param_args["items"] = {"type": self.items}  # Ensure `items` is a dict
        
        return {self.key: Param(**param_args)}

    
    def __hash__(self) -> int:
        return hash(self.key)


def import_flows():
    INGESTION_DAGS = get_main_dags()
    MAIN_DAG = MAIN_DAG_FLOW_NAME
    IMPLY_DAG = IMPLY_FLOW_NAME
    return INGESTION_DAGS, [MAIN_DAG], [IMPLY_DAG]


def construct_main_dict():

    INGESTION_DAGS, MAIN_DAG, IMPLY_DAG = import_flows()
    # The complete list of all params awailable in the airflow service
    # The value is the list of the flows eligible to receive particular key
    PARAMS_DICT = {
        ParamSet("force_historical_repull", 'boolean', False) : INGESTION_DAGS + MAIN_DAG,
        ParamSet("etl_endpoint_mock", 'array', [], 'object',): INGESTION_DAGS + MAIN_DAG,
        ParamSet("airflow_flows", 'array', [], 'string'): MAIN_DAG,
        ParamSet("ingestion_mode", 'string', ''): MAIN_DAG + IMPLY_DAG,
        ParamSet("sanity_pull", 'boolean', False): MAIN_DAG + INGESTION_DAGS,
        ParamSet("clients_to_run_on", 'array', [], 'integer'): INGESTION_DAGS + MAIN_DAG,
        ParamSet("timezone_groups", 'array', [], 'integer'): INGESTION_DAGS + MAIN_DAG,
        ParamSet("historical_range_in_days", 'integer', 0): INGESTION_DAGS + MAIN_DAG,
        ParamSet("historical_only", 'boolean', False): INGESTION_DAGS + MAIN_DAG,
        ParamSet("detailed_logging", 'boolean', False): MAIN_DAG + IMPLY_DAG,
        ParamSet("skip_ingestion", 'boolean',False): MAIN_DAG + IMPLY_DAG,
        ParamSet("tasks", 'array', [], 'string'): MAIN_DAG
    }
    return PARAMS_DICT


class DagConfigSetter():

    def __init__(self, flow_name) -> None:
        self._flow_name = flow_name
        self.params_dict = construct_main_dict()

    
    def get_params(self) -> dict:

        # get the list of awailable params
        eligible_params = [item[0] for item in self.params_dict.items() if self._flow_name in item[1]]
        
        res = {}
        [res.update(param.param_value) for param in eligible_params]
        return res
