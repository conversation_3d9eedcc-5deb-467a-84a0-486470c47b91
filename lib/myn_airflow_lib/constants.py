import datetime
from dataclasses import dataclass
from kubernetes.client import models as k8s

MAIN_DAG_FLOW_NAME = "main_dag"
IMPLY_FLOW_NAME = "IMPLY"
GLUE_FLOW_NAME = "Glue"
GLUE_DS_FLOW_NAME = "GlueDsAi"

class ENVIRONMENTS:
    TEST = 'test'
    PROD = 'prod'
    UAT = 'uat'
    DEMO = 'demo'


@dataclass(frozen=True)
class DatabaseInfo:
    postgres_database_name: str
    postgres_conn_id: str
    tables: tuple
    snowflake_database_name: str = None #NOSONAR


@dataclass(frozen=True)
class TableInfo:
    table_name: str
    clean_jsons: tuple = None #NOSONAR
    force_str: tuple = None #NOSONAR
    priority_weight: int = None #NOSONAR


FACEBOOK_ETL_ENDPOINT = "http://facebook-instagram/api/etl/facebook/?valid=true"
FACEBOOK_DOMAIN = "https://graph.facebook.com/v20.0"
FACEBOOK_CUSTOM_CONVERSIONS_ENDPOINT = 'https://graph.facebook.com/v20.0/{account_id}/customconversions?access_token={access_token}&fields=id,name'
FACEBOOK_BAD_REQUEST_RETRY_WAIT = 10
FACEBOOK_PAGE_SIZE = 500
FACEBOOK_MAX_WAIT_POLL_REPORT = 60 * 60  # Facebook has 1 hour to create report
FACEBOOK_MAX_DAYS_REQUESTED_IN_A_QUERY = 60
FACEBOOK_QUOTA_REACHED_RETRIES = 100  # How many times to retry if quota reached
FACEBOOK_QUOTA_REACHED_RETRY_WAIT = 120  # Wait 2 mintues if quota reached
FACEBOOK_STANDARD_QUERIES_BATCH_SIZE = 16
FACEBOOK_HISTORICAL_QUERIES_BATCH_SIZE = 12
# How many times to retry for requests errors (except quota issues)
FACEBOOK_BAD_REQUEST_RETRIES = 5
FACEBOOK_DEMOGRAPHIC_TYPES = ['age', 'gender', 'country']
CLOSED_FB_ACCOUNTS_LIST = ['act_463494625107303', 'act_798215177419764', 'act_4384674651597107',
                           'act_1113271309081874', 'act_1366832967023735', 'act_1689014481290894',
                           'act_1756678134622823',
                           'act_181911179838035', 'act_2798010713752859', 'act_371885353342993', 'act_449742926057045',
                           'act_469035044413690', 'act_549055852977363', 'act_623340141427507', 'act_665583496984824',
                           'act_690297875230830', 'act_1891285824513257']
GOOGLE_REPORTS_URL_FORMAT = 'https://dfareporting.googleapis.com/dfareporting/v4/userprofiles/{user_id}/reports'
GOOGLE_FILE_URL_FORMAT = 'https://dfareporting.googleapis.com/dfareporting/v4/userprofiles/{user_id}/reports/{report_id}/files/{file_id}'
GOOGLE_RUN_QUERY_URL_FORMAT = 'https://dfareporting.googleapis.com/dfareporting/v4/userprofiles/{user_id}/reports/{report_id}/run'
GOOGLE_OAUTH2_TOKEN_URL = 'https://accounts.google.com/o/oauth2/token'
CM360_ETL_ENDPOINT = 'http://cm360/api/campaign_manager/v2/etl/?valid=true'
DV360_ETL_ENDPOINT = 'http://dv360/api/etl/dv360/?valid=true'

HISTORICAL_LOG_TABLE = 'historical_log'
REPORTS_LOG_TABLE_SCHEDULED = 'reports_airflow_cm_log'
REPORTS_QUERY_QUEUE_TABLE = 'reports_query_queue'

# Adform flow
ADFORM_ETL_URL = "http://adform/api/adform/v2/nifi/"
ADFORM_REFRESH_TOKEN_URL_FORMAT = "http://adform/api/adform/refresh_token?integration={integration}"
ADFORM_GRAB_NEW_TOKEN_URL_FORMAT = "http://adform/api/adform/v2/nifi/{integration}"
ADFORM_STATS_ENDPOINT = "https://api.adform.com/v1/buyer/stats/data"
REQUESTS_BAD_STATUSES = [503, 429, 500, 403, 401, 404, 400]
ADFORM_WAIT_SLEEP = 120

# Google analystics flow
GA_ACCOUNT_TYPE = "google_analitics"
GA_API_VERSION = "v4"
GA_CAMPAIGN_TYPE = "google_analitics"
GA_CM_API_ENDPOINT = "http://cm360/api/campaign_manager/v2/nifi/"
GA_API_ENDPOINT = "http://ga/api/nifi/google_analytics/"
GA_V2_TABLE_NAME = "reports_google_analytics_v2"
GA_ADWORDS_TABLE_NAME = "reports_google_analytics_adwords"

GA_SEVEN_DAYS_BACK_FORMAT = int(
    (datetime.date.today() - datetime.timedelta(days=7)).strftime("%Y%m%d")
)
GA_SEVEN_DAYS_BACK = (datetime.date.today() -
                      datetime.timedelta(days=7)).strftime("%Y-%m-%d")
GA_YESTERDAY = (datetime.date.today() -
                datetime.timedelta(days=1)).strftime("%Y-%m-%d")
GA_YESTERDAY_FORMAT = int(
    (datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y%m%d")
)

GA_SLEEP_TIME = 15
GA_TABLE_NAME = "reports_google_analytics"
GA_WAITING_REPORTS = GA_SLEEP_TIME * 2
GA_BAD_STATUSES = [503, 429, 500, 403, 401, 404, 400]
GA_AlGORITHM = 'RS256'
GA_REPORT_API_URL = f"https://analyticsreporting.googleapis.com/{GA_API_VERSION}/reports:batchGet"
GA_V2_CAMPAIGN_TYPE = "google_analytics_v2"

# GA4
GA4_ETL_ENDPOINT = "http://ga4/api/nifi/ga4"
GA4_SCOPES = "https://www.googleapis.com/auth/analytics.readonly"
GA4_TOKEN_URI = "https://oauth2.googleapis.com/token"

ADWORDS_ETL_ENDPOINT = 'http://adwords/api/etl/adwords/?valid=true'
ADWORDS_DOMAIN_URL = 'https://googleads.googleapis.com/v20/customers'

# Floodlight
GCM_REPORTS_URL_FORMAT = 'https://www.googleapis.com/dfareporting/{version}/userprofiles/{user_id}/reports'
GCM_FILE_URL_FORMAT = 'https://www.googleapis.com/dfareporting/{version}/userprofiles/{user_id}/reports/{report_id}/files/{file_id}'
GCM_RUN_QUERY_URL_FORMAT = 'https://www.googleapis.com/dfareporting/{version}/userprofiles/{user_id}/reports/{report_id}/run'

# DV360
DV360_CREATE_QUERY_URL = 'https://doubleclickbidmanager.googleapis.com/v2/queries'
DV360_RUN_REPORTS_URL = 'https://doubleclickbidmanager.googleapis.com/v2/queries/{query_id}:run'
DV360_DOWNLOAD_REPORT_URL = 'https://doubleclickbidmanager.googleapis.com/v2/queries/{query_id}/reports/{report_id}'

# APSFLYER
APSFLYER_API_STR = "https://hq1.appsflyer.com/api/agg-data/export/app/{app_id}/{report_type}/v5?to={date_to}&from={date_from}"
APSFLYER_ETL_ENDPOINT = "http://appsflyer/api/appsflyer/v1/nifi"

# MICROSOFT ADS
MICROSOFT_ADS_ETL_ENDPOINT = 'http://bing/api/bing/nifi'
MICROSOFT_ADS_AUTH_URL = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
MICROSOFT_ADS_REPORTING_URL = "https://reporting.api.bingads.microsoft.com/Api/Advertiser/Reporting/v13/ReportingService.svc"
# ZEMANTA
ZEMANTA_ETL_ENDPOINT = "http://zemanta/api/etl/zemanta/?valid=true"
ZEMANTA_API_ENDPOINT = "https://oneapi.zemanta.com/rest/v1/reports/"
ZEMANTA_WAIT_SLEEP = 35
ZEMANTA_RETRY_API_ATTEMPTS = 12

# THEOUTPLAY
THEOUTPLAY_ETL_ENDPOINT = "http://theoutplay/api/nifi/theoutplay/"
THEOUTPLAY_CLOSED_ACCOUNTS = ["illimity-4oecuHXHbxlxeQ8"]
THEOUTPLAY_CLOSED_CAMPAIGNS = ["d283d3bb-7adb-4e3f-989b-6fcc61ffa7b9"]

# DRUID
DRUID_INGESTION_ENDPOINT = "http://druid-client.default.svc.cluster.local/deploy/full"
DRUID_PATH = 'final/druid_v2/main/'
# K8s
K8S_DBT_NODE = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            tolerations=[
                k8s.V1Toleration(
                    effect="NoSchedule",
                    key="dag",
                    operator="Equal",
                    value="true"
                )
            ],
            node_selector={"dag": "true"},
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(
                                            requests={'cpu': '1', 'memory': '500Mi'},
                                            limits={'cpu': '1', 'memory': '1Gi'}))],

        )
    )
}
EXECUTOR_STABLE_NODES = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(requests={'cpu': '1', 'memory': '500Mi'}))],
            node_selector={"dag": "true"},
            tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key="dag",
                operator="Equal",
                value="true"
            )
        ]
        )
    )
}

K8S_SPECS = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(requests={'cpu': '1', 'memory': '1Gi'},
                                                                             limits={'memory': '10Gi'}))],
            node_selector={"dag": "true"},
            tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key="dag",
                operator="Equal",
                value="true"
            )
        ]
        )
    )
}


TRIGGER_DAG_NODES = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(requests={'cpu': '1', 'memory': '500Mi'}))],
            node_selector={"dag": "true"},
            tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key="dag",
                operator="Equal",
                value="true"
            )
        ]
        )
    )
}

BRANCH_PODS = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(requests={'cpu': '1', 'memory': '500Mi'}))],
            node_selector={"dag": "true"},
            tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key="dag",
                operator="Equal",
                value="true"
            )
        ]
        )
    )
}

CUSTOM_TRIGGER_PODS = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(requests={'cpu': '1', 'memory': '500Mi'}))],
            node_selector={"dag": "true"},
            tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key="dag",
                operator="Equal",
                value="true"
            )
        ]
        )
    )
}

GLUE_PODS = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(requests={'cpu': '1', 'memory': '1Gi'}))],
            node_selector={"dag": "true"},
            tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key="dag",
                operator="Equal",
                value="true"
            )
        ]
        )
    )
}

IMPLY_EXTRAGLUE_PODS = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(requests={'cpu': '1', 'memory': '500Mi'}))],
            node_selector={"dag": "true"},
            tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key="dag",
                operator="Equal",
                value="true"
            )
        ]
        )
    )
}


TRIGGER_GLUE_JOB_POD = {
    'pod_override': k8s.V1Pod(
        spec=k8s.V1PodSpec(
            containers=[k8s.V1Container(name='base',
                                        resources=k8s.V1ResourceRequirements(requests={'cpu': '1', 'memory': '500Mi'}))],
            node_selector={"dag": "true"},
            tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key="dag",
                operator="Equal",
                value="true"
            )
        ]
        )
    )
}


# Blacklisted clients
BLACKLISTED_CLIENTS_LIST = [1,6,7,8,9,10,11,12,13,14,15,16,17,18,
                    19,20,21,22,23,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,
                    44,45,46,47,48,50,51,52,53,54,56,62,64,65,66,67,68,69,70,71,72,73,75,76,
                    79,80,81,85,87,88,89,94,95,98,100,101,103,107,108,109,110,
                    112,118,121,126,127,129,130,132,139,140,150,3,5]
# TIMEZONE STUFF
DEFAULT_TIMEZONE_GROUP = 2
TIMEZONE_PREFIX_MAP = {
    'Africa' : 1,
    'Africa-Novo' : 1,
    'Europe' : 1,
    'GMT' : 1,
    'UTC' : 1
}

GLUE_IMPLY_ONLY_APPROX_TIMES = [
        datetime.datetime.strptime('11:00', '%H:%M'),
        datetime.datetime.strptime('13:00', '%H:%M'),
        datetime.datetime.strptime('17:00', '%H:%M'),
        datetime.datetime.strptime('19:00', '%H:%M')
    ]
TIMEZON_INGESTION_MAP = {
        datetime.datetime.strptime('04:00', '%H:%M'): 1,
        datetime.datetime.strptime('08:00', '%H:%M'): 2
}

OMNI_ETL_ENDPOINT = 'http://omni-connector/api/omni-connector-service/etl'


FACEBOOK_DEFAULT_ACTION_TYPES = {
    # Mobile App Events
    "app_custom_event.fb_mobile_achievement_unlocked": "Mobile App Feature Unlocks",
    "app_custom_event.fb_mobile_activate_app": "Mobile App Starts",
    "app_custom_event.fb_mobile_add_payment_info": "Mobile App Payment Details",
    "app_custom_event.fb_mobile_add_to_cart": "Mobile App Adds To Cart",
    "app_custom_event.fb_mobile_add_to_wishlist": "Mobile App Adds to Wishlist",
    "app_custom_event.fb_mobile_complete_registration": "Mobile App Registrations",
    "app_custom_event.fb_mobile_content_view": "Mobile App Content Views",
    "app_custom_event.fb_mobile_initiated_checkout": "Mobile App Checkouts",
    "app_custom_event.fb_mobile_level_achieved": "Mobile App Achievements",
    "app_custom_event.fb_mobile_purchase": "Mobile App Purchases",
    "app_custom_event.fb_mobile_rate": "Mobile App Ratings",
    "app_custom_event.fb_mobile_search": "Mobile App Searches",
    "app_custom_event.fb_mobile_spent_credits": "Mobile App Credit Spends",
    "app_custom_event.fb_mobile_tutorial_completion": "Mobile App Tutorial Completions",
    "app_custom_event.other": "Other Mobile App Actions",

    # App actions
    "app_use": "App Uses",

    # Facebook actions
    "checkin": "Check-ins",
    "comment": "Post Comments",
    "credit_spent": "Credit Spends",
    "games.plays": "Game Plays",
    "landing_page_view": "Landing Page Views",
    "like": "Page Likes",
    "link_click": "Link Clicks",
    "mobile_app_install": "Mobile App Installs",

    # Offsite Conversion (Facebook Pixel)
    "offsite_conversion.custom.<custom_conv_id>": "Custom Conversions defined by the advertiser",
    "offsite_conversion.fb_pixel_add_payment_info": "Adds Payment Info",
    "offsite_conversion.fb_pixel_add_to_wishlist": "Adds To Wishlist",
    "offsite_conversion.fb_pixel_complete_registration": "Completed Registration",
    "offsite_conversion.fb_pixel_custom": "Custom pixel events defined by the advertiser",
    "offsite_conversion.fb_pixel_initiate_checkout": "Initiates Checkout",
    "offsite_conversion.fb_pixel_lead": "Leads",
    "offsite_conversion.fb_pixel_view_content": "Views Content",

    # Onsite Conversion (On-Facebook)
    "onsite_conversion.flow_complete": "On-Facebook Workflow Completions",
    "onsite_conversion.messaging_block": "Blocked Messaging Conversations",
    "onsite_conversion.messaging_conversation_started_7d": "Messaging Conversations Started",
    "onsite_conversion.messaging_first_reply": "New Messaging Conversations",
    "onsite_conversion.messaging_user_subscribed": "Messaging Subscriptions",
    "onsite_conversion.post_save": "Post Saves",
    "onsite_conversion.purchase": "On-Facebook Purchases",

    # Other actions
    "outbound_click": "Outbound Clicks",
    "photo_view": "Page Photo Views",
    "post": "Post Shares",
    "post_reaction": "Post Reactions",
    "rsvp": "Event Responses",
    "video_view": "3-Second Video Views",
    "contact_total": "Contacts",
    "contact_website": "Website Contacts",
    "contact_mobile_app": "Mobile App Contacts",
    "contact_offline": "Offline Contacts",

    # Product customization
    "customize_product_total": "Products Customized",
    "customize_product_website": "Website Products Customized",
    "customize_product_mobile_app": "Mobile App Products Customized",
    "customize_product_offline": "Offline Products Customized",

    # Donations
    "donate_total": "Donations",
    "donate_website": "Website Donations",
    "donate_on_facebook": "On Facebook Donations",
    "donate_mobile_app": "Mobile App Donations",
    "donate_offline": "Offline Donations",

    # Location search
    "find_location_total": "Location Searches",
    "find_location_website": "Website Location Searches",
    "find_location_mobile_app": "Mobile App Location Searches",
    "find_location_offline": "Offline App Location Searches",

    # Appointments
    "schedule_total": "Appointments Scheduled",
    "schedule_website": "Website Appointments Scheduled",
    "schedule_mobile_app": "Mobile App Appointments Scheduled",
    "schedule_offline": "Offline App Appointments Scheduled",

    # Trials
    "start_trial_total": "Trials Started",
    "start_trial_website": "Website Trials Started",
    "start_trial_mobile_app": "Mobile App Trials Started",
    "start_trial_offline": "Offline Trials Started",

    # Applications
    "submit_application_total": "Applications Submitted",
    "submit_application_website": "Website Applications Submitted",
    "submit_application_mobile_app": "Mobile App Applications Submitted",
    "submit_application_offline": "Offline Applications Submitted",
    "submit_application_on_facebook": "On Facebook Applications Submitted",

    # Subscriptions
    "subscribe_total": "Subscriptions",
    "subscribe_website": "Website Subscriptions",
    "subscribe_mobile_app":

    "Mobile App Subscriptions",
    "subscribe_offline": "Offline Subscriptions",

    # Recurring subscriptions
    "recurring_subscription_payment_total": "Recurring Subscription Payments",
    "recurring_subscription_payment_website": "Website Recurring Subscription Payments",
    "recurring_subscription_payment_mobile_app": "Mobile App Recurring Subscription Payments",
    "recurring_subscription_payment_offline": "Offline Recurring Subscription Payments",

    # Canceled subscriptions
    "cancel_subscription_total": "Canceled Subscriptions",
    "cancel_subscription_website": "Website Canceled Subscriptions",
    "cancel_subscription_mobile_app": "Mobile App Canceled Subscriptions",
    "cancel_subscription_offline": "Offline Canceled Subscriptions",

    # Ads
    "ad_click_mobile_app": "In-App Ad Clicks",
    "ad_impression_mobile_app": "In-App Ad Impressions",

    # Click to call
    "click_to_call_call_confirm": "Estimated Call Confirmation Clicks",
    "click_to_call_native_call_placed": "Calls Placed",
    "click_to_call_native_20s_call_connect": "20s Calls Placed",
    "click_to_call_native_60s_call_connect": "60s Calls Placed",

    # Grouped action types
    "page_engagement": "Page Engagement",
    "post_engagement": "Post Engagement",
    "onsite_conversion.lead_grouped": "All On-Facebook Leads",
    "lead": "All offsite leads plus all On-Facebook leads",
    "leadgen_grouped": "On-Facebook leads from Messenger and Instant Forms",
    "omni_app_install": "App Installs",
    "omni_purchase": "Purchases",
    "omni_add_to_cart": "Adds to Cart",
    "omni_complete_registration": "Registrations Completed",
    "omni_view_content": "Content Views",
    "omni_search": "Searches",
    "omni_initiated_checkout": "Checkouts Initiated",
    "omni_achievement_unlocked": "Achievements Unlocked",
    "omni_activate_app": "App Activations",
    "omni_level_achieved": "Levels Achieved",
    "omni_rate": "Ratings Submitted",
    "omni_spend_credits": "Credit Spends",
    "omni_tutorial_completion": "Tutorials Completed",
    "omni_custom": "Custom Events",

    # Mint defined
    "add_payment_info": "Adds of Payment Info",
    "web_in_store_purchase": "Website Purchases",
    "offsite_conversion.fb_pixel_add_to_cart": "Add To Cart",
    "app_install": "App Install",
    "offsite_conversion.fb_pixel_purchase": "Offsite Purchases",
    "offsite_conversion.fb_pixel_search": "Offsite Searches",}

DATABASES_INFO = {
    "DATINTELL": DatabaseInfo(
        postgres_database_name="DATINTELL",
        postgres_conn_id='uri_datintell',
        tables=(
            TableInfo(table_name='campaign_campaigncollection'),
            TableInfo(table_name='builder_mode_mediaplan'),
            TableInfo(
                table_name='builder_mode_mediaplanitem',
                clean_jsons=('fields',)
            ),
            TableInfo(table_name='campaign_campaign'),
            TableInfo(table_name='campaign_mediamix'),
            TableInfo(table_name='campaign_ghostcampaign'),
            TableInfo(table_name='campaign_ghostcampaignpricingitem'),
            TableInfo(table_name='campaign_ghostcampaignpricingsubitem'),
        )
    ),
    "ADWORDS": DatabaseInfo(
        postgres_database_name="ADWORDS",
        postgres_conn_id='uri_adwords',
        tables=(
            TableInfo(table_name='campaign_adwordssearchrelatedcampaign'),
            TableInfo(table_name='campaign_adwordsadgroup'),
            TableInfo(table_name='campaign_adwordsad'),
            TableInfo(table_name='service_responsivesearchadmodel'),
            TableInfo(table_name='service_admodel'),
            TableInfo(table_name='service_videoadmodel'),
            TableInfo(table_name='service_responsivedisplayadmodel'),
            TableInfo(table_name='service_discoverycarouseladmodel'),
            TableInfo(table_name='service_discoverymultiassetadmodel'),
            TableInfo(table_name='service_imageadmodel'),
            TableInfo(table_name='service_html5uploadadmodel'),
            TableInfo(table_name='service_dynamichtml5admodel'),
            TableInfo(table_name='campaign_adwordssearchcampaign'),
            TableInfo(table_name='external_services_adwordsexternaldata'),
            TableInfo(table_name='service_expandeddynamictextadmodel'),
        )
    ),
    "BING": DatabaseInfo(
        postgres_database_name="BING",
        postgres_conn_id='uri_bing',
        tables=(
            TableInfo(table_name='integrations'),
            TableInfo(table_name='platform_settings'),
            TableInfo(table_name='campaigns'),
            TableInfo(table_name='ad_groups'),
            TableInfo(table_name='ads'),
        )
    ),
    "DV360": DatabaseInfo(
        postgres_database_name="DV360",
        postgres_conn_id='uri_dv360',
        tables=(
            TableInfo(table_name='dv360_dv360displaycampaign'),
            TableInfo(table_name='dv360_dv360integration'),
            TableInfo(table_name='dv360_dv360insertionorder', clean_jsons=('audience_list_include',)),
            TableInfo(table_name='dv360_dv360lineitem'),
            TableInfo(table_name='dv360_dv360insertionorder_budget_segments'),
            TableInfo(table_name='dv360_budgetsegment'),
            TableInfo(table_name='dv360_dv360adgroup'),
            TableInfo(table_name='dv360_dv360adgroupad'),
        )
    ),
    "FACEBOOK_INSTAGRAM": DatabaseInfo(
        postgres_database_name="FACEBOOK_INSTAGRAM",
        postgres_conn_id='uri_facebook_instagram',
        tables=(
            TableInfo(table_name='account_facebookexternaldata'),
            TableInfo(table_name='campaign_facebookcampaign', force_str=("state_message",)),
            TableInfo(table_name='campaign_facebookadsetmodel', force_str=(
                "application_config", "included_product_audience", "excluded_product_audience", "state_message",
                "schedule", "include_connections", "friends_of_connections", "exclude_connections",
                "exclude_targeting", "include_targeting", "exclude_locations", "locations")),
            TableInfo(table_name='campaign_facebookad', force_str=("state_message",)),
        )
    ),
    "ZEMANTA": DatabaseInfo(
        postgres_database_name="ZEMANTA",
        postgres_conn_id='uri_zemanta',
        tables=(
            TableInfo(table_name='integrations'),
            TableInfo(table_name='platform_settings'),
            TableInfo(table_name='campaigns'),
            TableInfo(table_name='ad_groups'),
            TableInfo(table_name='ads'),
        )
    ),
    "OMNI_CONNECTOR": DatabaseInfo(
        postgres_database_name="OMNI-CONNECTOR",
        snowflake_database_name="OMNI_CONNECTOR",
        postgres_conn_id='uri_omni-connector',
        tables=(
            TableInfo(table_name='integrations'),
            TableInfo(table_name='tile_settings'),
            TableInfo(table_name='campaigns'),
            TableInfo(table_name='ad_groups'),
            TableInfo(table_name='ads'),
        )
    ),
    "CM360": DatabaseInfo(
        postgres_database_name="CM360",
        postgres_conn_id='uri_cm360',
        tables=(
            TableInfo(table_name='campaign_manager_campaignmanagerintegration'),
            TableInfo(table_name='campaign_manager_campaignmanagersettings'),
            TableInfo(table_name='campaign_manager_campaignmanagercampaignmapping'),
            TableInfo(table_name='site_mapping'),
            TableInfo(table_name='site_mapping_assignment'),
            TableInfo(table_name='platform_grouping'),
            TableInfo(table_name='campaign_manager_placement'),
            TableInfo(table_name='campaign_manager_campaignmanagerfloodlightactivitymapping'),
        )
    ),
    "ADFORM": DatabaseInfo(
        postgres_database_name="ADFORM",
        postgres_conn_id='uri_adform',
        tables=(
            TableInfo(table_name='adform_adformintegration'),
            TableInfo(table_name='adform_adformsettings'),
            TableInfo(table_name='adform_adformcampaignmapping'),
            TableInfo(table_name='adform_adformmediamapping'),
            TableInfo(table_name='adform_adformmediamapping_campaign_mapping'),
            TableInfo(table_name='adform_adformlineitemmapping'),
            TableInfo(table_name='adform_adformbannermapping'),
            TableInfo(table_name='adform_adformtrackingfiltermapping'),
        )
    ),
    "APPSFLYER": DatabaseInfo(
        postgres_database_name="APPSFLYER",
        postgres_conn_id='uri_appsflyer',
        tables=(
            TableInfo(table_name='campaigns'),
            TableInfo(table_name='conversion_activities'),
            TableInfo(table_name='integrations'),
            TableInfo(table_name='media_sources'),
            TableInfo(table_name='settings'),
        )
    ),
    "GA4": DatabaseInfo(
        postgres_database_name="GA4",
        postgres_conn_id='uri_ga4',
        tables=(
            TableInfo(table_name='integrations'),
            TableInfo(table_name='utm_configs'),
            TableInfo(table_name='groupings'),
            TableInfo(table_name='rules'),
            TableInfo(table_name='conversions'),
        )
    ),
    # reports table should go to package schemas like Adwords or CM360
    "REPORTS": DatabaseInfo(
            postgres_database_name="REPORTS",
            postgres_conn_id='uri_reports',
            tables=(
                TableInfo(table_name='search_campaign_performance'),
                TableInfo(table_name='search_campaign_performance_mobile'),
                TableInfo(table_name='reports_adwords_ad_performance'),
                TableInfo(table_name='search_campaign_conversion_performance'),
                TableInfo(table_name='reports_adwords_ad_conversion_performance'),
                TableInfo(table_name='reports_adwords_action_mapping'),
                TableInfo(table_name='microsoft_advertising_estimated_campaign_level_v2'),
                TableInfo(table_name='microsoft_advertising_performance_ad_level'),
                TableInfo(table_name='dv360_base_reports'),
                TableInfo(table_name='dv360_base_reports_youtube'),
                TableInfo(table_name='social_campaigns_reports_v3'),
                TableInfo(table_name='meta_conversions_report'),
                TableInfo(table_name='meta_conversions_mapping'),
                TableInfo(table_name='meta_default_conversions_mapping'),
                TableInfo(table_name='reports_zemanta_performance_v2'),
                TableInfo(table_name='reports_xandr_performance_v3'),
                TableInfo(table_name='reports_linkedin_performance'),
                TableInfo(table_name='reports_tiktok_performance'),
                TableInfo(table_name='reports_amazon_performance'),
                TableInfo(table_name='reports_appsflyer_performance_v2'),
                TableInfo(table_name='campaign_manager_base_report'),
                TableInfo(table_name='campaign_manager_dv360_report'),
                TableInfo(table_name='campaign_manager_floodlight_base_report'),
                TableInfo(table_name='campaign_manager_paid_search_external_report'),
                TableInfo(table_name='reports_adformconversion_v2'),
                TableInfo(table_name='reports_ga4_base_performance_v2'),
                TableInfo(table_name='reports_ga4_base_sessions_performance_v2'),
                TableInfo(table_name='reports_ga4_autotag_performance_v2'),
                TableInfo(table_name='reports_ga4_autotag_sessions_performance_v2'),
            )
        ),

}

REPORTS_SF_DB = 'REPORTS'
REPORTS_SF_SCHEMA = 'PUBLIC'
