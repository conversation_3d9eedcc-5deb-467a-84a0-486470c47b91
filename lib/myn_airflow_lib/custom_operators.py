from datetime import datetime
import logging
import time
import pytz
from airflow.models import <PERSON>Com
from airflow.models.dagrun import Dag<PERSON>un
from airflow.utils.state import State
from myn_airflow_lib.constants import GLUE_IMPLY_ONLY_APPROX_TIMES, TIMEZON_INGESTION_MAP, TRIGGER_DAG_NODES, TRIGGER_GLUE_JOB_POD
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor
from airflow.models.baseoperator import BaseOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.providers.amazon.aws.operators.glue import GlueJobOperator
from airflow.providers.amazon.aws.operators.glue_crawler import GlueCrawlerOperator
from airflow.utils.db import provide_session
from myn_airflow_lib.commons import get_date_to_execute_on, is_first_saturday, iter_batch, glue_client_factory
from airflow.providers.amazon.aws.hooks.glue import G<PERSON><PERSON><PERSON><PERSON><PERSON>
from myn_airflow_lib.datadog import datadog_timeit, increment_metric
from airflow.exceptions import AirflowException
from airflow.api.common import trigger_dag
from datetime import timedelta
import re


class CustomGlueCrawlerOperator(GlueCrawlerOperator):
    def __init__(self, env_resolver, layer, **kwargs) -> None:
        self.env_resolver = env_resolver
        self.layer = layer
        self.crawler_name = None
        super().__init__(**kwargs)

    @datadog_timeit('crawler_name')
    def execute(self, context):
        env = self.env_resolver.environment
        if env is None:
            raise Exception('No environment variable exists')
        # we can get environment variable only on task PODS, so it's possible to get it only when execution started
        self.config['Name'] = f'{env}-{self.layer}-{self.config["Name"]}'
        self.crawler_name = self.config['Name']
        return super().execute(context)


class CustomGlueJobOperator(GlueJobOperator):
    def __init__(self, env_resolver, layer, glue_client, **kwargs) -> None:
        self.env_resolver = env_resolver
        self.layer = layer
        self.glue_client = glue_client
        # Api of GlueJobOperator requires argument for script_location, but inner logic works fine if it's None
        super().__init__(script_location=None, **kwargs)


    @datadog_timeit('job_name')
    def execute(self, context):
        # we can get environment variable only on task PODS, so it's possible to get it only when execution started
        env = self.env_resolver.environment
        if env is None:
            raise Exception('No environment variable exists')
        self.job_name = f'{env}-{self.layer}-{self.job_name}'
        hook = GlueJobHook(job_name=self.job_name)
        try:
            job_run_id = super().execute(context)
            job_state = hook.get_job_state(self.job_name,job_run_id)
            error_message = "None"
        except AirflowException:
            logging.critical(f'{self.job_name} job failed')
            job_state = "FAILED"
            # Get the latest job run and error message using boto3 instead
            response = self.glue_client.get_job_runs(JobName=self.job_name, MaxResults=1)
            job_run_id = response['JobRuns'][0]['Id']    
            error_message = response['JobRuns'][0]['ErrorMessage']
            logging.critical(f"Job: {self.job_name} failed with the error: \n {error_message}")
 
        return job_state, self.job_name, error_message, job_run_id


class CustomGlueJobOperatorNoParallel(PythonOperator):
    """
    This operator triggers a set list of glue jobs
    without waiting for the completion
    """
    def __init__(self, env, layer, glue_client_params, job_list=None, with_light_run=False, **kwargs):
        self.env = env
        self.with_light_run = with_light_run
        self.layer = layer
        self.glue_client_params = glue_client_params
        self.job_list = job_list
        super().__init__(**kwargs, python_callable=self._python_callable)

    def set_light_run_jobs(self, **kwargs):
        """
        function sets light run if only glue and imply
        """ # to be deleted
        task_id = kwargs['task'].task_id
        logging.info(f'task_id == {task_id}')
        if task_id == 'base_jobs':
            jobs_new = ['ghost_csv']
        elif task_id == 'final_jobs':
            jobs_new = ['druid_v3']
        else:
            jobs_new = []
        return jobs_new

    def is_light_run(self, **kwargs_approx_times):
        """
        checks for the dag start time to be near the additional run time schedule
        modifies the tasks to execute if needed
        """
        maximum_offset_minutes = 80 # minutes


        def is_scheduled_run(**kwargs):
            logging.info('state check for light run:')
            logging.info(kwargs['dag_run'].conf.get('current_run_number'))
            logging.info(not kwargs['dag_run'].conf.get('current_run_number'))
            return bool(kwargs['dag_run'].conf.get('current_run_number'))
        
        def _set_dag_conf_based_on_schedule_time(**kwargs):
            execution_date = datetime.now().strftime("%H:%M")
            execution_date = datetime.strptime(execution_date, "%H:%M")
            logging.info('starting check for glue and imply only runs')
            logging.info('current exec time is')
            logging.info(execution_date)
            for schedule_time in GLUE_IMPLY_ONLY_APPROX_TIMES:
                if abs(int((schedule_time - execution_date).total_seconds() / 60)) < maximum_offset_minutes:
                    logging.info(f"check for {schedule_time} is ok, checking scheduled run...")
                    if is_scheduled_run(**kwargs):
                        logging.info('its a scheduled run, all good')
                        return True
                    else:
                        logging.info('its not a scheduled run!')
                        return False
                else:
                    logging.info(f"check for {schedule_time} is not ok")
            logging.info("Every check for supposed glue and imply only run failed...")
            return False

        return _set_dag_conf_based_on_schedule_time(**kwargs_approx_times)


    def _get_job_list(self, glue_client):
        jobs = glue_client.list_jobs()
        all_jobs = jobs['JobNames']
        next_job = jobs.get('NextToken', None)

        while next_job:
            jobs = glue_client.list_jobs(NextToken=next_job)
            all_jobs = all_jobs + jobs['JobNames']
            next_job = jobs.get('NextToken', None)
        job_prefix = f'{self.env}-{self.layer}-'
        p = re.compile(f'^{job_prefix}')
        job_list = [s[len(job_prefix):] for s in all_jobs if p.match(s)]
        return job_list

    def trigger_glue_job(self, glue_client, job_name):
        try:
            if self.env is None:
                raise AirflowException('No environment variable exists')
            job_name = f'{self.env}-{self.layer}-{job_name}'
            response = glue_client.start_job_run(JobName=job_name)
            job_run_id = response['JobRunId']
            print(f"Job run {job_run_id} triggered successfully for job {job_name}")
            return job_run_id, job_name
        except Exception as e:
            print(f"Error triggering job {job_name}: {str(e)}")
            return None

    def _python_callable(self, **kwargs):
        glue_client = glue_client_factory(**self.glue_client_params)  # Create a new instance inside the callable
        if self.with_light_run:
            logging.info('light run check is in progress...')
            if self.is_light_run(**kwargs):
                logging.info('light run detected')
                job_list = self.set_light_run_jobs(**kwargs)
            else:
                job_list = self.job_list
        else:
            if self.job_list:
                job_list = self.job_list
            else:
                job_list = self._get_job_list(glue_client)
        job_ids = []
        self.run_number = kwargs['dag_run'].conf.get('current_run_number')
        failed_jobs =[]
        glue_jobs_retry_eligible = job_list # list that contains all jobs eligible for retry
        # triggering jobs
        for job in job_list:
            result = self.trigger_glue_job(glue_client, job)
            if result:
                job_ids.append(result)
            time.sleep(2)
        # as they are triggered we poke glue_client
        # to get the state of the jobs
        # consider task done as all of the jobs leave state in progress
        start_time = time.time()
        fail_flag = False # this flag will indicate if we need to raise error
        num_jobs = len(job_ids)
        logging.info(f'Triggered {len(job_ids)} jobs out of {len(job_list)}')
        if self.layer in ['base', 'extra'] and len(job_ids)!=len(job_list):
            fail_flag = True
        while True:
            logging.info(f'Polling jobs.. running {len(job_ids)}/{num_jobs}')
            if len(job_ids)==0:
                break
            time.sleep(15)
            for idx, batch in enumerate(job_ids):
                time.sleep(0.3)
                job_name = batch[1]
                job_run_id = batch[0]
                hook = GlueJobHook(job_name=job_name)
                job_state = hook.get_job_state(job_name,job_run_id)
                if job_state in ['SUCCEEDED', 'FAILED', 'STOPPED']:
                    logging.info(f'Job {job_name} finished with the status: {job_state}')
                    if job_state in ["FAILED" , "STOPPED"]:
                        fail_flag = True
                        if job_name.split('-')[2] in glue_jobs_retry_eligible:
                            logging.info(f'Retrying job {job_name}...')
                            new_job_id = self.trigger_glue_job(glue_client, job_name.split('-')[2])
                            job_ids.append(new_job_id)
                            increment_metric('airflow_glue_job_fails.increment', self.env, job_name.split('-')[2], additional_tags= [f'run_number:{self.run_number}'])
                            glue_jobs_retry_eligible.remove(job_name.split('-')[2])
                        else:
                            if self.layer in ['final', 'base', 'extra']:
                                failed_jobs.append(job_name)
                            response = glue_client.get_job_runs(JobName=job_name, MaxResults=1)
                            try:
                                error_message = response['JobRuns'][0]
                            except KeyError:
                                logging.info('unable to retrive error message!')
                                error_message = 'ERROR NOT AWAILABLE'
                            logging.critical(f"Job: {job_name} failed details: \n {error_message}")
                            logging.info(f'{job_name} is not eligible for the retry, it has been retried already and failed again')
                    data_to_log = job_name.split('-')[2]
                    increment_metric('airflow_execution_time.increment', self.env, f'glue_{data_to_log}',
                    round(time.time() - start_time), [f'run_number:{self.run_number}'])
                    job_ids.pop(idx)
        increment_metric('airflow_execution_time.increment', self.env, f'flow_{self.task_id}',
                    round(time.time() - start_time), [f'run_number:{self.run_number}'])
        if fail_flag and len(failed_jobs)!=0:
            logging.critical(f'not all jobs finished in layer {self.layer}')
            for failed_job in failed_jobs:
                logging.critical(f'job {failed_job} FAILED!!!')
                increment_metric('airflow_glue_job_fails.increment', self.env, failed_job.split('-')[2], additional_tags= [f'run_number:{self.run_number}'], count=2)


class CustomCrawlersTriggerOperatorNoParallel(PythonOperator):
    """
    This operator triggers a set list of glue crawlers
    and waits for their completion
    """
    def __init__(self, env, glue_client_params, crawler_ids_list, **kwargs):
        self.env = env
        self.glue_client_params = glue_client_params
        self.crawler_ids_list = crawler_ids_list
        super().__init__(**kwargs, python_callable=self._python_callable)

    @staticmethod
    def _get_tables(glue_client, crawlers_names):
        tables = []

        for crawler_name in crawlers_names:
            x = glue_client.get_crawler(Name=crawler_name)
            prefix = x.get("Crawler").get("TablePrefix")
            suffix = (
                x.get("Crawler")
                .get("Targets")
                .get("S3Targets")[0]
                .get("Path")
                .split("/")[-2]
            )
            name = prefix + suffix

            database = (
                x.get("Crawler")
                .get("DatabaseName"))

            tables.append({"name": name, "database": database})
            time.sleep(2)
        return tables

    def _get_crawler_list(self, glue_client):
        crs = glue_client.list_crawlers()
        all_crawlers = crs['CrawlerNames']
        next_crawler = crs.get('NextToken', None)

        while next_crawler:
            crs = glue_client.list_crawlers(NextToken=next_crawler)
            all_crawlers = all_crawlers + crs['CrawlerNames']
            next_crawler = crs.get('NextToken', None)
        p = re.compile(f'^{self.env}-')
        crawlers = [s for s in all_crawlers if p.match(s)]

        return crawlers

    @staticmethod
    def _has_finished(glue_client, crawler_name):
        while True:
            time.sleep(2)
            response_get = glue_client.get_crawler(Name=crawler_name)
            state = response_get["Crawler"]["State"]
            last_crawl_state = response_get["Crawler"]["LastCrawl"]["Status"]
            if state == "READY" and last_crawl_state == "SUCCEEDED":
                # all good, crawler finished and succeeded :)
                logging.info(f"Crawler {crawler_name} successfully run!")
                return True
            elif state == "READY":
                # crawler finished, but didn't succeed
                logging.critical(f"Something wrong with the crawler {crawler_name}: current status: {state}, last run: {last_crawl_state}")
                return False
            elif state in ["RUNNING", "STOPPING"]:
                # crawler is still running
                pass

    def _python_callable(self, **kwargs):
        glue_client = glue_client_factory(**self.glue_client_params)
        if self.crawler_ids_list:
            crawlers_names = [f'{self.env}-athena-{crawler_id}' for crawler_id in self.crawler_ids_list]
        else:
            crawlers_names = self._get_crawler_list(glue_client)

        tables = self._get_tables(glue_client, crawlers_names)
        for table in tables:
            table_name = table["name"]
            table_db = table["database"]
            try:
                glue_client.delete_table(
                    DatabaseName=table_db,
                    Name=table_name,
                )
            except glue_client.exceptions.EntityNotFoundException:
                print(f"The table DB: {table_db} NAME: {table_name} doesn't exist, so no need to delete it")
            except Exception as e:
                raise e
            else:
                print(f"Table DB: {table_db} NAME: {table_name} was successfully deleted")

        time.sleep(10)

        for crawler_name in crawlers_names:
            try:
                logging.info(f'Triggered {crawler_name}')
                glue_client.start_crawler(Name=crawler_name)
            except Exception as e:
                logging.critical(f"Unexpected error in start_crawler for Crawler {crawler_name}: " + e.__str__())

        time.sleep(10)
        for crawler_name in crawlers_names:
            self._has_finished(glue_client, crawler_name)


class MergeMapOutputOperator(PythonOperator):
    """
    This operator tackles problem of using result of one partial python operator
    for another partial python operator

    retrieves returns of one partial python operator and transforms in into the list
    """

    def __init__(self, *args, deduplicate=False, map_output_is_batched=False,
                 batch_size=None, batches_count=None, **kwargs):
        self.deduplicate = deduplicate
        self.map_output_is_batched = map_output_is_batched
        self.batch_size = batch_size
        self.batches_count = batches_count
        super().__init__(*args, python_callable=self._python_callable, **kwargs)

    def _python_callable(self, *map_output):
        dedup_set = set()
        merged = []
        for out in map_output:
            if self.map_output_is_batched:
                flatten_out = []
                for b in out:
                    flatten_out.extend(b)
                out = flatten_out
            if not self.deduplicate:
                merged.extend(out)
            else:
                for val in out:
                    if repr(val) not in dedup_set:
                        merged.append(val)
                    dedup_set.add(repr(val))
        if self.batches_count is not None:
            self.batch_size = max(2, len(merged) // self.batches_count)
        if self.batch_size is not None:
            return [[batch] for batch in iter_batch(merged, self.batch_size)]
        else:
            return merged


class CleanupXcomsOperator(PythonOperator):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, python_callable=self._python_callable, **kwargs)

    @staticmethod
    @provide_session
    def _python_callable(session=None, **kwargs):
        dag_run_id = kwargs['dag_run'].id
        # It will delete all xcom of current dag run.
        # The column `dag_run_id` is indexed
        session.query(XCom).filter(XCom.dag_run_id == dag_run_id).delete()
        # lets also push current dag run duration
        execution_date = kwargs.get('execution_date')
        current_time = datetime.now(pytz.utc).astimezone(execution_date.tzinfo)
        delta_seconds = (current_time - execution_date).total_seconds()
        env_resolver = EnvironmentResolver()
        init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
        logging.info(f'dag runtime: {delta_seconds}')
        flow_id = kwargs['dag'].dag_id
        current_run_number = kwargs['dag_run'].conf.get('current_run_number')
        increment_metric('airflow_execution_time.increment', env_resolver.environment, f'flow_{flow_id}', delta_seconds,
                    additional_tags=[f'run_number:{current_run_number}']
                    )


class CustomTriggerDagRunOperator(TriggerDagRunOperator):
    def __init__(self, env_resolver, **kwargs) -> None:
        self.env_resolver = env_resolver
        # Api of GlueJobOperator requires argument for script_location, but inner logic works fine if it's None
        super().__init__(**kwargs)

    @datadog_timeit('trigger_dag_id')
    def execute(self, context):
        env = self.env_resolver.environment
        if env is None:
            raise Exception('No environment variable exists')
        return super().execute(context)


class CustomTriggerMultipleDagsRunOperator(PythonOperator):
    def __init__(self, *args, dag_ids=[], **kwargs):
        self.dag_ids = dag_ids
        super().__init__(*args, python_callable=self._python_callable, **kwargs)

    def get_dag_ids_to_run(self, kwargs):
        dags_to_execute = kwargs['dag_run'].conf.get('airflow_flows')
        if dags_to_execute is None:
            return self.dag_ids
        else:
            return [
                dag_id for dag_id in self.dag_ids
                if dag_id in dags_to_execute
            ]

    def _python_callable(self, **kwargs):
        current_dag_run_id = kwargs['dag_run'].run_id
        dag_run_ids = {}
        dag_ids_to_run = self.get_dag_ids_to_run(kwargs)
        logging.info(f'dag_ids_to_run {dag_ids_to_run}')
        for dag_id in dag_ids_to_run:
            dag_run_id = f'trigger_from_{current_dag_run_id}_{int(time.time())}'
            dag_run = trigger_dag.trigger_dag(
                dag_id=dag_id,
                run_id=dag_run_id,
                execution_date=kwargs['dag_run'].execution_date,
                conf=kwargs['dag_run'].conf,
            )
            dag_run_ids[dag_id] = dag_run.run_id
        return dag_run_ids


class WaitForDagsRunSensor(PythonSensor):

    finished_states = [
        State.SUCCESS,
        State.FAILED,
        State.SKIPPED,
    ]

    def __init__(self, *args, **kwargs):
        self.env_resolver = EnvironmentResolver()
        self.datadog_attr = 'airflow_ingestion_flows'
        self.start_time = datetime.now()
        super().__init__(*args, python_callable=self._python_callable, **kwargs)

    def send_stats_to_datadog(self, finished_dag_run, **kwargs):
        sent_to_datadog = kwargs['task_instance'].xcom_pull(key='sent_to_datadog',
                                                            task_ids=kwargs['task_instance'].task_id)
        if sent_to_datadog is None:
            sent_to_datadog = []
        if finished_dag_run.id not in sent_to_datadog:
            if finished_dag_run.end_date and finished_dag_run.start_date:
                duration = (finished_dag_run.end_date - finished_dag_run.start_date).total_seconds()
            else:
                duration = 0
            datadog_value_to_send = 'airflow_' + finished_dag_run.dag_id
            logging.info(f"sending to datadog {datadog_value_to_send} duration={duration}")
            increment_metric('airflow_execution_time.increment', self.env_resolver.environment,
                             datadog_value_to_send, duration)
            sent_to_datadog.append(finished_dag_run.id)
            kwargs['task_instance'].xcom_push(key='sent_to_datadog', value=sent_to_datadog)
    
    def _python_callable(self, dag_run_ids, **kwargs):
        logging.info(f"dag_run_ids {dag_run_ids}")
        not_finished = []
        for dag_id, run_id in dag_run_ids.items():
            dag_runs = DagRun.find(dag_id=dag_id, run_id=run_id)
            assert len(dag_runs) == 1, "Cannot find dag run or multiple results"
            dag_run = dag_runs[0]
            logging.info(f"{dag_id} is in state {dag_run.state}")
            if dag_run.state not in self.finished_states:
                not_finished.append(dag_id)
            else:
                self.send_stats_to_datadog(dag_run, **kwargs)
        for dag_id in not_finished:
            logging.info(f"Waiting for {dag_id}")
        if len(not_finished) == 0:
            self.end_time = datetime.now()
            self.exec_time = self.end_time - self.start_time
            increment_metric('airflow_execution_time.increment', self.env_resolver.environment, self.datadog_attr, int(self.exec_time.total_seconds()))
            logging.info(f"sending to datadog {self.datadog_attr} duration={self.exec_time.total_seconds()}")
        return len(not_finished) == 0
        
        
