import time

from datadog import initialize, statsd


def init_datadog(host: str, port: int):
    """Initialize datadog"""
    initialize(
        statsd_host=host,
        statsd_port=port
    )


def datadog_timeit(flow_id_attr):
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            flow_id = getattr(self, flow_id_attr)
            start_time = time.time()
            result = func(self, *args, **kwargs)
            execution_time = int(time.time() - start_time)
            increment_metric('airflow_execution_time.increment', self.env_resolver.environment, flow_id, execution_time)
            return result

        return wrapper

    return decorator


def increment_metric(metric: str, env: str, flow_name: str, count: int = 1, additional_tags: list = None):
    """Send incremental metric to datadog"""
    if additional_tags is None:
        additional_tags = []
    statsd.increment(metric, count, tags=[f"environment:{env}", f"flow:{flow_name}"] + additional_tags)
