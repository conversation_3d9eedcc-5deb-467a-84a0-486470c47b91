import logging

import requests


def generate_google_access_token(client_id, client_secret, refresh_token):
    response = requests.post('https://accounts.google.com/o/oauth2/token', json={'client_id': client_id,
                                                                                 'client_secret': client_secret,
                                                                                 'refresh_token': refresh_token,
                                                                                 'grant_type': 'refresh_token'})
    if 'access_token' not in response.json():
        logging.error(f"Access token requested but not received. Response `{response.text}`")
    access_token = response.json()['access_token']
    logging.info('New access token requested')
    return access_token
