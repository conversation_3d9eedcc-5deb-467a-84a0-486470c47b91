class QuotaIsReached(Exception):
    def __init__(self, flow_name):
        super().__init__(f'Quota is reached for {flow_name} flow')


class AuthError(Exception):
    def __init__(self, flow_name):
        super().__init__(f'Invalid Auth credentials found in {flow_name} flow')


class MintFlowFailed(Exception):
    # Generic exception. Please use this one as default
    def __init__(self, flow_name):
        super().__init__(f'{flow_name} flow failed')


class MintAuthFailed(MintFlowFailed):
    # Exception to indicate that authentication to external API has failed
    pass


class NotPaidSearchAccount(Exception):
    def __init__(self, flow_name):
        super().__init__(f'Not Paid Search account found for {flow_name}. Skipping')


class NotFloodLightAccount(Exception):
    def __init__(self, flow_name):
        super().__init__(f'Not FloodLight account found for {flow_name}. Skipping')


class OmniFlowFailed(Exception):
    # Generic exception. Please use this one as default
    pass


class SnowflakeFailed(Exception):
    # Generic exception. Please use this one as default
    pass
