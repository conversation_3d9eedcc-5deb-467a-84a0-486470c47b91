import base64
import logging
from os import environ as env, getenv
import os
from myn_airflow_lib.common_requests import generate_google_access_token


class EnvironmentResolver:

    @property
    def common_reports_email(self):
        common_reports_email = getenv("COMMON_REPORTS_EMAIL", None)
        logging.info(f'COMMON_REPORTS_EMAIL = {common_reports_email}')
        return common_reports_email

    @property
    def common_reports_password(self):
        common_reports_password = getenv("COMMON_REPORTS_PASSWORD", None)
        logging.info(f"COMMON_REPORTS_PASSWORD = {'****' if common_reports_password else None}")
        return common_reports_password

    @property
    def reports_tenant_id(self):
        tenant_id = getenv('REPORTS_TENANT_ID')
        return tenant_id
    
    @property
    def reports_client_id(self):
        client_id = getenv("REPORTS_CLIENT_ID")
        return client_id

    @property
    def reports_client_secret(self):
        client_secret = getenv("REPORTS_CLIENT_SECRET")
        return client_secret

    @property
    def ga_private_key(self):
        return base64.b64decode(
            os.environ["GOOGLE_ANALYTICS_BASE64_ENCODED_PRIVATE_KEY"])

    @property
    def gcm_excluded_clients(self):
        return env['GCM2_EXCLUDED_CLIENTS']

    @property
    def gcm_paid_search_wait_sleep(self):
        return int(env['GCM2_WAIT_SLEEP'])

    @property
    def ga_private_key_id(self):
        return os.environ["GOOGLE_ANALYTICS_KEY_ID"]

    @property
    def ga_email_adress(self):
        return os.environ["GOOGLE_ANALYTICS_CLIENT_EMAIL"]

    @property
    def ga_audience(self):
        return os.environ["GOOGLE_ANALYTICS_AUDIENCE"]

    @property
    def ga_scope(self):
        return os.environ["GOOGLE_ANALYTICS_SCOPE"]

    @property
    def ga_client_id(self):
        return os.environ["GOOGLE_ANALYTICS_CLIENT_ID"]

    @property
    def ga_client_secret(self):
        return os.environ["GOOGLE_ANALYTICS_CLIENT_SECRET"]

    @property
    def linkedin_reports_email(self):
        return env['LINKEDIN_REPORTS_EMAIL']

    @property
    def linkedin_reports_password(self):
        return env['LINKEDIN_REPORTS_PASSWORD']

    @property
    def gcm2_wait_sleep(self):
        return int(env["GCM2_WAIT_SLEEP"])

    @property
    def environment(self):
        environment = env.get("ENVIRONMENT")
        logging.info(f'ENVIRONMENT = {environment}')
        return environment

    @property
    def gcm_api_version(self):
        gcm_api_version = getenv("GCM_VERSION", 'v3.5')
        logging.info(f'GCM_VERSION = {gcm_api_version}')
        return gcm_api_version

    @property
    def wait_sleep_historical(self):
        wait_sleep_historical = int(getenv("WAIT_SLEEP_HISTORICAL", '600'))
        logging.info(f'WAIT_SLEEP_HISTORICAL = {wait_sleep_historical}')
        return wait_sleep_historical

    @property
    def gmail_reports_email(self):
        gmail_reports_email = getenv("GMAIL_REPORTS_EMAIL", None)
        logging.info(f'GMAIL_REPORTS_EMAIL = {gmail_reports_email}')
        return gmail_reports_email

    @property
    def gmail_reports_password(self):
        gmail_reports_password = getenv("GMAIL_REPORTS_PASSWORD", None)
        logging.info(f"GMAIL_REPORTS_PASSWORD = {'****' if gmail_reports_password else None}")
        return gmail_reports_password

    @property
    def google_client_id(self):
        client_id = env["GOOGLE_CLIENT_ID"]
        return client_id

    @property
    def google_refresh_token(self):
        refresh_token = env["GOOGLE_REFRESH_TOKEN"]
        return refresh_token

    @property
    def google_client_secret_id(self):
        secret_id = env["GOOGLE_CLIENT_SECRET_ID"]
        return secret_id

    @property
    def dv360_google_client_id(self):
        client_id = env["DV360_GOOGLE_CLIENT_ID"]
        return client_id

    @property
    def dv360_google_refresh_token(self):
        refresh_token = env["DV360_GOOGLE_REFRESH_TOKEN"]
        return refresh_token

    @property
    def dv360_google_client_secret_id(self):
        secret_id = env["DV360_GOOGLE_CLIENT_SECRET_ID"]
        return secret_id

    @property
    def gcm_dv360_google_client_id(self):
        client_id = env["GCM_DV360_GOOGLE_CLIENT_ID"]
        return client_id

    @property
    def gcm_dv360_google_refresh_token(self):
        refresh_token = env["GCM_DV360_GOOGLE_REFRESH_TOKEN"]
        return refresh_token

    @property
    def gcm_dv360_google_client_secret_id(self):
        secret_id = env["GCM_DV360_GOOGLE_CLIENT_SECRET_ID"]
        return secret_id

    @property
    def youtube_reports_email(self):
        youtube_reports_email = getenv("YOUTUBE_REPORTS_EMAIL", None)
        logging.info(f'YOUTUBE_REPORTS_EMAIL = {youtube_reports_email}')
        return youtube_reports_email

    @property
    def youtube_reports_password(self):
        youtube_reports_password = getenv("YOUTUBE_REPORTS_PASSWORD", None)
        logging.info(f"YOUTUBE_REPORTS_PASSWORD = {'****' if youtube_reports_password else None}")
        return youtube_reports_password

    @property
    def criteo_reports_email(self):
        criteo_reports_email = getenv("CRITEO_REPORTS_EMAIL", None)
        logging.info(f'CRITEO_REPORTS_EMAIL = {criteo_reports_email}')
        return criteo_reports_email

    @property
    def criteo_reports_password(self):
        criteo_reports_password = getenv("CRITEO_REPORTS_PASSWORD", None)
        logging.info(f"CRITEO_REPORTS_PASSWORD = {'****' if criteo_reports_password else None}")
        return criteo_reports_password

    @property
    def quantcast_reports_email(self):
        quantcast_reports_email = getenv("QUANTCAST_REPORTS_EMAIL", None)
        logging.info(f'QUANTCAST_REPORTS_EMAIL = {quantcast_reports_email}')
        return quantcast_reports_email

    @property
    def quantcast_reports_password(self):
        quantcast_reports_password = getenv("QUANTCAST_REPORTS_PASSWORD", None)
        logging.info(f"QUANTCAST_REPORTS_PASSWORD = {'****' if quantcast_reports_password else None}")
        return quantcast_reports_password

    @property
    def adwords_closed_accounts(self):
        return eval(env.get('ADWORDS_CLOSED_ACCOUNTS', "[]"))

    @property
    def gcm_paid_search_google_client_id(self):
        client_id = env["GCM_PAID_SEARCH_GOOGLE_CLIENT_ID"]
        return client_id

    @property
    def gcm_paid_search_google_refresh_token(self):
        refresh_token = env["GCM_PAID_SEARCH_GOOGLE_REFRESH_TOKEN"]
        return refresh_token

    @property
    def gcm_paid_search_google_client_secret_id(self):
        secret_id = env["GCM_PAID_SEARCH_GOOGLE_CLIENT_SECRET_ID"]
        return secret_id

    @property
    def druid_token(self):
        return env['DRUID_TOKEN']

    @property
    def druid_user(self):
        return env['DRUID_USER']

    @property
    def druid_password(self):
        return env['DRUID_PASSWORD']

    @property
    def druid_host(self):
        return env['DRUID_HOST']

    @property
    def druid_port(self):
        return env['DRUID_PORT']

    @property
    def druid_flow_host(self):
        return env['DRUID_FLOW_HOST']

    @property
    def microsoft_ads_client_id(self):
        return env["BING_CLIENT_ID"]

    @property
    def microsoft_ads_client_secret(self):
        return env["BING_CLIENT_SECRET"]

    @property
    def microsoft_ads_developer_token(self):
        return env["BING_DEVELOPER_TOKEN"]

    @property
    def datadog_host(self):
        """Host var is None while init airflow but present inside pod"""
        host = env.get("DATADOG_STATSD_HOST")
        return host

    @property
    def datadog_port(self):
        """Port var is None while init airflow but present inside pod"""
        port = env.get("DATADOG_STATSD_PORT")
        return port

    @property
    def s3_datalake_bucket(self):
        bucket = getenv('S3_DATALAKE_BUCKET', None)
        return bucket

    @property
    def current_environment(self):
        env = getenv('ENVIRONMENT', None)
        return env

    @property
    def mailchimp_token(self):
        env = getenv('MAILCHIMP_KEY', None)
        return env

    @property
    def slack_token(self):
        token = env.get('SLACK_TOKEN')
        return token

    @property
    def msteams_monitoring_webhook(self):
        monitoring_webhook = env.get('MSTEAMS_MONITORING_WEBHOOK')
        return monitoring_webhook

    @property
    def theoutplay_api_auth_endpoint(self):
        api_auth_endpoint = env.get('TOP_AUTHORIZATION_URL')
        logging.info(f"THEOUTPLAY_API_AUTH_ENDPOINT = {api_auth_endpoint}")
        return api_auth_endpoint

    @property
    def theoutplay_api_reports_endpoint(self):
        api_report_endpoint = env.get('TOP_BASE_URL')
        logging.info(f"THEOUTPLAY_API_REPORT_ENDPOINT = {api_report_endpoint}")
        return api_report_endpoint

    @property
    def aws_acces_key_id(self):
        env = getenv('AWS_ACCESS_KEY_ID', None)
        return env

    @property
    def aws_secret_acces_key_id(self):
        env = getenv('AWS_SECRET_ACCESS_KEY', None)
        return env

    @property
    def aws_sess_token(self):
        env = getenv('AWS_SESSION_TOKEN', None)
        return env

    # snowflake
    @property
    def snowflake_conn_params(self):
        env = {
            'account': getenv('SNOWFLAKE_ACCOUNT', None),
            'user': getenv('SNOWFLAKE_USER', None),
            'password': getenv('SNOWFLAKE_PASSWORD', None),
        }
        return env

    @property
    def snowflake_write_warehouse(self):
        snowflake_warehouse = env.get("SNOWFLAKE_WRITE_WAREHOUSE", f'{env.get("ENVIRONMENT")}_WAREHOUSE')
        logging.info(f"SNOWFLAKE_WRITE_WAREHOUSE = {snowflake_warehouse}")
        return snowflake_warehouse

    @property
    def snowflake_read_warehouse(self):
        snowflake_warehouse = env.get("SNOWFLAKE_READ_WAREHOUSE", f'{env.get("ENVIRONMENT")}_READ_WAREHOUSE')
        logging.info(f"SNOWFLAKE_READ_WAREHOUSE = {snowflake_warehouse}")
        return snowflake_warehouse

    @property
    def staging_db_name(self):
        staging_db_name = f"{self.environment.upper()}_STAGE"
        logging.info(f"Staging DB Name = {staging_db_name}")
        return staging_db_name

    @property
    def kafka_servers(self):
        kafka_servers = env.get("KAFKA_SERVERS")
        return kafka_servers


class GlobalsResolver:
    _GOOGLE_ACCESS_TOKEN = None
    _env_resolver = EnvironmentResolver()

    @property
    def google_access_token(self):
        if self._GOOGLE_ACCESS_TOKEN is None:
            self.refresh_google_access_token('GCM2')
        return self._GOOGLE_ACCESS_TOKEN

    @property
    def dv360_google_access_token(self):
        if self._GOOGLE_ACCESS_TOKEN is None:
            self.refresh_google_access_token('DV360')
        return self._GOOGLE_ACCESS_TOKEN

    @property
    def gcm_paid_search_google_access_token(self):
        if self._GOOGLE_ACCESS_TOKEN is None:
            self.refresh_google_access_token('GCM_PAID_SEARCH')
        return self._GOOGLE_ACCESS_TOKEN

    @property
    def gcm_dv360_google_access_token(self):
        if self._GOOGLE_ACCESS_TOKEN is None:
            self.refresh_google_access_token('gcm-dv360')
        return self._GOOGLE_ACCESS_TOKEN

    def refresh_google_access_token(self, flow_name):
        creds = self._google_creds_map[flow_name]()
        self._GOOGLE_ACCESS_TOKEN = generate_google_access_token(**creds)

    @property
    def _google_creds_map(self):
        return {'GCM2': self._google_creds_gcm2,
                'DV360': self._google_creds_dv360,
                'GCM_PAID_SEARCH': self._google_creds_gcm_paid_search,
                'gcm-dv360': self._google_creds_gcm2,
                'GCM_FLOODLIGHT': self._google_creds_gcm2}

    def _google_creds_gcm2(self):
        return {'client_id': self._env_resolver.google_client_id,
                'client_secret': self._env_resolver.google_client_secret_id,
                'refresh_token': self._env_resolver.google_refresh_token}

    def _google_creds_dv360(self):
        return {'client_id': self._env_resolver.dv360_google_client_id,
                'client_secret': self._env_resolver.dv360_google_client_secret_id,
                'refresh_token': self._env_resolver.dv360_google_refresh_token}

    def _google_creds_gcm_paid_search(self):
        return {'client_id': self._env_resolver.google_client_id,
                'client_secret': self._env_resolver.google_client_secret_id,
                'refresh_token': self._env_resolver.google_refresh_token}
