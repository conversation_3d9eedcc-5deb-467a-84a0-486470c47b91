from dataclasses import dataclass
import logging
import traceback
from typing import List, Optional, Union
import concurrent
import snowflake.connector
from myn_airflow_lib.resolvers import EnvironmentResolver
from airflow.providers.snowflake.hooks.snowflake import SnowflakeHook

resolver = EnvironmentResolver()

datamart_db = f'{resolver.environment}_datamart'
write_wh = resolver.snowflake_write_warehouse
snowflake_hook = SnowflakeHook(snowflake_conn_id="snowflake_report",
                               warehouse=write_wh,
                               database=datamart_db)


def create_table_sql(schema, name):
    create_table_sql = f"CREATE OR REPLACE TABLE {name} (\n"
    columns = []
    for column in schema:
        column_definition = f"    {column[0]} {column[1]}"
        columns.append(column_definition)

    # Join the column definitions into the CREATE TABLE SQL
    create_table_sql += ",\n".join(columns)

    # Close the parentheses and finish the statement
    create_table_sql += "\n);"
    return create_table_sql


@dataclass
class ControllerRequest:
    request: dict
    stage_name: Optional[str] = None
    schema_name: str = "druid_sources"

    @property
    def s3_bucket(self) -> str:
        return self.request['spec']['ioConfig']['inputSource']['prefixes'][0]

    @property
    def name(self) -> str:
        name = self.request['spec']['dataSchema']['dataSource']
        if 'main' in name:
            if resolver.environment.lower() == 'prod':
                return "MAIN"
            return f'MAIN_{resolver.environment.upper()}'
        return self.request['spec']['dataSchema']['dataSource']

    @property
    def temp_name(self):
        return f'temp_{self.name}'

    @property
    def time_col(self) -> str:
        return self.request['spec']['dataSchema']['timestampSpec']['column']

    @property
    def columns(self) -> List[str]:
        return self.request['spec']['dataSchema']['dimensionsSpec']['dimensions']

    def replace_temp_table_with_real(self):
        temp_name = self.temp_name
        name = self.name
        return f"""
        DROP TABLE IF EXISTS {name};
        ALTER TABLE {temp_name} rename to {name};
        """

    def generate_sql_to_keep_only_valid_columns(self, table_name: str) -> str:
        valid_columns = ['__time', 'date']
        for col in self.columns:
            if isinstance(col, str):
                valid_columns.append(col)
            elif isinstance(col, dict) and "name" in col:
                valid_columns.append(col["name"])

        sql_query = f"""
        CREATE OR REPLACE TABLE {table_name} AS
        SELECT {', '.join(valid_columns)}
        FROM {table_name};
        """.strip()
        logging.info(f'for table {table_name} we will keep cols = {valid_columns}')
        return sql_query

    def construct_copy_sql(self, database, schema, stage) -> str:
        # SQL to copy data into Snowflake table
        copy_sql = f"""
        COPY INTO {database}.{schema}.{self.temp_name}
        FROM @{stage}
        PATTERN = '.*\.parquet'
        MATCH_BY_COLUMN_NAME = CASE_INSENSITIVE
        FILE_FORMAT = (TYPE = 'PARQUET');
        """
        return copy_sql

    def create_metadata_sqls(self):
        schema = f"CREATE SCHEMA IF NOT EXISTS {self.schema_name};"
        format_ = f"""CREATE FILE FORMAT IF NOT EXISTS {self.schema_name}.druid_parquet_format  
                   TYPE = 'PARQUET';"""
        return [schema, format_]

    def stage_create_sql(self, stage_name) -> str:
        # storage
        self.stage_name = stage_name
        env = resolver.environment
        storage_sql = f"""CREATE OR REPLACE STAGE {self.schema_name}.{stage_name}
            STORAGE_INTEGRATION = "s3-myn-{env}-datalake-{env}-snowflake"
            URL = '{self.s3_bucket}'
            FILE_FORMAT = (TYPE = PARQUET);
        """
        return storage_sql

    def get_schema_sql(self) -> str:
        assert self.stage_name is not None  # should be called only after stage_create_sql
        schema_sql = f"""
            SELECT * 
            FROM TABLE(
                INFER_SCHEMA(
                    LOCATION => '@{self.stage_name}/',
                    FILE_FORMAT => 'druid_parquet_format'
                )
            );"""
        return schema_sql

    def generate_time_column_alter_stmnt(self):
        queries = [
            f"ALTER TABLE {self.temp_name} ADD COLUMN __time_tmp TIMESTAMP;",
            f"UPDATE {self.temp_name} SET __time_tmp = {self.time_col}::TIMESTAMP;",
            f"ALTER TABLE {self.temp_name} RENAME COLUMN __time_tmp TO __time;"
        ]
        return queries

    def generate_cast_sql(self):
        """
        Generate SQL to update specific columns to the specified types in a Snowflake table.
        returns Generated SQL query for updating column types.
        """
        alter_statements = []

        types_compatability_map = {
            'long': 'bigint'
        }

        for column in self.columns:
            if column == '__time':
                continue
            if isinstance(column, dict):
                column_name = column.get("name")
                column_type = column.get("type")
                if column_name and column_type:
                    if column_type in types_compatability_map.keys():
                        column_type = types_compatability_map[column_type]
                    alter_statements.append([
                        f"ALTER TABLE {self.name} ADD COLUMN {column_name}_tmp {column_type}",
                        f"UPDATE {self.name} SET {column_name}_tmp = CAST({column_name} AS {column_type})",
                        f"ALTER TABLE {self.name} DROP COLUMN {column_name}",
                        f"ALTER TABLE {self.name} RENAME COLUMN {column_name}_tmp TO {column_name}"
                    ])
            elif isinstance(column, str):
                # Default to VARCHAR(1000)
                alter_statements.append([
                    f"ALTER TABLE {self.name} ADD COLUMN {column}_tmp VARCHAR(1000)",
                    f"UPDATE {self.name} SET {column}_tmp = CAST({column} AS VARCHAR(1000))",
                    f"ALTER TABLE {self.name} DROP COLUMN {column}",
                    f"ALTER TABLE {self.name} RENAME COLUMN {column}_tmp TO {column}"
                ])
        return alter_statements


class DatalakeS3Path():

    def __init__(self, layer_and_name: str, is_partitioned=False) -> None:
        splitted_data = layer_and_name.split('/')
        self.table_name = splitted_data[1] if len(splitted_data) == 2 else splitted_data[-1]

        self.layer = splitted_data[0]
        self.path = layer_and_name
        self.is_partitioned = is_partitioned
        if len(splitted_data) == 3:
            self.is_partitioned = True


@dataclass
class DatalakeSource:
    s3_path: DatalakeS3Path
    schema_name: str = 'datalake_sources'
    rename_dict: Optional[dict] = None

    @property
    def s3_bucket(self) -> str:
        if self.s3_path.is_partitioned:
            return f's3://{resolver.s3_datalake_bucket}/{self.s3_path.path}/'
        return f's3://{resolver.s3_datalake_bucket}/{self.s3_path.path}/main/'

    @property
    def name(self) -> str:
        mapped_name = self.rename_dict.get(self.s3_path.table_name)
        return mapped_name if mapped_name else self.s3_path.table_name

    @property
    def temp_name(self):
        return f'temp_{self.name}'

    def replace_temp_table_with_real(self):
        temp_name = self.temp_name
        name = self.name
        return f"""
        DROP TABLE IF EXISTS {name};
        ALTER TABLE {temp_name} rename to {name};
        """

    def construct_copy_sql(self, database, schema, stage) -> str:
        # SQL to copy data into Snowflake table
        copy_sql = f"""
        COPY INTO {database}.{schema}.{self.temp_name}
        FROM @{stage}
        PATTERN = '.*\.parquet'
        MATCH_BY_COLUMN_NAME = CASE_INSENSITIVE
        FILE_FORMAT = (TYPE = 'PARQUET');
        """
        return copy_sql

    def create_metadata_sqls(self):
        schema = f"CREATE SCHEMA IF NOT EXISTS {self.schema_name};"
        format_ = f"""CREATE FILE FORMAT IF NOT EXISTS {self.schema_name}.druid_parquet_format  
                   TYPE = 'PARQUET';"""
        return [schema, format_]

    def stage_create_sql(self, stage_name) -> str:
        # storage
        self.stage_name = stage_name
        logging.info(f'part: {self.s3_path.is_partitioned}')
        env = resolver.environment
        storage_sql = f"""CREATE OR REPLACE STAGE {self.schema_name}.{stage_name}
            STORAGE_INTEGRATION = "s3-myn-{env}-datalake-{env}-snowflake"
            URL = '{self.s3_bucket}'
            FILE_FORMAT = (TYPE = PARQUET);
        """
        return storage_sql

    def get_schema_sql(self) -> str:
        assert self.stage_name is not None  # should be called only after stage_create_sql
        schema_sql = f"""
            SELECT * 
            FROM TABLE(
                INFER_SCHEMA(
                    LOCATION => '@{self.stage_name}/',
                    FILE_FORMAT => 'druid_parquet_format'
                )
            );"""
        return schema_sql


class SnowflakeSourceProcessor():
    def __init__(self, schema):

        self.schema = schema

    def snowflake_exec(self, sql_str: str, enforce_schema=True):
        # add schema to conn
        if enforce_schema:
            sql_str = f"USE SCHEMA {self.schema}; " + sql_str
        logging.info('trying to run...')
        logging.info(sql_str)
        try:
            result = snowflake_hook.get_records(sql_str)
            logging.info(result)
            return result
        except Exception:
            logging.error(f"Error executing query: {sql_str}")
            raise

    def begin(self):
        snowflake_hook.run('BEGIN;')

    def commit(self):
        snowflake_hook.run('COMMIT;')

    def rollback(self):
        snowflake_hook.run("ROLLBACK;")

    def process_druid_source_based_on_controller_request(self, req: ControllerRequest):
        stage_name = 'data_source_' + req.name
        storage_sql = req.stage_create_sql(
            stage_name
        )

        for metadata_sql in req.create_metadata_sqls():
            self.snowflake_exec(metadata_sql, enforce_schema=False)

        logging.info('Creating stage...')
        self.snowflake_exec(storage_sql)
        logging.info('putting files into stage...')
        #self.snowflake_exec(f"PUT '{req.s3_bucket}' @{stage_name};")

        schema_sql = req.get_schema_sql()
        logging.info('Preparing to get schema')
        schema = self.snowflake_exec(schema_sql)
        logging.info(schema)
        table_sql = create_table_sql(schema, req.temp_name)
        logging.info(table_sql)
        env = resolver.environment
        self.snowflake_exec(table_sql)
        self.snowflake_exec(req.construct_copy_sql(
            f'{env}_DATAMART',
            self.schema,
            stage_name
        ))

        # add time columns
        time_col_stnmts = req.generate_time_column_alter_stmnt()

        self.begin()
        for query in time_col_stnmts:
            self.snowflake_exec(query)
        self.commit()

        logging.info('keep only the columns that are needed')
        column_filter_sql = req.generate_sql_to_keep_only_valid_columns(req.temp_name)
        self.snowflake_exec(column_filter_sql)

        table_replace_sql = req.replace_temp_table_with_real()
        self.snowflake_exec(table_replace_sql)

    def process_raw_parquet_from_s3(self, req: DatalakeSource):
        stage_name = 'data_source_' + req.name
        storage_sql = req.stage_create_sql(
            stage_name
        )

        for metadata_sql in req.create_metadata_sqls():
            self.snowflake_exec(metadata_sql, enforce_schema=False)

        self.snowflake_exec(storage_sql)

        schema_sql = req.get_schema_sql()
        schema = self.snowflake_exec(schema_sql)
        table_sql = create_table_sql(schema, req.temp_name)
        env = resolver.environment
        self.snowflake_exec(table_sql)
        self.snowflake_exec(req.construct_copy_sql(
            f'{env}_DATAMART',
            self.schema,
            stage_name
        ))

        table_replace_sql = req.replace_temp_table_with_real()
        self.snowflake_exec(table_replace_sql)

    def process_datasource(self, req: Union[ControllerRequest, DatalakeSource]):
        """
        takes controller request and performs every step to copy data to snowflake
        Has additional functionality that depends if we are ingesting the druid source or the raw data from s3
        """
        if isinstance(req, ControllerRequest):
            self.process_druid_source_based_on_controller_request(req)
        else:
            self.process_raw_parquet_from_s3(req)
