import json
import logging
import re
from typing import Any, List
import datetime
from datetime import timedelta
import pandas as pd
import calendar
from dataclasses import dataclass
from airflow.providers.postgres.hooks.postgres import PostgresHook
import io
import base64
import boto3
from io import BytesIO
from myn_airflow_lib.datadog import increment_metric, init_datadog
from myn_airflow_lib.resolvers import EnvironmentResolver
import requests
from myn_airflow_lib.constants import DEFAULT_TIMEZONE_GROUP, TIMEZON_INGESTION_MAP, TIMEZONE_PREFIX_MAP
from botocore.config import Config
from sqlalchemy import func
from airflow.models.taskinstance import TaskInstance
from airflow.utils.state import State
from airflow import settings

def get_main_dags() -> List[str]:
    """Add all main tasks here"""
    return [
        'Adform',
        'Adwords',
        'APPSFLYER', 'DV360', 'facebook_v2', 'CM360',
        'MICROSOFT_ADS', 'ZEMANTA', 'OFFLINE_TV',
        'GOOGLE_ANALYTICS_V4', 'Linkedin', 'TikTok', 'Amazon', 'Xandr'
    ]

def failure_callback(context):
    env_resolver = EnvironmentResolver()
    init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
    task_id = context['task_instance'].task_id
    dag_id = context['task_instance'].dag_id
    env = env_resolver.environment
    increment_metric(
                'airflow_task_fail.increment', env, f'{dag_id}',
                additional_tags=[f"dag_id:{dag_id}", f"task_id:{task_id}"])


def load_static_template(path):
    with open(path) as file:
        template_file = json.load(file)
    return template_file


def iter_batch(iterable, n=1):
    len_iterable = len(iterable)
    for ndx in range(0, len_iterable, n):
        yield iterable[ndx:min(ndx + n, len_iterable)]


def is_first_saturday() -> bool:
    """
    Checks if today is the first saturday of the month
    """
    c = calendar.Calendar(firstweekday=calendar.SUNDAY)
    monthcal = c.monthdatescalendar(datetime.datetime.today().year, datetime.datetime.today().month)
    
    sats = [day for week in monthcal[0:2] for day in week if
                day.weekday() == calendar.SATURDAY and day.month == datetime.datetime.today().month][0]
    logging.info(f'result for check for saturday: {datetime.date.today()==sats}')
    return datetime.date.today() == sats

def get_date_to_execute_on(dates_obj, date_format="%Y%m%d", sanity_period = 'SANITY_PULL', **kwargs):
    """
    this function handles sanity Pulls functionality
    args:
        dates_obj - dates dict from set_execution_dates func
        **kwargs - dag context kwargs
    function checks if its supposed to be sanity pull and returns
    45 days ago object if so, otherwise returns 7 days ago
    """
    result = (
        dates_obj[sanity_period].strftime(date_format) 
            if 
                kwargs['dag_run'].conf.get('sanity_pull') or is_first_saturday()
            else 
                dates_obj['SEVEN_DAYS_AGO'].strftime(date_format))
    
    logging.info(f'date_from for the scheduled requests is {result}')
    
    return result


def set_execution_dates(obj):
    """
    Decorator to handle execution dates
    If code reqires time constants, they have to be implemented using this decorator
    usage:

    dates = {}

    @set_execution_dates(dates)
    def some_func(arg1,arg2,**kwargs):
        ....

    note: decorator should only decorate python callables for the tasks,
    decorated functions have to have **kwargs argument
    """

    def _set_execution_dates(task):
        def wrapper(*args, **kwargs):
            execution_date = kwargs.get('execution_date')
            #dag_run = kwargs['dag_run']
            obj['TODAY'] = datetime.date.today()
            obj['TOMORROW'] = datetime.date.today() + datetime.timedelta(days=1)
            obj['YESTERDAY'] = obj['TODAY'] - datetime.timedelta(days=1)
            obj['SEVEN_DAYS_AGO'] = obj['TODAY'] - datetime.timedelta(days=7)
            obj['YEAR_AGO'] = obj['TODAY'] - datetime.timedelta(days=365)
            obj['TODAY_FORMAT'] = obj['TODAY'].strftime("%Y%m%d")
            obj['YESTERDAY_FORMAT'] = obj['YESTERDAY'].strftime("%Y%m%d")
            obj['SEVEN_DAYS_AGO_FORMAT'] = obj['SEVEN_DAYS_AGO'].strftime("%Y%m%d")
            obj['YEAR_AGO_FORMAT'] = obj['YEAR_AGO'].strftime("%Y%m%d")
            obj['SANITY_PULL'] = obj['TODAY'] - datetime.timedelta(days=45)
            obj['SANITY_PULL_FORMAT'] = obj['SANITY_PULL'].strftime("%Y%m%d")
            obj['SANITY_PULL_30_DAYS'] = obj['TODAY'] - datetime.timedelta(days=30)
            obj['SANITY_PULL_30_DAYS_FORMAT'] = obj['SANITY_PULL_30_DAYS'].strftime("%Y%m%d")
            obj['TWO_YEARS_AGO'] = obj['TODAY'] - datetime.timedelta(days=365*2)
            obj['SIX_MINTHS_AGO'] = obj['TODAY'] - datetime.timedelta(days=round(365/2))
            obj['ONE_AND_A_HALF_YEAR_AGO'] = obj['TODAY'] - datetime.timedelta(days=round(365*1.5))
            obj['TWO_YEARS_AGO_FORMAT'] = obj['TWO_YEARS_AGO'].strftime("%Y%m%d")
            obj['89_DAYS_AGO'] = obj['TODAY'] - datetime.timedelta(days=89)
            obj['89_DAYS_AGO_FORMAT'] = obj['89_DAYS_AGO'].strftime("%Y%m%d")
            logging.info(
                f"execution_date = {execution_date}"
                f" TODAY = {obj['TODAY_FORMAT']}"
                f" YESTERDAY = {obj['YESTERDAY_FORMAT']}"
            )
            return task(*args, **kwargs)

        return wrapper

    return _set_execution_dates


def set_config(state):
    """
    Decorator to pull task config from **kwargs and put it in global var
    usage:

    state = {}

    @set_config(state)
    def some_func(arg1,arg2,**kwargs):
        ....

    note: decorator should only decorate python callables for the tasks,
    decorated functions have to have **kwargs argument
    """
    def _set_config(task):
        def wrapper(*args, **kwargs):
            state['task_args'] = args
            state['task_kwargs'] = kwargs
            state['dag_run'] = kwargs['dag_run']
            return task(*args, **kwargs)
        return wrapper
    return _set_config


def get_historical_date_from(state, default):
    dag_run = state.get('dag_run')
    assert dag_run is not None, ("Function `get_historical_date_from` can only be used"
                                 " when running Airflow task is decorated with `set_config`")
    param_key = "historical_date_from"
    if param_key not in dag_run.conf:
        return default
    param_value = dag_run.conf[param_key]
    try:
        dt = pd.to_datetime(param_value, infer_datetime_format=True)
    except Exception as e:
        logging.error(f"Exception when parsing input parameter"
                      f" `historical_date_from` `{param_value}`. {e}."
                      " Possibly invalid date format. Proper date format e.g. `2021-09-21`")
        raise
    return dt


def enable_hist_log_check_based_on_conf(state):
    """
    If dag config contains parameter that forces historical repull, always returns False.
    Otherwise, it calls decorated function, which should be `does_hist_log_exists` function
    usage:

    @enable_hist_log_check_based_on_conf(state)
    def does_hist_log_exists(...):
        ....
    """
    def _enable_hist_log_check_based_on_conf(does_hist_log_exist):
        def wrapper(*args, **kwargs):
            dag_run = state.get('dag_run')
            assert dag_run is not None, ("Decorator `enable_hist_log_check_based_on_conf` can only be used"
                                         " when running Airflow task is decorated with `set_config`")
            if dag_run.conf.get("force_historical_repull"):
                return False
            elif dag_run.conf.get('historical_only'):
                return does_hist_log_exist(*args, **kwargs)
            else:
                return True
        return wrapper
    return _enable_hist_log_check_based_on_conf


def filter_out_standard_queries(query_list: list, **kwargs):
    """
    takes query list and filters out standard_requests
    """
    def _filter_queries(query_list):
        result = [q for q in query_list if q.get('type', '').lower().startswith('hist')
                  or q.get('query_type', '').lower().startswith('hist')]
        return result

    if kwargs['dag_run'].conf.get('historical_only'):
        logging.info('Historical only RUN detected... filtering standard queries')
        # add additional logic for batched queries
        if len(query_list) == 0:
            return []
        if type(query_list[0]) == list:
            # its Batched!
            # find Batch size
            logging.info('its batched!!!')
            res = []
            for batch in query_list:
                new_batch = _filter_queries(batch)
                if len(new_batch) > 0:
                    res.append(_filter_queries(batch))
            return res
        else:
            return _filter_queries(query_list)

    else:
        logging.info('Daily run detected...')
        
        return query_list


def _get_db_value(value: Any) -> str:
    """Stringify value"""
    if isinstance(value, (dict, list)):
        return f"'{json.dumps(value)}'"
    elif isinstance(value, str):
        fixed_value = value.replace("'", "''")
        return f"'{fixed_value}'"
    elif pd.isna(value):
        return 'NULL'
    else:
        return str(value)


def safe_cast_to_float(val, default=None):
    if pd.isna(val):
        return default
    try:
        return float(val)
    except (ValueError, TypeError):
        return default


def safe_cast_to_int(val, default=None):
    if pd.isna(val):
        return default
    try:
        return int(val)
    except (ValueError, TypeError):
        return default


class EtlEndpointProcessorWithParams:
    """
    class to handle new endpoints
    with parametrization
    """
    def __init__(self, etl_endpoint, **kwargs) -> None:
        self.clients_to_call = []
        self.all_clients = []
        self.etl_endpoint = etl_endpoint
        self.kwargs = kwargs
        self.hook = PostgresHook('uri_authentication')

    def get_blacklisted_clients(self):
        """
        Get all blacklisted clients
        """
        blacklisted_clients_df = self.hook.get_pandas_df(
            'select id from company_info_view where turn_off_pipelines = True or is_active = False'
        )
        blacklisted_clients_list = blacklisted_clients_df['id'].unique().tolist()

        logging.info(f'Blacklisted clients - {blacklisted_clients_list}')

        return blacklisted_clients_list
    
    def add_company(self, id_):
        return f"company_id={id_}&"

    def filter_blacklisted_companies(self, blacklisted_list):
        """
        Apply filtering to the etl endpoint result
        Remove all blacklisted clients
        """
        logging.info(f'before filtering :{len(self.clients_to_call)}')
        logging.info([x['company_id'] for x in self.clients_to_call])
        self.clients_to_call = list(
            filter(
                lambda x: x['company_id'] not in blacklisted_list,
                self.clients_to_call))
        logging.info(f'after filtering blacklisted:{len(self.clients_to_call)}')

    def filter_clients_based_on_config(self):
        # appy additional filtering based on the config
        clients_to_run = self.kwargs['dag_run'].conf.get('clients_to_run_on')
        if clients_to_run:
            logging.info('CLIENTS_TO_RUN_ON was found applying filtering...')
            logging.info(f'Removing all profiles except for: {clients_to_run} clients')
            self.clients_to_call = list(
                filter(
                    lambda x: x['company_id'] in clients_to_run,
                    self.clients_to_call
                )
            )
        else:
            self.filter_blacklisted_companies(blacklisted_list=self.get_blacklisted_clients())

    def add_timezone_to_the_endpoint(self):
        """
        Adding timezone parameter to the endpoint
        it is added to the root of the endpoint items
        based on auth service
        """

        def _get_timezone_group(timezone:str):
            """
            function returns timezone group based on the prefix
            of the timezone str
            """
            timezone = timezone.split('/')[0]
            return TIMEZONE_PREFIX_MAP[timezone] if timezone in TIMEZONE_PREFIX_MAP.keys() else DEFAULT_TIMEZONE_GROUP

        # get all clients and timezones
        all_clients = self.hook.get_pandas_df(
            'select id, timezone from company_info_view'
        )
        self.all_clients = all_clients.set_index('id')['timezone'].to_dict()
        # dicts of {'id':'timezone'}
        self.all_clients = [list(item) for item in self.all_clients.items()]
        logging.info(self.all_clients)
        for idx,item in enumerate(self.all_clients):
            try:
                self.all_clients[idx][1] = _get_timezone_group(self.all_clients[idx][1])
            except KeyError:
                errored_company = self.all_clients[idx][0]
                logging.error(f'error extracting timezone for company {errored_company} reverting to default group!!!')
                self.all_clients[idx][1] = DEFAULT_TIMEZONE_GROUP

    def filter_timezones_based_by_current_time(self, timezon_ingestion_map=TIMEZON_INGESTION_MAP):
        """
        filters endpoint response based by current time
        """
        execution_date = datetime.datetime.now().strftime("%H:%M")
        execution_date = datetime.datetime.strptime(execution_date, "%H:%M")
        config_mock = self.kwargs['dag_run'].conf.get('timezone_groups')  # should be a list of ints [1,2]

        def filter_by_config(list_of_timezone_groups):
            self.clients_to_call= [
                    item for item in self.all_clients if item[1] in list_of_timezone_groups
            ]
        if config_mock:
            # if config is present take only those that are present in config
            logging.info(f'Config key for TIMEZONE WAS FOUND!! {config_mock}')
            filter_by_config(config_mock)
            return
        for schedule_time in timezon_ingestion_map.keys():
            offset = abs(int((schedule_time - execution_date).total_seconds()) / 60)
            if offset < 40:
                logging.info(f'Scheduling time indicates that ingestion will be done for group {timezon_ingestion_map[schedule_time]}')
                filter_by_config([timezon_ingestion_map[schedule_time]])
                return
        logging.info('No particular timezone will be pulled this run, because of the start_time of the dag')
        self.clients_to_call= [
                    item for item in self.all_clients
            ]

    def apply_all(self):
        self.add_timezone_to_the_endpoint()
        self.filter_timezones_based_by_current_time()
        self.clients_to_call = [{'company_id': id_[0]} for id_ in self.clients_to_call]
        self.filter_clients_based_on_config()
        params = '&'
        for i in self.clients_to_call:
            items = list(i.items())[0]
            params += f'company_id={items[1]}&'
        if params[-1] == "&":
            params = params[:-1]
        logging.info(f'endpoint = {self.etl_endpoint + params}')
        resp = requests.get(self.etl_endpoint + params).json()
        if type(resp) == dict:
            resp = resp['data']['items']
        comps = [x['company_id'] for x in resp]
        logging.info(f'After timezone and blacklist filtering applied etl endpoint returned these clients:{set(comps)}')
        return resp


class EtlEndpointProcessor:
    """
    Class to handle common filtering and processing of the
    etl endpoints
    """
    def __init__(self, etl_endpoint: str = None, omni_profiles: list = None, **kwargs) -> None:
        if omni_profiles:  # for omni platforms only
            self.response = omni_profiles
        else:  # for others
            self.response = requests.get(etl_endpoint).json()
        if type(self.response) == dict:
            self.response = self.response['data']['items']
        self.kwargs = kwargs
        self.hook = PostgresHook('uri_authentication')

    def get_blacklisted_clients(self):
        """
        Get all blacklisted clients
        """
        blacklisted_clients_df = self.hook.get_pandas_df(
            'select id from company_info_view where turn_off_pipelines = True or is_active = False'
        )
        blacklisted_clients_list = blacklisted_clients_df['id'].unique().tolist()

        logging.info(f'Blacklisted clients - {blacklisted_clients_list}')

        return blacklisted_clients_list

    def filter_blacklisted_companies(self, blacklisted_list):
        """
        Apply filtering to the etl endpoint result
        Remove all blacklisted clients
        """
        self.response = list(
            filter(
                lambda x: x['company_id'] not in blacklisted_list,
                self.response))

    def filter_clients_based_on_config(self):
        # appy additional filtering based on the config
        clients_to_run = self.kwargs['dag_run'].conf.get('clients_to_run_on')
        if clients_to_run:
            logging.info('CLIENTS_TO_RUN_ON was found applying filtering...')
            logging.info(f'Removing all profiles except for: {clients_to_run} clients')
            self.response = list(
                filter(
                    lambda x: x['company_id'] in clients_to_run,
                    self.response
                )
            )
        else:
            self.filter_blacklisted_companies(blacklisted_list=self.get_blacklisted_clients())

    def add_timezone_to_the_endpoint(self):
        """
        Adding timezone parameter to the endpoint
        it is added to the root of the endpoint items
        based on auth service
        """

        def _get_timezone_group(timezone:str):
            """
            function returns timezone group based on the prefix
            of the timezone str
            """
            timezone = timezone.split('/')[0]
            return TIMEZONE_PREFIX_MAP[timezone] if timezone in TIMEZONE_PREFIX_MAP.keys() else DEFAULT_TIMEZONE_GROUP

        # get all clients and timezones
        # TODO: conn should be added for authentication DB
        all_clients = self.hook.get_pandas_df(
            'select id, timezone from company_info_view'
        )
        all_clients = all_clients.set_index('id')['timezone'].to_dict()
        logging.info(all_clients)
        for idx, item in enumerate(self.response):
            try:
                self.response[idx]['timezone'] = _get_timezone_group(all_clients[item['company_id']])
            except KeyError:
                errored_company = self.response[idx]['company_id']
                logging.error(f'error extracting timezone for company {errored_company} reverting to default group!!!')
                self.response[idx]['timezone']=DEFAULT_TIMEZONE_GROUP

    def filter_timezones_based_by_current_time(self, timezon_ingestion_map=TIMEZON_INGESTION_MAP):
        """
        filters endpoint response based by current time
        """
        execution_date = datetime.datetime.now().strftime("%H:%M")
        execution_date = datetime.datetime.strptime(execution_date, "%H:%M")
        config_mock = self.kwargs['dag_run'].conf.get('timezone_groups')  # should be a list of ints [1,2]

        def filter_by_config(response, list_of_timezone_groups):
            return [
                    item for item in response if item['timezone'] in list_of_timezone_groups
            ]
        if config_mock:
            # if config is present take only those that are present in config
            logging.info(f'Config key for TIMEZONE WAS FOUND!! {config_mock}')
            self.response = filter_by_config(self.response, config_mock)
            return
        for schedule_time in timezon_ingestion_map.keys():
            offset = abs(int((schedule_time - execution_date).total_seconds()) / 60)
            if offset < 40:
                logging.info(f'Scheduling time indicates that ingestion will be done for group {timezon_ingestion_map[schedule_time]}')
                self.response = filter_by_config(self.response, [timezon_ingestion_map[schedule_time]])
                return
        logging.info('No particular timezone will be pulled this run, because of the start_time of the dag')

    def apply_all(self):
        """
        Apply all transformations to the endpoint
        """
        self.add_timezone_to_the_endpoint()
        self.filter_clients_based_on_config()
        self.filter_timezones_based_by_current_time()
        comps = [x['company_id'] for x in self.response]
        logging.info(f'AFTER ALL FILTERS data endpoint response is truncated to these clients:{set(comps)}')
        return self.response


class QueryHandler():
    """
    this class handles sql queryies agains
    postgress and datalake tables
    Used in query scheduler
    """
    def __init__(self, json_config) -> None:

        self.conn_map = {
        'reports': PostgresHook(postgres_conn_id='uri_reports'),
        'datintell': PostgresHook(postgres_conn_id='uri_datintell'),
        'authentication': PostgresHook(postgres_conn_id='uri_authentication'),
        'data_ingestion': PostgresHook(postgres_conn_id='uri_data_ingestion'),
        'druid_client': PostgresHook(postgres_conn_id='uri_druid_client'),
        'margin': PostgresHook(postgres_conn_id='uri_margin'),
        'omni-connector': PostgresHook(postgres_conn_id='uri_omni-connector'),
        'platform': PostgresHook(postgres_conn_id='uri_platform'),
        'user_activity': PostgresHook(postgres_conn_id='uri_user_activity')
        }
        @dataclass
        class QueryData():
            query : str
            schedule: str
            database: str
            recipient: str
            query_name: str

        self.QueryData = QueryData(**json_config)

    def clean_sql(self):
        """
        remove all coments from sql stmnt
        """
        # Remove single-line comments (e.g., -- comment)
        self.QueryData.query = re.sub(r'--.*', '', self.QueryData.query)

        # Remove multi-line comments (e.g., /* comment */)
        self.QueryData.query = re.sub(r'/\*.*?\*/', '', self.QueryData.query, flags=re.DOTALL)



    def parse_sql_query(self):
        """
        Method takes sql string and parses it
        returns True if sql string contains only select
        """
        # remove commented code
        self.clean_sql()
        sql_str = self.QueryData.query
        if len(sql_str.split(';')) > 1 and len(sql_str.split(';')[1])> 0:
            logging.warning('Sql query contains multiple statements')
            return False
        elif not sql_str.strip().lower().startswith('select'):
            logging.warning('Sql query does not start with "select"')
            return False
        elif any(x in sql_str.strip().lower() for x in ['drop database', 'alter table']):
            logging.warning('Stop words were found in sql stmnt')
            return False
        else:
            return True

    def read_parquet_from_s3(self,bucket_name, location):
        # Initialize a Boto3 S3 client
        s3 = boto3.client('s3')
        response = s3.list_objects_v2(Bucket=bucket_name, Prefix=location)
        if 'Contents' in response:
            first_object = response['Contents'][0]
            logging.info(first_object)
            # Get the key (object name) of the first object
            file_key = first_object['Key']
            logging.info(file_key)
            # Read the Parquet file from S3
            try:
                response = s3.get_object(Bucket=bucket_name, Key=file_key)
                parquet_data = response['Body'].read()
                df = pd.read_parquet(BytesIO(parquet_data))
                return df
            except Exception as e:
                logging.info(f"Error reading Parquet file from S3: {e}")
        else:
            logging.info("No objects found in the specified location of druid file.")


    def get_data(self):
        """
        reads query and returns base 64 encoded string
        """

        def base64_encode(df):
            csv_buffer = io.BytesIO()
            df.to_csv(csv_buffer, index=False)
            base64_encoded = base64.b64encode(csv_buffer.getvalue()).decode('utf-8')
            return base64_encoded


        if self.QueryData.database in self.conn_map.keys():
            hook = self.conn_map[self.QueryData.database]
            df = hook.get_pandas_df(self.QueryData.query)
            logging.info('got this data to be sent:')
            print(df.head(30))
            return base64_encode(df)
        else:    
            logging.warning(f'Wrong data source connection provided, this data source is not implemented yet. List of available {list(self.conn_map.keys())}')


def add_mapp_columns(df: pd.DataFrame, mapping: dict) -> pd.DataFrame:
    """Map columns based on mapping"""
    df = df.copy()
    for key in mapping:
        df[key] = mapping[key]
    return df


def create_interval(start_date: str, end_date: str, periods: int) -> list:
    """Splits 1 period into chunks"""
    intervals = pd.interval_range(start=pd.Timestamp(start_date), end=pd.Timestamp(end_date), periods=periods)
    res = [(interval.left.strftime('%Y-%m-%d'), interval.right.strftime('%Y-%m-%d')) for interval in intervals]
    return res


def glue_client_factory(**kwargs):
    if kwargs.get('config', None) is not None:
        kwargs['config'] = Config(kwargs['config'])
    return boto3.client('glue', **kwargs)



def split_date_range_period(date_from: str, date_to: str, period_days: int):
    """Split specified date range to days periods
        :param date_from: Start date in format 'YYYY-MM-DD'
        :param date_to: End date in format 'YYYY-MM-DD'
        :param period_days: Period in days (for example - monthly period is 30, weekly period is 7)"""
    date_from = datetime.datetime.strptime(date_from, '%Y-%m-%d').date()
    date_to = datetime.datetime.strptime(date_to, '%Y-%m-%d').date()
    date_ranges = []
    current_period_start = date_from
    current_period_end = current_period_start + timedelta(days=period_days)
    while current_period_end <= date_to:
        # Append the current period date_from and date_to to the list
        date_ranges.append((current_period_start.strftime('%Y-%m-%d'), current_period_end.strftime('%Y-%m-%d')))
        # Move to the next period
        current_period_start = current_period_end + timedelta(days=1)
        current_period_end = current_period_start + timedelta(days=period_days)
    # Append the last date_to if it's not complete
    if current_period_start <= date_to:
        date_ranges.append((current_period_start.strftime('%Y-%m-%d'), date_to.strftime('%Y-%m-%d')))
    return date_ranges


def fetch_avg_durations(dag_id: str) -> dict[str, int]:
    """Fetches average durations of tasks in the given DAG from the Airflow database."""
    session = settings.Session()

    rows = (
        session.query(
            TaskInstance.task_id,
            func.avg(TaskInstance.duration).label("avg_dur")
        )
        .filter(
            TaskInstance.dag_id == dag_id,
            TaskInstance.state  == State.SUCCESS,
        )
        .group_by(TaskInstance.task_id)
        .all()
    )
    session.close()
    return { task: max(int(avg or 0), 1) for task, avg in rows }