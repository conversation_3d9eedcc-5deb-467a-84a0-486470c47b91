from airflow.providers.snowflake.hooks.snowflake import <PERSON><PERSON><PERSON><PERSON><PERSON>

from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.snowflake_constants import LOAD_TABLE_NAME, START_METADATA_STATUS, \
    FINISH_METADATA_STATUS, LOAD_SCHEMA
from datetime import datetime, timezone
from typing import Any, Dict
from snowflake.connector.errors import ProgrammingError
import time


def track_staging_load(entry_name: str, status: str, **kwargs: Dict[str, Any]) -> None:
    """
    Tracks the metadata of the staging load for a particular DAG run.

    Args:
        entry_name (str): The name o entry (free text).
        status (str): The status whether entry started or finished (free text).
        kwargs (Dict[str, Any]): Context provided by Airflow, including 'dag'.
    """
    # Retrieve job details from Airflow context
    run_id = kwargs['dag_run'].run_id
    database_name = kwargs['database_name']
    table_name = kwargs['table_name']
    data_source = kwargs['dag'].data_source
    sf_hook = kwargs['snowflake_hook']

    # Capture the execution start time and current end time
    execution_timestamp = int(datetime.now(timezone.utc).timestamp())

    # Log the staging load details to Snowflake
    log_staging_load(database_name, table_name, run_id, data_source, entry_name, status, execution_timestamp, sf_hook)


# Whole functionality needs refactoring
def log_staging_load(database_name: str, table_name: str, run_id: str, source_data: str, entry_name: str, status: str,
                     timestamp: int, sf_hook:SnowflakeHook) -> None:
    """
    Logs the entry and its status of job execution to LOAD_TABLE

    Args:
        database_name (str): The name of the Database that is being populated.
        table_name (str): The name of the Table that is being populated.
        run_id (str): The unique run ID for the DAG.
        source_data (str): The source of the data being staged.
        source_data (str): The source of the data being staged.
        sf_hook (SnowflakeHook): The Snowflake connection hook.

    """
    conn = sf_hook.get_conn()
    cursor = conn.cursor()
    env_resolver = EnvironmentResolver()
    max_retries = 5
    backoff = 5
    try:
        # Retry mechanism exists only because big number of tasks in parallel writing to SF and its throttling
        for attempt in range(1, max_retries + 1):
            try:
                cursor.execute(f'USE DATABASE "{env_resolver.staging_db_name}"')
                cursor.execute(f'CREATE SCHEMA IF NOT EXISTS {LOAD_SCHEMA}')
                cursor.execute(f'USE SCHEMA "{LOAD_SCHEMA}"')
                create_table_query = f"""
                                        CREATE TABLE IF NOT EXISTS {env_resolver.staging_db_name}.{LOAD_SCHEMA}.{LOAD_TABLE_NAME} (
                                            RUN_ID VARCHAR(100),
                                            DB_NAME VARCHAR(255),
                                            TABLE_NAME VARCHAR(255),
                                            SOURCE_DATA VARCHAR(100),
                                            ENTRY_NAME VARCHAR(255),
                                            STATUS VARCHAR(50),
                                            TIMESTAMP TIMESTAMP_NTZ(9)
                                        );
                                    """

                # Execute the query to create the table
                cursor.execute(create_table_query)
                # Insert load details into the staging_load_tracking table, ensuring proper datetime formatting
                query = f"""
                                    INSERT INTO {LOAD_TABLE_NAME} (RUN_ID, DB_NAME, TABLE_NAME, SOURCE_DATA, ENTRY_NAME, STATUS, TIMESTAMP)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                                """
                cursor.execute(query,
                               (run_id, database_name, table_name, source_data, entry_name, status, str(timestamp)))

                conn.commit()
                break
            except ProgrammingError as e:
                print(f"Attempt {attempt} failed with error: {e}")
                code = getattr(e, "errno", None) or ""
                msg = str(e)
                if ("57014" in msg or code == 57014) and attempt < max_retries:
                    conn.rollback()
                    time.sleep(backoff)
                    backoff *= 2
                    continue
                conn.rollback()
                raise
    except Exception as e:
        # Rollback in case of error
        conn.rollback()
        raise e
    finally:
        # Close the cursor and connection
        cursor.close()
        conn.close()


def log_table_entry(entry_name):
    """
    Decorator to handle logging to LOAD_TABLE
    usage:

    entry_name = JOB_STARTED

    @log_table_entry(entry_name)
    def some_func(arg1,arg2,**kwargs):
        ....

    note: decorator should only decorate python callables for the tasks,
    decorated functions have to have **kwargs argument
    """

    def _log_table_entry(task):
        def wrapper(*args, **kwargs):
            track_staging_load(entry_name, START_METADATA_STATUS, **kwargs)
            result = task(*args, **kwargs)
            track_staging_load(entry_name, FINISH_METADATA_STATUS, **kwargs)
            return result

        return wrapper

    return _log_table_entry
