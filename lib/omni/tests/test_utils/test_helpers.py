import pytest
import requests
from omni.utils import create_interval, QueryData, RequestData
from unittest import mock
from requests.models import Response


@pytest.fixture
def time_interval():
    intervals = {'13_periods': [('2023-04-05', '2023-05-03'), ('2023-05-03', '2023-05-31'), ('2023-05-31', '2023-06-28'),
                ('2023-06-28', '2023-07-26'), ('2023-07-26', '2023-08-23'), ('2023-08-23', '2023-09-20'),
                ('2023-09-20', '2023-10-18'), ('2023-10-18', '2023-11-15'), ('2023-11-15', '2023-12-13'),
                ('2023-12-13', '2024-01-10'), ('2024-01-10', '2024-02-07'), ('2024-02-07', '2024-03-06'),
                ('2024-03-06', '2024-04-04')],
                '4_periods': [('2023-04-05', '2023-07-05'), ('2023-07-05', '2023-10-04'), ('2023-10-04', '2024-01-03'),
                              ('2024-01-03', '2024-04-04')]}
    return intervals


@pytest.fixture
def standard_query():
    query = {'query_body': {'q': 'statistics', 'dateRange.start.month': '03', 'dateRange.start.day': '13',
                            'dateRange.start.year': '2024', 'dateRange.end.month': '03', 'dateRange.end.day': '19',
                            'dateRange.end.year': '2024', 'timeGranularity': 'DAILY',
                            'pivots': ['CAMPAIGN', 'CAMPAIGN_GROUP', 'CREATIVE'],
                            'campaigns': 'urn:li:sponsoredCampaign:*********', 'count': 200000,
                            'accounts': 'urn:li:sponsoredAccount:*********',
                            'fields': 'externalWebsiteConversions,dateRange,clicks,impressions'},
             'query_type': 'standard',
             'profile': {'id': *****************, 'name': 'linkedin integration insights tests',
                         'ad_account_id': '*********', 'ad_account_name': 'IT_Illimity',
                         'access_token': '***', 'refresh_token': '***', 'company_id': 261,
                         'platform': 'linkedin_integrated',
                         'campaigns': [{'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                                        'end_date': '2022-05-17'}], 'timezone': 2},
             'campaign_data': {'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                          'end_date': '2022-05-17'},
             'campaign_date_from': '2024-03-13',
             'campaign_date_to': '2024-03-19',
             'report_link': None}
    return query


@pytest.fixture
def historical_query():
    query = {'query_body': {'q': 'statistics', 'dateRange.start.month': '03', 'dateRange.start.day': '21',
                            'dateRange.start.year': '2023', 'dateRange.end.month': '03', 'dateRange.end.day': '19',
                            'dateRange.end.year': '2024', 'timeGranularity': 'DAILY',
                            'pivots': ['CAMPAIGN', 'CAMPAIGN_GROUP', 'CREATIVE'],
                            'campaigns': 'urn:li:sponsoredCampaign:*********', 'count': 200000,
                            'accounts': 'urn:li:sponsoredAccount:*********',
                            'fields': 'externalWebsiteConversions,dateRange,clicks,impressions'},
             'query_type': 'historical',
             'profile': {'id': *****************, 'name': 'linkedin integration insights tests',
                         'ad_account_id': '*********', 'ad_account_name': 'IT_Illimity', 'access_token': '***',
                         'refresh_token': '***', 'company_id': 261, 'platform': 'linkedin_integrated',
                         'campaigns': [{'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                                        'end_date': '2022-05-17'}], 'timezone': 2},
             'campaign_data': {'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                          'end_date': '2022-05-17'},
             'campaign_date_from': '2023-03-21',
             'campaign_date_to': '2024-03-19',
             'report_link': None}
    return query


@pytest.fixture
def historical_query_for_request(historical_query):
    return QueryData(**historical_query)


@pytest.fixture
def request_mocked_content():
    content = {'paging': {'start': 0, 'count': 200000, 'links': []},
               'elements': [{'videoCompletions': 303, 'videoMidpointCompletions': 358,
                             'dateRange': {'start': {'month': 11, 'day': 30, 'year': 2023},
                                           'end': {'month': 11, 'day': 30, 'year': 2023}},
                             'clicks': 0, 'videoThirdQuartileCompletions': 327, 'videoFirstQuartileCompletions': 414,
                             'costInLocalCurrency': '4.286441364231866172', 'impressions': 520,
                             'pivotValues': ['urn:li:sponsoredCampaign:266080134',
                                             'urn:li:sponsoredCampaignGroup:634766554',
                                             'urn:li:sponsoredCreative:304061764'],
                             'videoStarts': 542, 'videoViews': 355}]}
    return content


def prep_response():
    data = {'paging': {'start': 0, 'count': 200000, 'links': []},
            'elements': [{'videoCompletions': 303, 'videoMidpointCompletions': 358,
                          'dateRange': {'start': {'month': 11, 'day': 30, 'year': 2023},
                                        'end': {'month': 11, 'day': 30, 'year': 2023}},
                          'clicks': 0, 'videoThirdQuartileCompletions': 327, 'videoFirstQuartileCompletions': 414,
                          'costInLocalCurrency': '4.286441364231866172', 'impressions': 520,
                          'pivotValues': ['urn:li:sponsoredCampaign:266080134',
                                          'urn:li:sponsoredCampaignGroup:634766554',
                                          'urn:li:sponsoredCreative:304061764'],
                          'videoStarts': 542, 'videoViews': 355}]}
    response = Response()
    response.status_code = 200
    response._content = data
    req = requests.Request('GET', 'https://api.linkedin.com/v2/adAnalyticsV2/')
    response.request = req.prepare()
    return response


def test_time_interval(time_interval):
    date_from = '2023-04-05'
    date_to = '2024-04-04'
    historical_period_13 = create_interval(start_date=date_from, end_date=date_to, periods=13)
    historical_period_4 = create_interval(start_date=date_from, end_date=date_to, periods=4)
    assert historical_period_13 == time_interval['13_periods']
    assert '2023-04-05' in historical_period_13[0]
    assert '2024-04-04' in historical_period_13[-1]
    assert isinstance(historical_period_13, list)
    assert historical_period_4 == time_interval['4_periods']
    assert '2023-04-05' in historical_period_4[0]
    assert '2024-04-04' in historical_period_4[-1]
    assert isinstance(historical_period_4, list)


def test_query_data_standard_query(standard_query):
    standard_query_data = QueryData(**standard_query)
    assert standard_query_data.query_type == 'standard'
    assert standard_query_data.campaign_data == standard_query['campaign_data']
    assert standard_query_data.query_body == standard_query['query_body']
    assert standard_query_data.campaign_date_from == '2024-03-13'
    assert standard_query_data.campaign_date_to == '2024-03-19'
    assert standard_query_data.report_link is None
    assert standard_query_data.campaign_detail == '{"account_id": "*********", "campaign_id": "*********"}'
    assert standard_query_data.access_token == '***'
    assert standard_query_data.company_id == 261
    assert standard_query_data.to_dict() == standard_query


def test_query_data_historical_query(historical_query):
    historical_query_data = QueryData(**historical_query)
    assert historical_query_data.query_type == 'historical'
    assert historical_query_data.campaign_data == historical_query['campaign_data']
    assert historical_query_data.query_body == historical_query['query_body']
    assert historical_query_data.campaign_date_from == '2023-03-21'
    assert historical_query_data.campaign_date_to == '2024-03-19'
    assert historical_query_data.report_link is None
    assert historical_query_data.campaign_detail == '{"account_id": "*********", "campaign_id": "*********"}'
    assert historical_query_data.access_token == '***'
    assert historical_query_data.company_id == 261
    assert historical_query_data.to_dict() == historical_query


@mock.patch('omni.utils.RequestData.send_request', return_value=prep_response())
def test_request_report(historical_query_for_request, request_mocked_content):
    resp = RequestData(query=historical_query_for_request,
                       flow_name='test_flow',
                       token_header='Authorization',
                       retries=1,
                       error_sleep_time=1,
                       success_sleep_time=1,
                       env_type='test',
                       url='https://api.linkedin.com/v2/adAnalyticsV2/',
                       method='GET').send_request()
    assert resp._content == request_mocked_content
    assert resp.request.url == 'https://api.linkedin.com/v2/adAnalyticsV2/'
    assert resp.status_code == 200
