import pytest
import datetime
import pandas as pd
from unittest import mock
from unittest.mock import MagicMock
from omni.processors import ProfileProcessor, QueryDataProcessor, ReportOrderProcessor, ReportLoadProcessor
from omni.utils import QueryData
from airflow.providers.postgres.hooks.postgres import PostgresHook

pg_hook = PostgresHook(postgres_conn_id='test_reports')


def prepare_queries():
    queries = [{'query_body': {'q': 'statistics', 'dateRange.start.month': '03', 'dateRange.start.day': '21',
                               'dateRange.start.year': '2023', 'dateRange.end.month': '03', 'dateRange.end.day': '19',
                               'dateRange.end.year': '2024', 'timeGranularity': 'DAILY',
                               'pivots': ['CAMPAIGN', 'CAMPAIGN_GROUP', 'CREATIVE'],
                               'campaigns': 'urn:li:sponsoredCampaign:*********', 'count': 200000,
                               'accounts': 'urn:li:sponsoredAccount:*********',
                               'fields': 'externalWebsiteConversions,dateRange,clicks,impressions'},
                'query_type': 'historical',
                'profile': {'id': *****************, 'name': 'linkedin integration insights tests',
                            'ad_account_id': '*********', 'ad_account_name': 'IT_Illimity', 'access_token': '***',
                            'refresh_token': '***', 'company_id': 261, 'platform': 'linkedin_integrated',
                            'campaigns': [{'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                                           'end_date': '2022-05-17'}], 'timezone': 2},
                'campaign_data': {'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                             'end_date': '2022-05-17'},
                'campaign_date_from': '2023-03-21',
                'campaign_date_to': '2024-03-19',
                'report_link': None},
               {'query_body': {'q': 'statistics', 'dateRange.start.month': '03', 'dateRange.start.day': '13',
                               'dateRange.start.year': '2024', 'dateRange.end.month': '03', 'dateRange.end.day': '19',
                               'dateRange.end.year': '2024', 'timeGranularity': 'DAILY',
                               'pivots': ['CAMPAIGN', 'CAMPAIGN_GROUP', 'CREATIVE'],
                               'campaigns': 'urn:li:sponsoredCampaign:*********', 'count': 200000,
                               'accounts': 'urn:li:sponsoredAccount:*********',
                               'fields': 'externalWebsiteConversions,dateRange,clicks,impressions'},
                'query_type': 'standard',
                'profile': {'id': *****************, 'name': 'linkedin integration insights tests',
                            'ad_account_id': '*********', 'ad_account_name': 'IT_Illimity',
                            'access_token': '***', 'refresh_token': '***', 'company_id': 261,
                            'platform': 'linkedin_integrated',
                            'campaigns': [
                              {'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                               'end_date': '2022-05-17'}], 'timezone': 2},
                'campaign_data': {'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                             'end_date': '2022-05-17'},
                'campaign_date_from': '2024-03-13',
                'campaign_date_to': '2024-03-19',
                'report_link': None}]
    return queries


@pytest.fixture
def dates():
    dates = {'TODAY': datetime.datetime.today(), 'YEAR_AGO': datetime.datetime.today() - datetime.timedelta(days=365),
             'YESTERDAY': datetime.datetime.today() - datetime.timedelta(days=1),
             'SEVEN_DAYS_AGO': datetime.datetime.today() - datetime.timedelta(days=7)}
    return dates


@pytest.fixture
def config_mock():
    mocked = {"data": {"message": "null",
                       "items": [{"id": *****************, "name": "linkedin integration insights tests",
                                  "ad_account_id": "*********", "ad_account_name": "IT_Illimity",
                                  "access_token": "***", "refresh_token": "***", "company_id": 261,
                                  "platform": "linkedin_integrated", "campaigns": [
                               {"id": *****************, "external_id": "*********", "start_date": "2021-09-29",
                                "end_date": "2022-05-17"}]},
                               {"id": 111111111111111111, "name": "tiktok integration insights tests",
                                "ad_account_id": "2347288", "ad_account_name": "tiktok", "access_token": "***",
                                "refresh_token": "***", "company_id": 261, "platform": "tiktok_integrated",
                                "campaigns": [{"id": **************, "external_id": "4583838",
                                               "start_date": "2023-02-21", "end_date": "2024-03-17"}],
                                "page_number": "null", "items_per_page": "null", "total_items": "null",
                                "ordering": "null", "additional": "null"}], "error": "null"}}
    return mocked


@pytest.fixture
def profiles():
    profiles = [{"id": *****************, "name": "linkedin integration insights tests", "ad_account_id": "*********",
                 "ad_account_name": "IT_Illimity", "access_token": "***", "refresh_token": "***", "company_id": 261,
                 "platform": "linkedin_integrated", "campaigns": [{"id": *****************, "external_id": "*********",
                                                                   "start_date": "2021-09-29",
                                                                   "end_date": "2022-05-17"}]}]
    return profiles


@pytest.fixture
def standard_query():
    query = {'query_body': {'q': 'statistics', 'dateRange.start.month': '03', 'dateRange.start.day': '13',
                            'dateRange.start.year': '2024', 'dateRange.end.month': '03', 'dateRange.end.day': '19',
                            'dateRange.end.year': '2024', 'timeGranularity': 'DAILY',
                            'pivots': ['CAMPAIGN', 'CAMPAIGN_GROUP', 'CREATIVE'],
                            'campaigns': 'urn:li:sponsoredCampaign:*********', 'count': 200000,
                            'accounts': 'urn:li:sponsoredAccount:*********',
                            'fields': 'externalWebsiteConversions,dateRange,clicks,impressions'},
             'query_type': 'standard',
             'profile': {'id': *****************, 'name': 'linkedin integration insights tests',
                         'ad_account_id': '*********', 'ad_account_name': 'IT_Illimity',
                         'access_token': '***', 'refresh_token': '***', 'company_id': 261,
                         'platform': 'linkedin_integrated',
                         'campaigns': [{'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                                        'end_date': '2022-05-17'}], 'timezone': 2},
             'campaign_data': {'id': *****************, 'external_id': '*********', 'start_date': '2021-09-29',
                          'end_date': '2022-05-17'},
             'campaign_date_from': '2024-03-13',
             'campaign_date_to': '2024-03-19',
             'report_link': None}
    return QueryData(**query)


@pytest.fixture
def mocked_report_response():
    content = {'elements': [{'clicks': 0, 'videoStarts': 34}]}
    return content


@pytest.fixture
def mocked_parsed_report():
    report = pd.DataFrame([{'clicks': 0, 'videoStarts': 34}])
    return report


def test_profile_processor(config_mock):
    test_profiler = ProfileProcessor(platform_name='linkedin_integrated')
    filtered_profiles = test_profiler.filter_profiles(config_mock)
    all_profiles = test_profiler.get_profiles(config_mock=config_mock)
    assert filtered_profiles == [{"id": *****************, "name": "linkedin integration insights tests",
                                  "ad_account_id": "*********", "ad_account_name": "IT_Illimity",
                                  "access_token": "***", "refresh_token": "***", "company_id": 261,
                                  "platform": "linkedin_integrated", "campaigns": [
                               {"id": *****************, "external_id": "*********", "start_date": "2021-09-29",
                                "end_date": "2022-05-17"}]}]
    assert len(filtered_profiles) == 1
    assert all_profiles == config_mock
    assert len(all_profiles['data']['items']) == 2


@mock.patch('omni.processors.get_date_to_execute_on', return_value='2024-03-29')
@mock.patch('omni.processors.QueryDataProcessor._does_hist_log_exist', return_value=False)
def test_query_processor(profiles, dates):
    test_querier = QueryDataProcessor(profiles=profiles, dates=dates, flow_name='test_flow',
                                      api_fields=['dateRange', 'clicks', 'impressions'],
                                      template_file='linkedin_template', sanity_period='SANITY_PULL_30_DAYS')
    standard_queries = test_querier.get_standard_queries(profile=profiles,
                                                         campaign_data={"id": *****************,
                                                                        "external_id": "*********",
                                                                        "start_date": "2021-09-29",
                                                                        "end_date": "2022-05-17"})
    historical_queries = test_querier.get_historical_queries(profile=profiles,
                                                             campaign_data={"id": *****************,
                                                                            "external_id": "*********",
                                                                            "start_date": "2021-09-29",
                                                                            "end_date": "2022-05-17"})
    assert len(standard_queries) == 1
    assert isinstance(standard_queries, list)
    assert isinstance(standard_queries[0], QueryData)
    assert standard_queries[0].query_type == 'standard'
    assert isinstance(standard_queries[0].profile, MagicMock)
    assert len(historical_queries) == 1
    assert isinstance(historical_queries, list)
    assert isinstance(historical_queries[0], QueryData)
    assert historical_queries[0].query_type == 'historical'
    assert isinstance(historical_queries[0].profile, MagicMock)


@mock.patch('omni.processors.ReportLoadProcessor._parse_report_data', return_value=pd.DataFrame([{'clicks': 0,
                                                                                                  'videoStarts': 34}]))
@mock.patch('omni.processors.ReportLoadProcessor._request_report', return_value={'elements': [{'clicks': 0,
                                                                                               'videoStarts': 34}]})
def test_report_loader_processor(standard_query, mocked_parsed_report, mocked_report_response):
    report_loader = ReportLoadProcessor(query_data=standard_query,
                                        flow_name='test_flow',
                                        token_header='Authorization',
                                        retries=1,
                                        error_sleep_time=1,
                                        success_sleep_time=1,
                                        env_type='test',
                                        report_table_name='test_reports_table',
                                        postgres_hook=pg_hook)
    report_data = report_loader._request_report(query=standard_query)
    parsed_report = report_loader._parse_report_data([{'clicks': 0, 'videoStarts': 34}])
    assert report_data == mocked_report_response
    assert 'clicks' or 'videoStarts' in report_data['elements'][0].keys()
    assert report_data['elements'][0]['videoStarts'] == 34
    assert isinstance(parsed_report, pd.DataFrame)
    assert parsed_report.empty is False
    assert list(parsed_report.columns) == ['clicks', 'videoStarts']


@mock.patch('omni.processors.ReportOrderProcessor.order_report', return_value='https://test-report-system/?ogbl#12533')
def test_report_order_processor(mock):
    report_orderer = ReportOrderProcessor(query_data=prepare_queries(),
                                          flow_name='test_flow',
                                          token_header='Authorization',
                                          retries=1,
                                          error_sleep_time=1,
                                          success_sleep_time=1,
                                          env_type='test')
    ordered_queries = report_orderer.order_reports()
    assert isinstance(ordered_queries, list)
    assert ordered_queries[0]['campaign_data']['external_id'] == '*********'
    assert ordered_queries[1]['campaign_data']['external_id'] == '*********'
    assert len(ordered_queries) == 2
    assert ordered_queries[0]['report_link'] == 'https://test-report-system/?ogbl#12533'
    assert ordered_queries[1]['report_link'] == 'https://test-report-system/?ogbl#12533'
