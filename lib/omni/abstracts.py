from abc import abstractmethod, ABC
from typing import Optional


class ProfileInterface(ABC):

    @abstractmethod
    def get_profiles(self, mock: list) -> list:
        """Cleansing the profiles"""
        pass


class QueryInterface(ABC):

    @abstractmethod
    def get_historical_queries(self, profile: dict, campaign: dict, historical_range_mock: Optional[int]) -> list:
        """Creates historical queries"""
        pass

    @abstractmethod
    def get_standard_queries(self, profile: dict, campaign: dict) -> list:
        """Creates historical queries"""
        pass

    @abstractmethod
    def prepare_queries(self) -> list:
        """Creates historical and standard queries"""
        pass


class ReportOrderInterface(ABC):

    @abstractmethod
    def order_report(self, query_data: dict) -> str:
        """Order the report link for single query"""
        pass

    @abstractmethod
    def order_reports(self) -> None:
        """Order the reports links for all queries"""
        pass


class ReportLoadInterface(ABC):

    @abstractmethod
    def process_report(self, query_data: dict) -> None:
        """Process single query"""
        pass

    @abstractmethod
    def process_reports(self) -> None:
        """Process all queries"""
        pass
