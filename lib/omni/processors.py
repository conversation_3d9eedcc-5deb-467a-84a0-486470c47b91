import logging
import json
import traceback
import datetime
from pandas import DataFrame
from typing import Union, Optional
from dataclasses import dataclass

from airflow.providers.postgres.hooks.postgres import PostgresHook
from myn_airflow_lib.commons import filter_out_standard_queries, get_date_to_execute_on, iter_batch, EtlEndpointProcessorWithParams
from myn_airflow_lib.constants import OMNI_ETL_ENDPOINT, HISTORICAL_LOG_TABLE
from myn_airflow_lib.datadog import increment_metric
from myn_airflow_lib.commons import _get_db_value

from omni.abstracts import ProfileInterface, QueryInterface, ReportLoadInterface, ReportOrderInterface
from omni.utils import QueryData, RequestData, split_date_range_period


@dataclass
class ProfileProcessor(ProfileInterface):
    """Class gets and filters all profiles from OMNI_ETL_ENDPOINT"""

    # define init to use **kwargs via dataclass properly
    def __init__(self, platform_name, **kwargs):
        self.platform_name = platform_name
        self.kwargs = kwargs


    def get_profiles(self, config_mock: list) -> list:
        new_endpoint = OMNI_ETL_ENDPOINT + f'/?platform={self.platform_name}'
        processor = EtlEndpointProcessorWithParams(new_endpoint, **self.kwargs)
        if config_mock:
            logging.info('Mocked endpoint was found - using that...')
            raw_profiles = config_mock
        else:
            raw_profiles = processor.apply_all()
        return raw_profiles

@dataclass
class QueryDataProcessor(QueryInterface):
    """Class creates full list of historical and standard queries"""
    profiles: list
    dates: dict
    flow_name: str
    api_fields: list
    template_file: str
    sanity_period: str
    chunk_historical_query: bool = False
    chunk_periods: int = None
    historical_period: str = 'YEAR_AGO'
    campaigns_plural: bool = False

    def _read_query_template(self) -> Union[dict, list]:
        """Get query template from json file"""
        with open(f'static/{self.template_file}.json') as file:
            query_template = json.load(file)
        return query_template

    def _create_query_body(self, campaign_data: Union[dict, list], date_from: str, date_to: str,
                           account_id: str = None) -> dict:
        """Creates filters to use in request body"""
        pass

    def _does_hist_log_exist(self, hist_request: QueryData) -> bool:
        """Filters historical request if it has been already processed
           This method should be added in future inherited class"""
        pass

    def get_historical_queries(self, profile: dict, campaign_data: Union[dict, list], historical_range_mock: Optional[int]) -> list:
        """Collects historical query data"""
        historical_query_data = []
        settings_date_from = self.dates[self.historical_period]
        settings_date_to = self.dates['TODAY']
        if historical_range_mock:
            logging.info('historical_range_mock is set. Using it')
            settings_date_from = self.dates['TODAY'] - datetime.timedelta(historical_range_mock)
        date_from = settings_date_from.strftime('%Y-%m-%d')
        date_to = settings_date_to.strftime('%Y-%m-%d')
        account_id = profile['ad_account_id']
        if self.chunk_historical_query:
            historical_periods = split_date_range_period(date_from=date_from, date_to=date_to, period_days=self.chunk_periods)
            for period in historical_periods:
                hist_req_data = self._create_query_body(campaign_data, period[0], period[1], account_id)
                hist_req = QueryData(hist_req_data, 'historical', profile, campaign_data, period[0], period[1])
                if not self._does_hist_log_exist(hist_req):
                    historical_query_data.append(hist_req)
        else:
            hist_req_data = self._create_query_body(campaign_data, date_from, date_to, account_id)
            hist_req = QueryData(hist_req_data, 'historical', profile, campaign_data, date_from, date_to)
            if not self._does_hist_log_exist(hist_req):
                historical_query_data.append(hist_req)
        return historical_query_data

    def get_standard_queries(self, profile: dict, campaign_data: Union[dict, list], **kwargs) -> list:
        """Collects standard query data"""
        standard_query_data = []
        today = self.dates['TODAY']
        today_format = today.strftime('%Y-%m-%d')
        seven_days_ago_format = get_date_to_execute_on(self.dates, '%Y-%m-%d', self.sanity_period, **kwargs)
        date_from = seven_days_ago_format
        date_to = today_format
        account_id = profile['ad_account_id']
        standard_req_data = self._create_query_body(campaign_data, date_from, date_to, account_id)
        standard_query = QueryData(standard_req_data, 'standard', profile, campaign_data, date_from, date_to)
        standard_query_data.append(standard_query)
        return standard_query_data

    def prepare_queries(self, **kwargs) -> list:
        """Collects historical and standard query data"""
        all_queries = []
        historical_range_mock = kwargs['dag_run'].conf.get('historical_range_in_days')
        for profile in self.profiles:
            if self.campaigns_plural:
                campaigns = [c['external_id'] for c in profile['campaigns']]
                historical_query_data = self.get_historical_queries(profile=profile,
                                                                    campaign_data=campaigns,
                                                                    historical_range_mock=historical_range_mock)
                if historical_query_data:
                    all_queries.extend(historical_query_data)
                standard_query_data = self.get_standard_queries(profile=profile, campaign_data=campaigns, **kwargs)
                if standard_query_data:
                    all_queries.extend(standard_query_data)
            else:
                campaigns = profile['campaigns']
                for campaign in campaigns:
                    historical_query_data = self.get_historical_queries(profile=profile,
                                                                        campaign_data=campaign,
                                                                        historical_range_mock=historical_range_mock)
                    if historical_query_data:
                        all_queries.extend(historical_query_data)
                    standard_query_data = self.get_standard_queries(profile=profile, campaign_data=campaign, **kwargs)
                    if standard_query_data:
                        all_queries.extend(standard_query_data)
        print(f"Total queries prepared = {len(all_queries)}")
        return filter_out_standard_queries([query.to_dict() for query in all_queries], **kwargs)


@dataclass
class ReportLoadProcessor(ReportLoadInterface):
    """Class process reports loading to DB"""
    query_data: list
    flow_name: str
    token_header: str
    retries: int
    error_sleep_time: int
    success_sleep_time: int
    env_type: str
    report_table_name: str
    postgres_hook: PostgresHook
    current_run_number = int

    def _log_hist_request(self, query: QueryData) -> None:
        """Logs historical data to be sure that it has been already processed"""
        campaign_detail = query.campaign_detail
        company_id = query.company_id
        query = f"""
                    INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
                    Values (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
                    """
        data = {'company_id': company_id,
                'campaign_type': self.flow_name,
                'campaign_detail': campaign_detail}
        self.postgres_hook.run(query, True, data)

    def _create_report_db_query(self, columns: str, values_str: str) -> str:
        """
        Creates insert query to push data to DB.
        This method should be added in future inherited class
        """
        pass

    def _put_report_to_db(self, report: DataFrame) -> None:
        """Pushes data to REPORTS DB"""
        columns = ",".join(['"{0}"'.format(col) for col in report.columns])
        rows = list(report.itertuples(index=False, name=None))
        fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]

        # batching data to push in DB
        for i, batch_rows in enumerate(iter_batch(fixed_rows, 5000)):
            logging.info(f"Inserting batch[max_size=5000] #{i}")
            values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
            query = self._create_report_db_query(columns=columns, values_str=values_str)
            self.postgres_hook.run(query, True)

    def _send_request(self, query: QueryData, url: str, method: str, params: dict = None, json_data: dict = None):
        """Handles the requesting API endpoint"""
        requester = RequestData(query=query, url=url, method=method, params=params, json_data=json_data,
                                flow_name=self.flow_name, token_header=self.token_header, retries=self.retries,
                                error_sleep_time=self.error_sleep_time, success_sleep_time=self.success_sleep_time,
                                env_type=self.env_type)
        return requester.send_request()

    def _parse_report_data(self, responses: list, query: QueryData = None) -> DataFrame:
        """Parse report responses into pandas DataFrame.
        This method should be added in future inherited class"""

        pass

    def _request_report(self, query: QueryData) -> list:
        """Call API to get report responses.
        This method should be added in future inherited class"""

        pass

    def process_report(self, query_data: dict) -> None:
        """Process query to get report and load it to DB"""
        logging.info("Starting processing query...")
        query = QueryData(**query_data)
        report_data = self._request_report(query)
        # saving reports in db
        report = self._parse_report_data(report_data, query)
        logging.info(f"Parsed report of length = {len(report)} rows")
        if report.empty:
            logging.info("Report is empty")
        else:
            logging.info("Pushing report to DB ...")
            self._put_report_to_db(report)
            increment_metric('airflow_data_pushed.increment', self.env_type, self.flow_name, report.shape[0],
                             [f"table:{self.report_table_name}", f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
            logging.info('Report is pushed')
        if query.query_type == 'historical':
            self._log_hist_request(query)
            logging.info('Historical log is pushed')

    def process_reports(self) -> None:
        """Run reports for each query"""
        errors = {"historical": 0,
                  "standard": 0}
        logging.info(f'Going to process {len(self.query_data)} queries')
        for query_index, query_item in enumerate(self.query_data):
            logging.info(f'Going to process {query_index + 1} / {len(self.query_data)} queries')
            try:
                self.process_report(query_item)
            except Exception as e:
                errors.setdefault(query_item['query_type'], 0)
                errors[query_item['query_type']] += 1
                error_message = f'had an error {e} on {json.dumps(query_item)} item \n' + traceback.format_exc()
                logging.error(error_message)
        for error_type, error_count in errors.items():
            all_requests_count = len(list(filter(lambda x: x["query_type"] == error_type, self.query_data)))
            logging.info(f'{error_type} errors {error_count}/{all_requests_count}')


@dataclass
class ReportOrderProcessor(ReportOrderInterface):
    """Class calls API to get report links (report ordering)"""
    query_data: list
    flow_name: str
    token_header: str
    retries: int
    error_sleep_time: int
    success_sleep_time: int
    env_type: str
    current_run_number = int

    def order_report(self, query_data: dict) -> str:
        """Order single report by processing single query and returns report link.
        This method should be added in future inherited class"""
        pass

    def order_reports(self) -> list:
        """Calls API to process endpoints"""
        all_queries = []
        errors = {"historical": 0, "standard": 0}
        logging.info(f"Processing len(query_data)={len(self.query_data)};")
        for query_item in self.query_data:
            try:
                report_link = self.order_report(query_item)
                query_item['report_link'] = report_link
                all_queries.append(query_item)
            except Exception as e:
                errors.setdefault(query_item['query_type'], 0)
                errors[query_item['query_type']] += 1
                msg = f'API failed for {self.flow_name} when process {query_item} with error {e}'
                logging.error(msg)

        for error_type, error_count in errors.items():
            all_requests_count = len(list(filter(lambda x: x["query_type"] == error_type, self.query_data)))
            logging.info(f'{error_type} errors {error_count}/{all_requests_count}')
        return all_queries
