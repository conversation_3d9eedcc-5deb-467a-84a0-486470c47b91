import json
import pandas as pd
import requests
import logging
import time
from typing import Union
import datetime
from datetime import timedelta
from dataclasses import dataclass
from copy import deepcopy

from myn_airflow_lib.exceptions import OmniFlowFailed
from myn_airflow_lib.datadog import increment_metric


def create_interval(start_date: str, end_date: str, periods: int) -> list:
    """Splits date range into list of date intervals"""
    intervals = pd.interval_range(start=pd.Timestamp(start_date), end=pd.Timestamp(end_date), periods=periods)
    res = [(interval.left.strftime('%Y-%m-%d'), interval.right.strftime('%Y-%m-%d')) for interval in intervals]
    return res


@dataclass
class QueryData:
    """Class to create queries"""
    query_body: dict
    query_type: str
    profile: dict
    campaign_data: Union[dict, list]
    campaign_date_from: str = None
    campaign_date_to: str = None
    report_link: str = None

    @property
    def campaign_detail(self):
        details = {'account_id': self.profile['ad_account_id']}
        if isinstance(self.campaign_data, dict):
            details.update({'campaign_id': self.campaign_data['external_id']})  # single campaign
        elif isinstance(self.campaign_data, list):
            details.update({'campaign_ids': self.campaign_data})  # plural campaigns
        return json.dumps(details)

    @property
    def account_id(self):
        return self.profile['ad_account_id']

    @property
    def refresh_token(self):
        return self.profile['refresh_token']

    @property
    def access_token(self):
        return self.profile['access_token']

    @property
    def company_id(self):
        return self.profile['company_id']

    @property
    def region(self):
        return self.profile.get('region')

    def to_dict(self):
        return {'query_body': self.query_body,
                'query_type': self.query_type,
                'profile': self.profile,
                'campaign_data': self.campaign_data,
                'campaign_date_from': self.campaign_date_from,
                'campaign_date_to': self.campaign_date_to,
                'report_link': self.report_link}


@dataclass
class RequestData:
    """Class to call API and request/get data"""
    query: QueryData
    flow_name: str
    token_header: str
    retries: int
    error_sleep_time: int
    success_sleep_time: int
    env_type: str
    url: str
    method: str
    params: dict = None
    json_data: dict = None

    def send_request(self):
        """Handles the requesting API endpoint"""
        bad_statuses = [401, 403, 429, 500, 502, 504, 503]
        request_data = {'headers': {'Content-Type': 'application/json',
                                    'Accept': 'application/json'},
                        'url': self.url,
                        'method': self.method}
        if self.token_header == 'Access-Token':
            request_data['headers'].update({'Access-Token': self.query.access_token})
        elif self.token_header == 'Authorization':
            request_data['headers'].update({'Authorization': f'Bearer {self.query.access_token}'})
        if self.params:
            request_data['params'] = self.params
        if self.json_data:
            request_data['json'] = self.json_data

        exc = None
        for tries_made in range(self.retries):
            try:
                response = requests.request(**request_data)
            except Exception as e:
                logging.error(f"Network exception during request execution. {e}. {request_data}")
                exc = e
                time.sleep(self.error_sleep_time)
                continue
            if response.status_code in bad_statuses:
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{self.query.query_type}",
                                                  "request_status:error",
                                                  f"client:{self.query.company_id}"])
                logging.warning(f'send request with data = {json.dumps(request_data)} \n'
                                f'received {response.status_code}')
                time.sleep(self.error_sleep_time)
            else:
                time.sleep(self.success_sleep_time)
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{self.query.query_type}", "request_status:success",
                                                  f"client:{self.query.company_id}"])
                return response
        data_to_log = deepcopy(request_data)
        data_to_log.pop('headers')
        msg = f'FLOW - {self.flow_name}. Cant process the request (tries_made={tries_made + 1}) {json.dumps(data_to_log)}. Exception `{exc}`'
        raise OmniFlowFailed(msg)


def split_date_range_period(date_from: str, date_to: str, period_days: int):
    """Split specified date range to days periods
        :param date_from: Start date in format 'YYYY-MM-DD'
        :param date_to: End date in format 'YYYY-MM-DD'
        :param period_days: Period in days (for example - monthly period is 30, weekly period is 7)"""
    date_from = datetime.datetime.strptime(date_from, '%Y-%m-%d').date()
    date_to = datetime.datetime.strptime(date_to, '%Y-%m-%d').date()
    date_ranges = []
    current_period_start = date_from
    current_period_end = current_period_start + timedelta(days=period_days)
    while current_period_end <= date_to:
        # Append the current period date_from and date_to to the list
        date_ranges.append((current_period_start.strftime('%Y-%m-%d'), current_period_end.strftime('%Y-%m-%d')))
        # Move to the next period
        current_period_start = current_period_end + timedelta(days=1)
        current_period_end = current_period_start + timedelta(days=period_days)
    # Append the last date_to if it's not complete
    if current_period_start <= date_to:
        date_ranges.append((current_period_start.strftime('%Y-%m-%d'), date_to.strftime('%Y-%m-%d')))
    return date_ranges
