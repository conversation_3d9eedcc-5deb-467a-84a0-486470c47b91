import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime
from myn_airflow_lib.snowflake_commons import track_staging_load, log_staging_load
from myn_airflow_lib.snowflake_constants import LOAD_TABLE_NAME


class TestTrackStagingLoad(unittest.TestCase):

    @patch('myn_airflow_lib.snowflake_commons.snowflake.connector.connect')
    def test_log_staging_load(self, mock_connect):
        # Mock the Snowflake connection and cursor
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        sf_hook = MagicMock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor

        # Set up test data
        db_name = 'test_db'
        job_name = 'test_job'
        run_id = 'test_run_id'
        source_data = 'test_source'
        entry_name = 'test_entry_name'
        status = 'test_status'
        timestamp = 1111


        # Call the function
        log_staging_load(db_name, job_name, run_id, source_data, entry_name, status, timestamp, sf_hook)

        # Assertions to ensure the SQL query was called correctly
        mock_cursor.execute.assert_called_once_with(
            f"""
            INSERT INTO {LOAD_TABLE_NAME} (RUN_ID, JOB_NAME, SOURCE_DATA, ENTRY_NAME, STATUS, TIMESTAMP)
            VALUES (%s, %s, %s, %s, %s)
        """.strip(), (run_id, job_name, source_data, entry_name, status, timestamp)
        )

        # Ensure commit was called
        mock_conn.commit.assert_called_once()

        # Ensure cursor and connection were closed
        mock_cursor.close.assert_called_once()
        mock_conn.close.assert_called_once()

    @patch('myn_airflow_lib.snowflake_commons.log_staging_load')
    @patch("myn_airflow_lib.snowflake_commons.datetime")
    def test_track_staging_load(self, mock_datetime, mock_log_staging_load):
        # Mock the Airflow context (kwargs)
        test_source = 'test_source'
        test_run_id = 'test_run_id'
        test_dag_id = 'test_dag_id'
        test_entry = 'test_entry'
        test_status = 'test_status'

        fixed_timestamp = 1742334945

        # Create a mock datetime instance
        mock_datetime_instance = MagicMock()
        mock_datetime_instance.timestamp.return_value = fixed_timestamp

        # Configure the mock to return the mock datetime instance when utcnow() is called
        mock_datetime.utcnow.return_value = mock_datetime_instance


        kwargs = {
            'dag_run': MagicMock(run_id=test_run_id),
            'dag': MagicMock(dag_id=test_dag_id, data_source=test_source)
        }

        # Call the function with the mocked kwargs
        track_staging_load(test_entry, test_status, **kwargs)

        # Ensure log_staging_load is called with correct parameters
        mock_log_staging_load.assert_called_once_with(test_dag_id, test_run_id, test_source, test_entry, test_status,
                                                      1742334945)


if __name__ == '__main__':
    unittest.main()
