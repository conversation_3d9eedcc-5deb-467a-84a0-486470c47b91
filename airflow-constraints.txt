
#
# This constraints file was automatically generated on 2024-12-10T22:43:32.645259
# via "eager-upgrade" mechanism of PIP. For the "v2-10-test" branch of Airflow.
# This variant of constraints install uses the HEAD of the branch version for 'apache-airflow' but installs
# the providers from PIP-released packages at the moment of the constraint generation.
#
# Those constraints are actually those that regular users use to install released version of Airflow.
# We also use those constraints after "apache-airflow" is released and the constraints are tagged with
# "constraints-X.Y.Z" tag to build the production image for that version.
#
# This constraints file is meant to be used only in the "apache-airflow" installation command and not
# in all subsequent pip commands. By using a constraints.txt file, we ensure that solely the Airflow
# installation step is reproducible. Subsequent pip commands may install packages that would have
# been incompatible with the constraints used in Airflow reproducible installation step. Finally, pip
# commands that might change the installed version of apache-airflow should include "apache-airflow==X.Y.Z"
# in the list of install targets to prevent Airflow accidental upgrade or downgrade.
#
# Typical installation process of airflow for Python 3.8 is (with random selection of extras and custom
# dependencies added), usually consists of two steps:
#
# 1. Reproducible installation of airflow with selected providers (note constraints are used):
#
# pip install "apache-airflow[celery,cncf.kubernetes,google,amazon,snowflake]==X.Y.Z" \
#     --constraint \
#    "https://raw.githubusercontent.com/apache/airflow/constraints-X.Y.Z/constraints-3.8.txt"
#
# 2. Installing own dependencies that are potentially not matching the constraints (note constraints are not
#    used, and apache-airflow==X.Y.Z is used to make sure there is no accidental airflow upgrade/downgrade.
#
# pip install "apache-airflow==X.Y.Z" "snowflake-connector-python[pandas]=N.M.O"
#
aenum==3.1.15
agate==1.9.1
aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
aiosqlite==0.21.0
alembic==1.15.1
annotated-types==0.7.0
anyio==4.9.0
apache-airflow==2.10.0
apache-airflow-providers-apache-kafka==1.9.0
apache-airflow-providers-common-compat==1.5.1
apache-airflow-providers-common-io==1.5.1
apache-airflow-providers-common-sql==1.24.0
apache-airflow-providers-fab==1.5.3
apache-airflow-providers-ftp==3.12.3
apache-airflow-providers-http==5.2.1
apache-airflow-providers-imap==3.8.3
apache-airflow-providers-postgres==5.13.0
apache-airflow-providers-smtp==2.0.1
apache-airflow-providers-snowflake==5.7.1
apache-airflow-providers-sqlite==4.0.1
apispec==6.8.1
argcomplete==3.6.0
asgiref==3.8.1
asn1crypto==1.5.1
astronomer-cosmos==1.9.2
async-timeout==5.0.1
attrs==25.3.0
babel==2.17.0
backports.tarfile==1.2.0
blinker==1.9.0
boltons==25.0.0
boto3==1.35.36
build==1.2.2.post1
cachelib==0.13.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.7
clickclick==20.10.2
cloudpickle==3.0.0
colorama==0.4.6
colorlog==6.9.0
ConfigUpdater==3.2
connexion==2.14.2
cron-descriptor==1.4.5
croniter==6.0.0
cryptography==44.0.2
daff==1.3.46
datadog==0.50.2
dbt-adapters==1.14.1
dbt-common==1.16.0
dbt-core==1.9.3
dbt-extractor==0.5.1
dbt-semantic-interfaces==0.7.4
dbt-snowflake==1.9.2
deepdiff==7.0.1
Deprecated==1.2.18
deprecation==2.1.0
dill==0.3.9
distlib==0.3.9
dnspython==2.7.0
email_validator==2.2.0
eval_type_backport==0.2.2
exceptiongroup==1.2.2
filelock==3.18.0
Flask==2.2.5
Flask-AppBuilder==4.5.3
Flask-Babel==2.0.0
Flask-Caching==2.3.1
Flask-JWT-Extended==4.7.1
Flask-Limiter==3.11.0
Flask-Login==0.6.3
Flask-Session==0.5.0
Flask-SQLAlchemy==2.5.1
Flask-WTF==1.2.2
frozenlist==1.5.0
fsspec==2025.3.0
google-analytics-data==0.18.18
google-re2==1.1.20240702
googleapis-common-protos==1.69.2
greenlet==3.1.1
grpcio==1.71.0
gunicorn==23.0.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
idna==3.10
imap-tools==1.10.0
importlib-metadata==6.11.0
inflection==0.5.1
isodate==0.6.1
itsdangerous==2.2.0
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
jeepney==0.9.0
Jinja2==3.1.6
jmespath==1.0.1
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
keyring==25.6.0
kubernetes==30.1.0
lazy-object-proxy==1.10.0
leather==0.4.0
limits==4.2
linkify-it-py==2.0.3
lockfile==0.12.2
mailchimp-transactional==1.0.56
Mako==1.3.9
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
marshmallow-oneofschema==3.1.1
marshmallow-sqlalchemy==0.28.2
mashumaro==3.14
mdit-py-plugins==0.4.2
mdurl==0.1.2
methodtools==0.4.7
more-itertools==10.6.0
msal==1.31.1
msgpack==1.1.0
multidict==6.2.0
networkx==3.2.1
openpyxl==3.1.5
opentelemetry-api==1.31.0
opentelemetry-exporter-otlp==1.31.0
opentelemetry-exporter-otlp-proto-common==1.31.0
opentelemetry-exporter-otlp-proto-grpc==1.31.0
opentelemetry-exporter-otlp-proto-http==1.31.0
opentelemetry-proto==1.31.0
opentelemetry-sdk==1.31.0
opentelemetry-semantic-conventions==0.52b0
ordered-set==4.1.0
packaging==24.2
pandasql==0.7.3
parsedatetime==2.6
pathspec==0.12.1
pendulum==3.0.0
pip-tools==7.4.1
platformdirs==4.3.6
pluggy==1.5.0
prison==0.2.1
propcache==0.3.0
protobuf==5.29.3
psutil==7.0.0
psycopg2-binary==2.9.10
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
Pygments==2.19.1
PyJWT==2.9.0
pyOpenSSL==25.0.0
pyproject_hooks==1.2.0
pytest==8.3.4
python-daemon==3.1.2
python-dateutil==2.9.0.post0
python-nvd3==0.16.0
python-slugify==8.0.4
pytimeparse==1.1.8
pytz==2025.1
PyYAML==6.0.2
referencing==0.36.2
requests==2.32.3
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rich==13.9.4
rich-argparse==1.7.0
rpds-py==0.23.1
SecretStorage==3.3.3
setproctitle==1.3.5
six==1.17.0
sniffio==1.3.1
snowflake-connector-python==3.14.0
snowflake-snowpark-python==1.29.1
snowplow-tracker==1.1.0
sortedcontainers==2.4.0
SQLAlchemy==1.4.54
SQLAlchemy-JSONField==1.0.2
SQLAlchemy-Utils==0.41.2
sqlparse==0.5.3
tabulate==0.9.0
tenacity==9.0.0
termcolor==2.5.0
text-unidecode==1.3
time-machine==2.16.0
tomli==2.2.1
tomlkit==0.13.2
typing_extensions==4.12.2
tzdata==2025.1
tzlocal==5.3.1
uc-micro-py==1.0.3
universal_pathlib==0.2.6
urllib3==1.26.20
virtualenv==20.29.3
Werkzeug==2.2.3
wirerope==1.0.0
wrapt==1.17.2
WTForms==3.2.1
yarl==1.18.3
zipp==3.20.2
pyarrow==20.0.0
kafka-python==2.2.11