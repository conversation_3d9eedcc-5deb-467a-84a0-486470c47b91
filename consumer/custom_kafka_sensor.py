from airflow.sensors.base import BaseSensorOperator
from airflow.hooks.base import BaseHook
from airflow.utils.decorators import apply_defaults
from kafka import KafkaConsumer
from airflow.exceptions import AirflowException


class CustomKafkaSensor(BaseSensorOperator):
    """
    Polls a Kafka topic every 10 seconds. As soon as a new message
    arrives, pushes its metadata (value, partition, offset, timestamp) into XCom
    under key "message" and returns True to signal success.
    """

    @apply_defaults
    def __init__(
        self,
        *,
        kafka_conn_id: str,
        topic: str,
        group_id: str,
        poke_interval: int = 5,
        timeout: int = None,
        **kwargs,
    ) -> None:
        super().__init__(poke_interval=poke_interval, timeout=timeout, **kwargs)
        self.kafka_conn_id = kafka_conn_id
        self.topic = topic
        self.group_id = group_id
        self.consumer = None

    def _build_consumer(self) -> KafkaConsumer:
        conn = BaseHook.get_connection(self.kafka_conn_id)
        bootstrap_servers = conn.extra_dejson.get("bootstrap.servers", "")

        if not bootstrap_servers:
            raise AirflowException("Missing 'bootstrap.servers' in connection extra config")

        bootstrap_servers = bootstrap_servers.split(",")

        extra = conn.extra_dejson or {}

        valid_keys = {
            "bootstrap_servers",
            "security_protocol",
            "ssl_cafile",
            "ssl_certfile",
            "ssl_keyfile",
            "sasl_mechanism",
            "sasl_plain_username",
            "sasl_plain_password",
            "ssl_check_hostname",
        }

        kafka_kwargs = {
            "bootstrap_servers": bootstrap_servers,
            "group_id": self.group_id,
            "auto_offset_reset": "latest",
            "enable_auto_commit": False,  
        }

        for key, value in extra.items():
            if key in valid_keys:
                kafka_kwargs[key] = value

        return KafkaConsumer(self.topic, **kafka_kwargs)

    def poke(self, context) -> bool:
        if self.consumer is None:
            self.consumer = self._build_consumer()

        # Poll once
        msg_pack = self.consumer.poll(timeout_ms=500, max_records=1)
        if not msg_pack:
            return False

        # Process the first record in the pack (if any)
        for tp, records in msg_pack.items():
            if not records:
                continue

            record = records[0]
            payload = {
                "value": record.value,
                "partition": record.partition,
                "offset": record.offset,
                "timestamp": record.timestamp,
            }

            context["ti"].xcom_push(key="message", value=payload)

            try:
                self.consumer.commit()

            except Exception as e:
                self.log.error("Failed to commit Kafka offset: %s", e)

            return True

        return False
