# Only used for local development
version: '3'
x-airflow-common:
  &airflow-common
  build: .
  image: airflow-mint:latest
  env_file:
    - .env
  environment:
    &airflow-common-env
    AIRFLOW__CORE__EXECUTOR: LocalExecutor
    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
    AIRFLOW__CORE__FERNET_KEY: ''
    AIRFLOW__WEBSERVER__SECRET_KEY: 'test'
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
    AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
    AIRFLOW__SCHEDULER__MIN_FILE_PROCESS_INTERVAL: 10
    AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.default'
    AIRFLOW__LOGGING__REMOTE_LOGGING: False
    AIRFLOW__SENTRY__SENTRY_ON: False
    AIRFLOW__CORE__PARALLELISM: 16
    AIRFLOW__CORE__DAG_CONCURRENCY: 8
    AIRFLOW__CORE__DAGBAG_IMPORT_TIMEOUT: 60
    AIRFLOW__WEBSERVER__EXPOSE_CONFIG: True
    AIRFLOW__CORE__ENABLE_DAG_SERIALIZATION: False
    AIRFLOW__CORE__COMPRESS_SERIALIZED_DAGS: False
    AIRFLOW__CORE__STORE_SERIALIZED_DAGS: False
    _PIP_ADDITIONAL_REQUIREMENTS: ${_PIP_ADDITIONAL_REQUIREMENTS:-}


  volumes:
    - ./dags:/opt/airflow/dags
    - ./logs:/opt/airflow/logs
    - ./lib:/opt/airflow/lib
    - ./static:/opt/airflow/static
    - ~/.aws:/home/<USER>/.aws:ro
    - ${SNOWFLAKE_KEY_PATH}:/opt/airflow/keys/rsa_key_snowflake.p8:ro

  user: "${AIRFLOW_UID:-50000}:0"
  depends_on:
    postgres:
      condition: service_healthy

services:
  postgres:
    image: postgres:12
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    ports:
      - 5432:5432
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 5s
      retries: 5
    restart: always

  airflow-webserver:
    <<: *airflow-common
    command: webserver
    ports:
      - 8080:8080
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 10s
      timeout: 10s
      retries: 5
    restart: always

  airflow-scheduler:
    <<: *airflow-common
    command: scheduler
    restart: always

  airflow-init:
    <<: *airflow-common
    command: >
      bash -c "airflow db upgrade &&
               python /opt/airflow/lib/local/add_airflow_connections.py &&
               airflow version"

    environment:
      <<: *airflow-common-env
      _AIRFLOW_DB_UPGRADE: 'true'
      _AIRFLOW_WWW_USER_CREATE: 'true'
      _AIRFLOW_WWW_USER_USERNAME: ${_AIRFLOW_WWW_USER_USERNAME:-airflow}
      _AIRFLOW_WWW_USER_PASSWORD: ${_AIRFLOW_WWW_USER_PASSWORD:-airflow}

  token-refresh:
    <<: *airflow-common
    command: bash /opt/airflow/lib/local/airflow_token_refresher.sh
    restart: always
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres-db-volume:
