# Adform Integration Schema Changes Proposal

## Overview
This document outlines the proposed schema changes required to integrate Adform data into the existing dbt model structure. The integration will add 49 new columns to support Adform's adserver hierarchy and metrics.

## Current Schema Analysis

### Existing Column Structure
Based on analysis of the current models (Google Ads, Meta, DV360, etc.), the current standard schema includes:

**Core Taxonomy Columns:**
- company_id
- journey_id, journey_name
- media_plan_id, media_plan_name
- channel_id, channel_name
- platform_id, platform_name
- tile_id, tile_name
- media_row_id, media_row_name
- platform_campaign_id, platform_campaign_name
- platform_campaign_group_id, platform_campaign_group_name
- ad_asset_id, ad_asset

**Existing Adserver Columns (from CM360):**
- adserver_advertiser_id
- adserver_level_1_id, adserver_level_1_name
- adserver_level_2_id, adserver_level_2_name
- adserver_level_3_id, adserver_level_3_name
- adserver_level_4_id, adserver_level_4_name

**Standard Metrics:**
- currency
- data_source, data_source_details
- date
- impressions, clicks, spend
- completed_views_first, completed_views_mid, completed_views_third, completed_views_full
- video_start, video_views

## Proposed Schema Changes for Adform

### New Columns to Add

The following columns need to be added to support the complete Adform data structure:

**Extended Adserver Hierarchy:**
```sql
-- New adserver level columns (extending existing CM360 pattern)
adserver_level_5_id         -- Maps to Adform Tag ID
adserver_level_6_id         -- Maps to Adform Tracker ID  
adserver_level_6_name       -- Maps to Adform Tracker Name

-- Note: adserver_level_5_name is intentionally omitted as per requirements
```

**New Conversion Metrics:**
```sql
-- Conversion tracking columns
conversion_metric           -- Type of conversion being measured
cost_based_metric          -- Cost-based metric identifier
view_through_conversions   -- Post-impression conversions
click_through_conversions  -- Post-click conversions
conversions               -- Total conversions
```

### Complete Adform Column Set (49 columns)

The final Adform model will include these 49 columns:

**Taxonomy & Hierarchy (22 columns):**
1. company_id
2. journey_id
3. journey_name
4. media_plan_id
5. media_plan_name
6. channel_id
7. channel_name
8. platform_id
9. platform_name
10. tile_id
11. tile_name
12. media_row_id
13. media_row_name
14. platform_campaign_id
15. platform_campaign_name
16. platform_campaign_group_id
17. platform_campaign_group_name
18. ad_asset_id
19. ad_asset
20. adserver_advertiser_id
21. adserver_level_1_id (Campaign)
22. adserver_level_1_name

**Extended Adserver Levels (8 columns):**
23. adserver_level_2_id (Media)
24. adserver_level_2_name
25. adserver_level_3_id (Line Item)
26. adserver_level_3_name
27. adserver_level_4_id (Banner)
28. adserver_level_4_name
29. adserver_level_5_id (Tag)
30. adserver_level_6_id (Tracker)
31. adserver_level_6_name

**Metadata (5 columns):**
32. currency
33. conversion_metric
34. cost_based_metric
35. data_source
36. data_source_details
37. date

**Performance Metrics (12 columns):**
38. impressions
39. clicks
40. spend
41. completed_views_first
42. completed_views_mid
43. completed_views_third
44. completed_views_full
45. video_start
46. video_views
47. view_through_conversions
48. click_through_conversions
49. conversions

## Implementation Plan

### 1. Schema Updates Required

**A. Update Core Schema Files:**
- `dags/dbt/snowflake_dbt/models/transform/schema.yml`
- `dags/dbt/snowflake_dbt/models/core/schema.yml`
- `dags/dbt/snowflake_dbt/models/datamart/schema.yml`

**B. Add New Column Definitions:**
```yaml
# Add to existing schema.yml files
columns:
  - name: adserver_level_5_id
    description: "Adserver Level 5 identifier (Tag ID for Adform)"
    
  - name: adserver_level_6_id
    description: "Adserver Level 6 identifier (Tracker ID for Adform)"
    
  - name: adserver_level_6_name
    description: "Adserver Level 6 name (Tracker Name for Adform)"
    
  - name: conversion_metric
    description: "Type of conversion metric being measured"
    
  - name: cost_based_metric
    description: "Cost-based metric identifier"
    
  - name: view_through_conversions
    description: "Post-impression conversions"
    
  - name: click_through_conversions
    description: "Post-click conversions"
    
  - name: conversions
    description: "Total conversions"
```

### 2. Model Updates Required

**A. Transform Layer Models:**
- Update `fact_performance_data_transform.sql` to include new columns
- Update dimension models to handle new adserver levels

**B. Staging Layer Models:**
- Create new Adform staging models following existing patterns:
  - `adform_base_ad_level_report_case_view.sql`
  - `adform_final_view.sql`
  - `adform_taxonomy_view.sql`

**C. Datamart Layer Models:**
- Update `druid_v2_datamart.sql` to include Adform reports
- Create `adform_base_ad_level_report.sql`

### 3. Source Configuration

**A. Add Adform Sources:**
```yaml
# Add to sources.yml
- name: ADFORM
  database: "{{ env_var('STAGE_DB_NAME') }}"
  schema: ADFORM
  tables:
    - name: adform_adformcampaignmapping
    - name: adform_adformmediamapping  
    - name: adform_adformlineitemmapping
    - name: adform_adformbannermapping
    - name: adform_adformtrackingfiltermapping
```

### 4. Mapping Configuration

**Adform to Standard Mapping:**
- Campaign → adserver_level_1
- Media → adserver_level_2  
- Line Item → adserver_level_3
- Banner → adserver_level_4
- Tag → adserver_level_5
- Tracker → adserver_level_6

## Impact Analysis

### Backward Compatibility
- All existing models will continue to work
- New columns will be NULL for non-Adform data sources
- Existing adserver_level_1 through adserver_level_4 columns remain unchanged

### Performance Considerations
- Additional columns will increase storage requirements
- Query performance may be impacted due to wider tables
- Consider partitioning strategies for large datasets

### Testing Requirements
- Unit tests for new Adform models
- Integration tests for datamart union
- Data quality tests for new columns
- Comparison tests between staging and datamart layers

## Next Steps

1. **Create Adform staging models** following existing patterns
2. **Update schema definitions** with new column specifications  
3. **Modify transform layer** to handle new columns
4. **Update datamart union** to include Adform data
5. **Implement comprehensive testing** for the new integration
6. **Update documentation** and data lineage

## Files to Modify

### Schema Files:
- `dags/dbt/snowflake_dbt/models/transform/schema.yml`
- `dags/dbt/snowflake_dbt/models/core/schema.yml` 
- `dags/dbt/snowflake_dbt/models/datamart/schema.yml`
- `dags/dbt/snowflake_dbt/models/staging/schema.yml`

### Source Files:
- `dags/dbt/snowflake_dbt/models/transform/sources.yml`
- `dags/dbt/snowflake_dbt/models/staging/sources.yml`

### Model Files:
- `dags/dbt/snowflake_dbt/models/datamart/druid_v2_datamart.sql`
- `dags/dbt/snowflake_dbt/models/transform/main/fact_performance_data_transform.sql`

### New Files to Create:
- `dags/dbt/snowflake_dbt/models/staging/adform/` (entire directory)
- `dags/dbt/snowflake_dbt/models/datamart/reports/adform_base_ad_level_report.sql`
- `dags/dbt/snowflake_dbt/models/transform/intermediate/adform/` (directory and models)

This comprehensive approach ensures seamless integration of Adform data while maintaining the existing architecture and data quality standards.
