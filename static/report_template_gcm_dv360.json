{"type": "STANDARD", "criteria": {"dateRange": {"kind": "dfareporting#dateRange", "startDate": "", "endDate": ""}, "dimensions": [{"name": "activityId", "kind": "dfareporting#sortedDimension"}, {"name": "advertiserId", "kind": "dfareporting#sortedDimension"}, {"kind": "dfareporting#sortedDimension", "name": "date"}, {"name": "placementId", "kind": "dfareporting#sortedDimension"}, {"name": "dv360AdvertiserId", "kind": "dfareporting#sortedDimension"}, {"name": "dv360CampaignId", "kind": "dfareporting#sortedDimension"}, {"name": "dv360InsertionOrderId", "kind": "dfareporting#sortedDimension"}, {"name": "dv360LineItemId", "kind": "dfareporting#sortedDimension"}], "metricNames": ["totalConversions", "activeViewViewableImpressions", "richMediaVideoCompletions", "totalConversionsRevenue", "activityClickThroughConversions", "activityViewThroughConversions", "richMediaVideoPlays", "richMediaVideoFirstQuartileCompletes", "richMediaVideoMidpoints", "richMediaVideoThirdQuartileCompletes", "activeViewMeasurableImpressions"]}, "format": "CSV"}