{"ga4_base": {"property": "", "dimensions": [{"name": "sessionSource"}, {"name": "sessionMedium"}, {"name": "sessionCampaignName"}, {"name": "sessionManualTerm"}, {"name": "session<PERSON><PERSON>al<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "eventName"}, {"name": "date"}], "dimension_filter": {"filter": {"field_name": "isKeyEvent", "string_filter": {"value": "true"}}}, "metrics": [{"name": "keyEvents"}, {"name": "activeUsers"}], "date_ranges": [], "limit": 200000, "offset": 0}, "ga4_auto": {"property": "", "dimensions": [{"name": "sessionGoogleAdsAdGroupId"}, {"name": "sessionGoogleAdsCampaignId"}, {"name": "sessionGoogleAdsCreativeId"}, {"name": "eventName"}, {"name": "date"}], "dimension_filter": {"and_group": {"expressions": [{"filter": {"string_filter": {"value": "true"}, "field_name": "isKeyEvent"}}, {"not_expression": {"filter": {"string_filter": {"value": "(not set)"}, "field_name": "sessionGoogleAdsCampaignId"}}}]}}, "metrics": [{"name": "keyEvents"}, {"name": "activeUsers"}], "date_ranges": [], "limit": 200000, "offset": 0}, "ga4_base_sessions": {"property": "", "dimensions": [{"name": "sessionSource"}, {"name": "sessionMedium"}, {"name": "sessionCampaignName"}, {"name": "sessionManualTerm"}, {"name": "session<PERSON><PERSON>al<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "date"}], "metrics": [{"name": "sessions"}, {"name": "totalRevenue"}, {"name": "transactions"}, {"name": "engagedSessions"}, {"name": "grossPurchaseRevenue"}], "date_ranges": [], "limit": 200000, "offset": 0}, "ga4_auto_sessions": {"property": "", "dimensions": [{"name": "sessionGoogleAdsAdGroupId"}, {"name": "sessionGoogleAdsCampaignId"}, {"name": "sessionGoogleAdsCreativeId"}, {"name": "date"}], "dimension_filter": {"not_expression": {"filter": {"string_filter": {"value": "(not set)"}, "field_name": "sessionGoogleAdsCampaignId"}}}, "metrics": [{"name": "sessions"}, {"name": "totalRevenue"}, {"name": "transactions"}, {"name": "engagedSessions"}, {"name": "grossPurchaseRevenue"}], "date_ranges": [], "limit": 200000, "offset": 0}}