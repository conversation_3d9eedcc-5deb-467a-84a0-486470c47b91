{"metadata": {"title": "", "dataRange": {"range": "CUSTOM_DATES", "customStartDate": "", "customEndDate": ""}, "format": "CSV", "sendNotification": false}, "schedule": {"frequency": "ONE_TIME"}, "params": {"groupBys": ["FILTER_DATE", "FILTER_ADVERTISER", "FILTER_TRUEVIEW_AD_GROUP_AD_ID", "FILTER_TRUEVIEW_AD", "FILTER_INSERTION_ORDER", "FILTER_ADVERTISER_CURRENCY", "FILTER_LINE_ITEM"], "type": "YOUTUBE", "metrics": ["METRIC_CLICKS", "METRIC_TRUEVIEW_VIEWS", "METRIC_RICH_MEDIA_VIDEO_FIRST_QUARTILE_COMPLETES", "METRIC_RICH_MEDIA_VIDEO_MIDPOINTS", "METRIC_RICH_MEDIA_VIDEO_THIRD_QUARTILE_COMPLETES", "METRIC_RICH_MEDIA_VIDEO_COMPLETIONS", "METRIC_REVENUE_ADVERTISER", "METRIC_ACTIVE_VIEW_VIEWABLE_IMPRESSIONS", "METRIC_IMPRESSIONS"]}}