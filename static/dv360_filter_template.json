{"metadata": {"title": "", "dataRange": {"range": "CUSTOM_DATES", "customStartDate": "", "customEndDate": ""}, "format": "CSV", "sendNotification": false}, "schedule": {"frequency": "ONE_TIME"}, "params": {"groupBys": ["FILTER_DATE", "FILTER_ADVERTISER", "FILTER_LINE_ITEM", "FILTER_CREATIVE_ID", "FILTER_CREATIVE", "FILTER_MEDIA_PLAN", "FILTER_INSERTION_ORDER", "FILTER_ADVERTISER_CURRENCY"], "type": "STANDARD", "metrics": ["METRIC_IMPRESSIONS", "METRIC_CLICKS", "METRIC_TOTAL_CONVERSIONS", "METRIC_RICH_MEDIA_VIDEO_FIRST_QUARTILE_COMPLETES", "METRIC_RICH_MEDIA_VIDEO_MIDPOINTS", "METRIC_RICH_MEDIA_VIDEO_THIRD_QUARTILE_COMPLETES", "METRIC_RICH_MEDIA_VIDEO_COMPLETIONS", "METRIC_REVENUE_ADVERTISER", "METRIC_ACTIVE_VIEW_VIEWABLE_IMPRESSIONS", "METRIC_LAST_CLICKS", "METRIC_LAST_IMPRESSIONS", "METRIC_RICH_MEDIA_VIDEO_PLAYS", "METRIC_TRUEVIEW_VIEWS"]}}