[{"query_type_nifi_id": 1, "metrics": [{"expression": "ga:goal1Completions"}, {"expression": "ga:goal2Completions"}, {"expression": "ga:goal3Completions"}, {"expression": "ga:goal4Completions"}, {"expression": "ga:goal5Completions"}, {"expression": "ga:goal6Completions"}, {"expression": "ga:goal7Completions"}, {"expression": "ga:goal8Completions"}, {"expression": "ga:goal9Completions"}, {"expression": "ga:goal10Completions"}]}, {"query_type_nifi_id": 2, "metrics": [{"expression": "ga:goal11Completions"}, {"expression": "ga:goal12Completions"}, {"expression": "ga:goal13Completions"}, {"expression": "ga:goal14Completions"}, {"expression": "ga:goal15Completions"}, {"expression": "ga:goal16Completions"}, {"expression": "ga:goal17Completions"}, {"expression": "ga:goal18Completions"}, {"expression": "ga:goal19Completions"}, {"expression": "ga:goal20Completions"}]}, {"query_type_nifi_id": 3, "metrics": [{"expression": "ga:transactions"}, {"expression": "ga:sessions"}, {"expression": "ga:adxClicks"}]}]