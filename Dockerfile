FROM apache/airflow:2.10.0-python3.9 as airflow-base

# It seems like we need to add dbt-snowflake to requirements.txt
RUN python -m venv dbt_venv && source dbt_venv/bin/activate && \
    pip install --no-cache-dir dbt-snowflake && deactivate

ENV PYTHONDONTWRITEBYTECODE=True
ENV PYTHONPATH=$PYTHONPATH:/opt/airflow:/opt/airflow/lib

ENV AIRFLOW__CORE__DAGS_FOLDER=/opt/airflow/dags
ENV AIRFLOW__CORE__DONOT_PICKLE=False
ENV AIRFLOW__WEBSERVER__SHOW_TRIGGER_FORM_IF_NO_PARAMS=True
ENV AIRFLOW__CORE__ENABLE_XCOM_PICKLING=True
ENV AIRFLOW__CORE__EXECUTOR=SequentialExecutor
ENV AIRFLOW__CORE__FERNET_KEY=''
ENV AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=True
ENV AIRFLOW__CORE__LOAD_EXAMPLES=False
ENV AIRFLOW__API__AUTH_BACKEND='airflow.api.auth.backend.default'
ENV AIRFLOW__CORE__DEFAULT_TASK_RETRIES=1
ENV AIRFLOW__CORE__RETRY_EXPONENTIAL_BACKOFF=True

# DBT dag can take longer time to parse
ENV AIRFLOW__CORE__DAGBAG_IMPORT_TIMEOUT=300
ENV AIRFLOW__CORE__DAG_FILE_PROCESSOR_TIMEOUT=300


ENV GCM2_EXCLUDED_CLIENTS=""
ENV GCM2_WAIT_SLEEP=5

 # S3 logging
ENV AIRFLOW__LOGGING__REMOTE_LOGGING=True
ENV AIRFLOW__LOGGING__REMOTE_LOG_CONN_ID=aws_default

# Update this variables to enchance scheduler performance, doesn't really help when you have just 100 tasks
ENV AIRFLOW__KUBERNETES_EXECUTOR__WORKER_PODS_QUEUED_CHECK_INTERVAL=5
ENV AIRFLOW__SCHEDULER__SCHEDULER_HEARTBEAT_SEC=5
ENV AIRFLOW__SCHEDULER__MAX_TIS_PER_QUERY=128
ENV AIRFLOW__KUBERNETES_EXECUTOR__WORKER_PODS_CREATION_BATCH_SIZE=128
ENV AIRFLOW__SCHEDULER__PARSING_PROCESSES=1
ENV AIRFLOW__DAG_PROCESSOR__MIN_FILE_PROCESS_INTERVAL=120

# Probably fixing this issue, but almost impossible to reproduce that's why not sure
# https://mint-ai.atlassian.net/browse/PDT-82815
ENV AIRFLOW__KUBERNETES_EXECUTOR__KUBE_CLIENT_REQUEST_ARGS='{"_request_timeout":[60,60]}'



# Use this options to offload web if needed.
# Faced several bugs with empty dag list
#ENV AIRFLOW__CORE__ENABLE_DAG_SERIALIZATION=True
#ENV AIRFLOW__CORE__COMPRESS_SERIALIZED_DAGS=True
#ENV AIRFLOW__CORE__STORE_SERIALIZED_DAGS=True
#ENV AIRFLOW__CORE__MIN_SERIALIZED_DAG_UPDATE_INTERVAL=30
#ENV AIRFLOW__CORE__MIN_SERIALIZED_DAG_FETCH_INTERVAL=30
#ENV AIRFLOW__SCHEDULER__STANDALONE_DAG_PROCESSOR=True



RUN python -m pip install --upgrade pip
COPY requirements.txt .
COPY airflow-constraints.txt .

RUN pip install -r requirements.txt -c airflow-constraints.txt

FROM airflow-base as airflow-deploy

COPY --chown=airflow:root dags/ /opt/airflow/dags
COPY --chown=airflow:root consumer/ /opt/airflow/consumer
COPY --chown=airflow:root lib/ /opt/airflow/lib
COPY --chown=airflow:root static/ /opt/airflow/static
# Set default env vars for the build phase
ENV STAGE_DB_NAME=DEV_STAGE \
    TRANSFORM_DB_NAME=DEV_TRANSFORM \
    CORE_DB_NAME=DEV_CORE \
    DATAMART_DB_NAME=DEV_DATAMART
RUN dbt deps --project-dir /opt/airflow/dags/dbt/snowflake_dbt

