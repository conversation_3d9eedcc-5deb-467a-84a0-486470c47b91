import datetime
import json
import re
import logging
from io import BytesIO
from typing import List, NoReturn

from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
import pandas as pd
from imap_tools import MailMessage, MailAttachment, EmailAddress, AND, A, MailBox
from openpyxl import load_workbook

from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.sentry import capture_message
from myn_airflow_lib.commons import failure_callback
from myn_airflow_lib.datadog import increment_metric, init_datadog


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
FLOW_NAME = 'AVRAGE_MEDIA_DOOH'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
REPORTS_TABLE_NAME = 'reports_avrage_media_dooh_performance'
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)


def _init_mailbox() -> MailBox:
    """Initialize gmail mailbox"""
    return MailBox('imap.gmail.com').login(env_resolver.common_reports_email,
                                           env_resolver.common_reports_password,
                                           'AVERAGE_MEDIA_DOOH_REPORTS')


def _get_db_value(value):
    if isinstance(value, (dict, list)):
        return f"'{json.dumps(value)}'"
    elif isinstance(value, str):
        return f"'{value}'"
    elif value is None:
        return 'NULL'
    else:
        return str(value)


def _extract_client_id(email):
    if isinstance(email, tuple):
        email = email[0]
    if isinstance(email, EmailAddress):
        email = email.email
    client_id_match = re.match(r'[^+@]+\+(\d+)@[^+@]+', email)
    return None if not client_id_match else client_id_match.group(1)


def validate_message_has_client_id(message: MailMessage) -> bool:
    client_id = _extract_client_id(message.to)
    if client_id is None:
        # unable to extract client id. should log error
        capture_message(f"{FLOW_NAME} | Unable to extract client id. message.to='{message.to}'")
        return False
    return True


def validate_attachment_is_xlsx(attachment: MailAttachment) -> bool:
    if not attachment.filename.lower().endswith('.xlsx'):
        # log error
        capture_message(
            f"{FLOW_NAME} | Email message has non-xlsx attachment. Skip this attachment."
            f" attachment.filename={attachment.filename}")
        return False
    return True


def _find_header_row(content, header_columns):
    # iterates through rows to find first one that contains all `header_columns`
    wb = load_workbook(BytesIO(content), data_only=True)
    # get first sheet
    ws = wb.get_sheet_by_name(wb.sheetnames[0])
    for i, row in enumerate(ws.iter_rows()):
        row_vals = {cell.value for cell in row
                    if isinstance(cell.value, str)}
        if header_columns.issubset(row_vals):
            return i


def process_attachment(message: MailMessage, attachment: MailAttachment) -> None:
    logging.info(
        f"Processing message attachment"
        f" message.id={message.uid} attachment.filename={attachment.filename}")
    content = attachment.payload

    header_columns = {'Campaign ID', 'Position ID', 'Placement ID', 'Date', 'Impressions'}
    header_row = _find_header_row(content, header_columns)
    if header_row is None:
        capture_message(
            f"Cannot find suitable header inside xlsx."
            f" The header must contain columns {header_columns}"
        )
        return

    df = pd.read_excel(BytesIO(content), engine="openpyxl", skiprows=header_row)
    if len(df) == 0:
        logging.info("Empty xlsx. Skip")
        return

    df['company_id'] = _extract_client_id(message.to)
    df['campaign_id'] = df['Campaign ID'].astype(str)
    df['position_id'] = df['Position ID'].astype(str)
    df['placement_id'] = df['Placement ID'].astype(str)
    df['date'] = df['Date'].dt.strftime('%Y-%m-%d')
    df['impressions'] = df['Impressions'].astype(int)

    df = df[[
        'company_id', 'campaign_id', 'position_id', 'placement_id', 'date', 'impressions',
    ]]
    _execute_sql_query(df)
    increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, df.shape[0],
                     [f"table:{REPORTS_TABLE_NAME}"])


def _execute_sql_query(report: pd.DataFrame) -> NoReturn:
    """Prepare and runs sql UPSERT query"""
    logging.info("Starting pushing to DB")
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""
        INSERT INTO {REPORTS_TABLE_NAME} ({columns})
        VALUES {values_str}
        ON CONFLICT (company_id, date, campaign_id, position_id, placement_id)
        DO UPDATE SET impressions=Excluded.impressions
    """
    pg_hook.run(query, True)


def get_message_ids() -> List:
    """Gets all unseen message ids"""
    logging.info("Starting retrieving emails ids")
    with _init_mailbox() as mailbox:
        mailbox_fetched = mailbox.fetch(criteria=AND(seen=False), bulk=True)
        return [[message.uid] for message in mailbox_fetched]


def process_email(msg_id: str) -> NoReturn:
    """Retrieves, process and push emails"""
    logging.info("Starting parsing emails")
    with _init_mailbox() as mailbox:
        mailbox_message = mailbox.fetch(A(uid=msg_id))
        for message in mailbox_message:
            if not validate_message_has_client_id(message):
                logging.info(
                    f"Message skipped. Cannot extract client_id from recipient."
                    f" message.id={message.uid} message.to='{message.to}'")
                continue
            for attachment in message.attachments:
                if not validate_attachment_is_xlsx(attachment):
                    logging.info(
                        f"Attachment skipped. Not xlsx extension."
                        f" message.id={message.uid}"
                        f" attachment.filename={attachment.filename}")
                    continue
                process_attachment(message, attachment)


with DAG(FLOW_NAME,
         description='avrage media dooh flow dag',
         schedule_interval=None,
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False,
         tags=['ingestion_flow'],
         max_active_runs=1,
         on_failure_callback=failure_callback) as dag:
    t1 = PythonOperator(
        task_id='get_message_ids',
        python_callable=get_message_ids,
        on_failure_callback=failure_callback
    )

    t2 = PythonOperator.partial(
        task_id='process_email',
        on_failure_callback=failure_callback,
        python_callable=process_email,
    ).expand(op_args=XComArg(t1))

    t1 >> t2
