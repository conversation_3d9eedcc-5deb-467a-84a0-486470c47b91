import datetime
import itertools
import json
import logging
import re
from io import String<PERSON>
from typing import List, Dict, Union, Iterator, NoReturn, Any

import pandas as pd
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import <PERSON>g<PERSON>Hook
from imap_tools import MailBox, AND, A
from pandas import DataFrame

from myn_airflow_lib.commons import failure_callback
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.sentry import capture_message
from myn_airflow_lib.datadog import increment_metric, init_datadog

pg_hook = PostgresHook(postgres_conn_id='uri_reports')
FLOW_NAME = 'COGNITIVE_RADIO'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
WAIT_SLEEP = 100
REPORTS_TABLE_NAME = 'reports_cognitive_radio_performance'
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)


def _init_mailbox() -> MailBox:
    """Initialize gmail mailbox"""
    return MailBox('imap.gmail.com').login(env_resolver.common_reports_email,
                                           env_resolver.common_reports_password,
                                           'COGNITIVE_RADIO_REPORTS')


def _check_empty_mailbox(iterable: Iterator) -> Union[Iterator, None]:
    """Checks if mailbox generator has message objects"""
    try:
        first = next(iterable)
    except StopIteration:
        return None
    return first, itertools.chain([first], iterable)


def _validate_reqs(message_attrs: Dict[str, bool], check_header: bool = True) -> bool:
    """Validate email requirements"""
    uid = message_attrs.pop('uid')
    date = message_attrs.pop('date')
    validation_type = 'header' if check_header else 'attachment'
    statuses = ('FAILED.', 'SUCCEEDED.')
    message_header = f'{FLOW_NAME}. Message id = {uid}, date = {date}. Validation {validation_type}'
    success_message_body = 'Company id is present, attachment is present' if check_header else 'Attachment is CSV'
    message_mapp = dict(has_to=f'{message_header} - {statuses[0]} Company id is absent in email header (to)',
                        has_attachment=f'{message_header} - {statuses[0]} Attachment is absent in email',
                        has_csv=f'{message_header} - {statuses[0]} Attachment is not a CSV')
    for key, bool_value in message_attrs.items():
        if not bool_value:
            logging.error(message_mapp.get(key))
            capture_message(message_mapp.get(key))
            return False
    logging.info(f'{message_header} - {statuses[1]} {success_message_body}')
    return True


def _parse_payload(payload: Dict[str, str]) -> DataFrame:
    """Cleansing the dataframe"""
    logging.info("Starting parsing email")
    df = pd.read_csv(StringIO(payload['payload']), sep=';')
    # columns renaming
    col_rename_map = {'Campaign ID': 'campaign_id',
                      'Campaign Name': 'campaign_name',
                      'Date': 'date',
                      'Budget Delivered': 'spend',
                      'Impressions Delivered': 'impressions',
                      'Clicks': 'clicks',
                      '25% Completions': 'audio_25_completion',
                      '50% Completions': 'audio_50_completion',
                      '75% Completions': 'audio_75_completion',
                      '100% Completions': 'audio_100_completion'}

    df = df.rename(columns=col_rename_map)
    company_id = payload['company_id']
    df = df.assign(company_id=company_id)
    df['spend'] = df['spend'].str.replace(r',', '.')
    # retrieve last date and format it
    df['date'] = pd.to_datetime(df['date']).dt.strftime('%b %d, %Y')
    # columns ordering
    df_cleared = df[['company_id', 'campaign_id', 'campaign_name', 'date', 'spend', 'impressions', 'clicks',
                     'audio_25_completion', 'audio_50_completion', 'audio_75_completion', 'audio_100_completion']]
    return df_cleared


def _get_db_value(value: Any) -> str:
    """Stringify value"""
    if isinstance(value, (dict, list)):
        return f"'{json.dumps(value)}'"
    elif isinstance(value, str):
        return f"'{value}'"
    elif value is None:
        return 'NULL'
    else:
        return str(value)


def _execute_sql_query(report: DataFrame) -> NoReturn:
    """Prepare and runs sql UPSERT query"""
    logging.info("Starting pushing to DB")
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""INSERT INTO {REPORTS_TABLE_NAME} ({columns})
                    VALUES
                    {values_str} ON CONFLICT (company_id, campaign_id, date)
                    DO UPDATE
                    SET impressions=Excluded.impressions, 
                    clicks=Excluded.clicks,
                    spend=Excluded.spend,
                    audio_25_completion=Excluded.audio_25_completion,
                    audio_50_completion=Excluded.audio_50_completion,
                    audio_75_completion=Excluded.audio_75_completion,
                    audio_100_completion=Excluded.audio_100_completion;
                  """
    pg_hook.run(query, True)


def get_message_ids() -> List:
    """Gets all unseen message ids"""
    logging.info("Starting retrieving emails ids")
    with _init_mailbox() as mailbox:
        mailbox_fetched = mailbox.fetch(criteria=AND(seen=False), bulk=True)
        checked_empty_mailbox = _check_empty_mailbox(mailbox_fetched)
        if checked_empty_mailbox is None:
            logging.info("Gmail mailbox has no any new messages")
            return []
        else:
            # return first checked element back to generator after checking it
            _, mailbox_fetched = checked_empty_mailbox
            emails_ids = [[message.uid] for message in mailbox_fetched]
            return emails_ids


def process_email(msg_id: str) -> NoReturn:
    """Retrieves, process and push emails"""
    logging.info("Starting parsing emails")
    with _init_mailbox() as mailbox:
        mailbox_message = mailbox.fetch(A(uid=msg_id))
        for msg_attr in mailbox_message:
            to_attr_match = re.search(r'[+](\d+)[@]', msg_attr.to[0])
            to_attr = to_attr_match.group(1) if to_attr_match is not None else None
            msg_attachments_attr = msg_attr.attachments
            header_dict = dict(uid=msg_attr.uid,
                               date=msg_attr.date_str,
                               has_to=bool(to_attr),
                               has_attachment=bool(msg_attachments_attr)
                               )
            is_valid_header = _validate_reqs(header_dict)
            if is_valid_header:
                for attachment in msg_attachments_attr:
                    attachment_dict = dict(uid=msg_attr.uid,
                                           date=msg_attr.date_str,
                                           has_csv='.csv' in attachment.filename
                                           )
                    is_valid_attachment = _validate_reqs(attachment_dict, check_header=False)
                    if is_valid_attachment:
                        payload = dict(company_id=to_attr,
                                       payload=attachment.payload.decode())
                        parsed_payload = _parse_payload(payload)
                        _execute_sql_query(parsed_payload)
                        increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, parsed_payload.shape[0],
                                         [f"table:{REPORTS_TABLE_NAME}"])


with DAG(FLOW_NAME,
         description='offline_tv flow dag',
         schedule_interval=None,
         tags=['ingestion_flow'],
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False) as dag:
    t1 = PythonOperator(
        task_id='get_message_ids',
        python_callable=get_message_ids,
        on_failure_callback=failure_callback
    )
    t2 = PythonOperator.partial(
        task_id='process_email',
        on_failure_callback=failure_callback,
        python_callable=process_email,
    ).expand(op_args=XComArg(t1))
    t1 >> t2
