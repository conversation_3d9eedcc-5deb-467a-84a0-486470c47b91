import traceback
import datetime
import io
import json
import logging
import time
from copy import deepcopy
from dataclasses import dataclass
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
import pandas as pd
import requests
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pandas import DataFrame
import os
from myn_airflow_lib.commons import (
    NIfiEndpointProcessor, failure_callback, set_execution_dates, set_config,
    enable_nifilog_check_based_on_conf, get_date_to_execute_on
)
from myn_airflow_lib.constants import (
    GOOGLE_REPORTS_URL_FORMAT,
    GOOGLE_FILE_URL_FORMAT,
    CM360_NIFI_ENDPOINT,
    GOOGLE_RUN_QUERY_URL_FORMAT,
    EXECUTOR_STABLE_NODES,
    K8S_SPECS
)
from myn_airflow_lib.exceptions import QuotaIsReached
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.sentry import capture_message
from myn_airflow_lib.datadog import increment_metric, init_datadog


pg_hook = PostgresHook(postgres_conn_id="uri_reports")
pg_hook.log.disabled = True
FLOW_NAME = "gcm-dv360"
env_resolver = EnvironmentResolver()
state = {
    'dates': {}
}
dates = state['dates']
CAMPAIGN_TYPE = 'gcm_dv360'

env_type = env_resolver.environment
REPORTS_TABLE_NAME = 'campaign_manager_dv360_report'

init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
access_tokens = {}


@dataclass
class QueryData:
    data: dict
    type: str
    profile: dict
    setting: dict
    report_id: int = None

    @property
    def campaign_detail(self):
        campaign_detail = {
            "advertiser_ids": self.profile['advertiser_ids'],
            "campaigns": self.setting['campaigns']
        }
        return json.dumps(campaign_detail, sort_keys=True)

    @property
    def client_id(self):
        return self.profile['client_id']

    @property
    def client_secret(self):
        return self.profile['client_secret']

    @property
    def refresh_token(self):
        return self.profile['refresh_token']

    def to_dict(self):
        return {'data': self.data,
                'type': self.type,
                'profile': self.profile,
                'setting': self.setting,
                'report_id': self.report_id}


def filter_profiles(profile):
    return all([profile['advertiser_ids'],
                profile['profile_id'],
                profile['settings'],
                profile['company_id']])


def get_profiles(**kwargs):
    logging.info(f'Environments are {json.dumps(dict(os.environ))}')
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    processor = NIfiEndpointProcessor(CM360_NIFI_ENDPOINT, **kwargs)
    profiles = processor.apply_all()
    # use this mock for testing, stage profiles don't match with campaigns on CM360
    if config_mock:
        profiles = config_mock
    # use xcom only for small amount of data, not big-ass dataframes
    profiles = list(filter(filter_profiles, profiles))
    return profiles


def get_setting_dimensions(profile, setting):
    dimensions = []
    for advertiser_id in profile['advertiser_ids']:
        dimension = {'dimensionName': 'advertiserId',
                     'kind': 'dfareporting#dimensionValue',
                     'value': int(advertiser_id)}
        dimensions.append(dimension)

    for c in setting['campaigns']:
        if c:
            dimension = {'dimensionName': 'campaign',
                         'kind': "dfareporting#dimensionValue",
                         'id': c}
            dimensions.append(dimension)
    return dimensions


def get_report_template():
    with open("static/report_template_gcm_dv360.json") as file:
        report_template = json.load(file)
    return report_template


def get_standard_query(profile, **kwargs):
    # we are using only campaigns as filter, but in different settings we can have same campaigns
    settings_campaigns = list(set([campaign for setting in profile['settings'] for campaign in setting['campaigns']]))
    sorted_campaigns = sorted(settings_campaigns)
    if len(sorted_campaigns) == 0:
        return
    setting = {'campaigns': sorted_campaigns}
    dimensions = get_setting_dimensions(profile, setting)
    standard_req_data = get_standard_requests_body(dimensions, **kwargs)
    standard_query = QueryData(standard_req_data, 'standard', profile, setting)
    return standard_query


def get_historical_query(profile, setting):
    dimensions = get_setting_dimensions(profile, setting)
    two_years_ago = dates['TODAY'] - datetime.timedelta(365)

    hist_req_data = get_setting_historical_request(two_years_ago.strftime('%Y-%m-%d'),
                                                   dates['TODAY'].strftime('%Y-%m-%d'),
                                                   dimensions)
    hist_req = QueryData(hist_req_data, 'historical', profile, setting)
    if len(hist_req.setting['campaigns']) == 0:
        return None
    if not does_nifilog_exist(hist_req):
        return hist_req


def get_standard_requests_body(dimensions, **kwargs):
    report_template = get_report_template()
    report_template['name'] = f"Yesterday_A"
    today = dates['TODAY'].strftime('%Y-%m-%d')
    week_ago = get_date_to_execute_on(dates, '%Y-%m-%d', **kwargs)
    report_template['criteria']['dateRange'] = {
        "kind": "dfareporting#dateRange",
        "startDate": week_ago,
        "endDate": today
    }
    report_template['criteria']['dimensionFilters'] = dimensions
    return report_template


def get_setting_historical_request(date_from, date_to, dimensions) -> dict:
    report_template = get_report_template()
    report_template['name'] = f"Custom_dates"
    report_template['criteria']['dateRange'] = {
        "kind": "dfareporting#dateRange",
        "startDate": date_from,
        "endDate": date_to
    }
    report_template['criteria']['dimensionFilters'] = dimensions
    return report_template


def get_campaign_detail(req: QueryData):
    return req.campaign_detail


@enable_nifilog_check_based_on_conf(state)
def does_nifilog_exist(hist_request: QueryData):
    if len(hist_request.setting['campaigns']) == 0:
        return False
    campaign_detail = get_campaign_detail(hist_request)
    query = f"""
    select id as nifilog_id
    from reports_nifilog
    where campaign_type = '{CAMPAIGN_TYPE}'
      and campaign_detail = '{campaign_detail}' limit 1;
    """
    df = pg_hook.get_pandas_df(query)
    return not df.empty


def get_setting_scheduled_requests(advertiser_id, dimensions, floodlight_activities_id):
    report_template = get_report_template()
    report_template["name"] = f"Yesterday_A{advertiser_id}"
    report_template["criteria"]["dateRange"] = {
        "kind": "dfareporting#dateRange",
        "relativeDateRange": "LAST_7_DAYS",
    }
    today = dates['TODAY'].strftime("%Y-%m-%d")
    report_template["schedule"] = {
        "active": True,
        "every": 1,
        "repeats": "DAILY",
        "startDate": today,
        "expirationDate": "2030-07-27",
    }
    report_template["criteria"]["dimensionFilters"] = dimensions
    return report_template


def get_scheduled_query(profile, setting):
    dimensions = get_setting_dimensions(profile, setting)
    scheduled_req_data = get_setting_scheduled_requests(
        profile["advertiser_id"], dimensions, setting["floodlight_activities"]
    )
    scheduled_query = QueryData(scheduled_req_data, "scheduled", profile, setting)
    return scheduled_query


@set_config(state)
@set_execution_dates(dates)
def get_google_queries(profiles, **kwargs):  # kwargs are used by decorator
    queries = []
    for profile in profiles:
        settings = profile['settings']
        for setting in settings:
            historical_query = get_historical_query(profile, setting)
            if historical_query is not None:
                queries.append(historical_query)
        standard_query = get_standard_query(profile, **kwargs)
        if standard_query:
            queries.append(standard_query)
    dict_queries = list(map(lambda x: x.to_dict(), queries))
    logging.info(f'Going to process {len(dict_queries)} queries')
    return dict_queries


def generate_google_access_token(client_id, client_secret, refresh_token, force_update=False):
    if refresh_token in access_tokens and not force_update:
        return access_tokens[refresh_token]
    response = requests.post('https://accounts.google.com/o/oauth2/token', json={'client_id': client_id,
                                                                                 'client_secret': client_secret,
                                                                                 'refresh_token': refresh_token,
                                                                                 'grant_type': 'refresh_token'})
    logging.info('Access token resp received')
    if 'access_token' not in response.json():
        logging.error(f"Access token requested but not received. Response `{response.text}`")
        return None
    access_tokens[refresh_token] = response.json()['access_token']
    return access_tokens[refresh_token]


def send_google_request(url: str, method: str, token_creds: dict, json_data: dict = None, headers: dict = None,
                        report_type: str = None, company_id: int = None):
    bad_statuses = [502, 504, 503, 429, 500, 403, 401]
    access_token = generate_google_access_token(**token_creds)
    request_data = {"headers": {"Authorization": f"Bearer {access_token}"}, "url": url, "method": method}
    if json_data:
        request_data["json"] = json_data
    if headers:
        request_data["headers"].update(headers)
    for _ in range(5):
        response = requests.request(**request_data)
        if response.status_code in bad_statuses:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:error",
                                              f"client:{company_id}"])
            logging.warning(
                f"send request with data = {json.dumps(request_data)} \n"
                f"received {response.status_code}"
            )
            if response.status_code == 401:
                logging.info('need new access token')
                access_token_retry = generate_google_access_token(**token_creds, force_update=True)
                request_data['headers']['Authorization'] = f"Bearer {access_token_retry}"
            time.sleep(env_resolver.gcm2_wait_sleep*2)
        else:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:success",
                                              f"client:{company_id}"])
            return response
    data_to_log = deepcopy(request_data)
    data_to_log.pop("headers")
    capture_message("can't process the request " + json.dumps(data_to_log))
    logging.info(f'request response: {response.text}')
    raise QuotaIsReached(FLOW_NAME)


def create_report(url, data, report_type, token_creds, company_id):
    response = send_google_request(url, "POST", token_creds, data, report_type=report_type, company_id=company_id)
    report_id = response.json()["id"]
    return report_id


def create_file(url, report_type, token_creds, company_id):
    response = send_google_request(url, "POST", token_creds, report_type=report_type, company_id=company_id)
    file_id = response.json()["id"]
    return file_id


def get_file_download_url(file_url, report_type, token_creds, company_id):
    for _ in range(100):
        response = send_google_request(file_url, 'GET', token_creds, report_type=report_type, company_id=company_id)
        json_response = response.json()
        status = json_response['status']
        if status == 'REPORT_AVAILABLE':
            return json_response['urls']['apiUrl']
        time.sleep(env_resolver.gcm2_wait_sleep * (3 if report_type == 'historical' else 2))
    message = f'Can\'t load file for file_url={file_url}'
    logging.error(message)
    capture_message(message)
    raise Exception(message)


def get_db_value(value):
    if isinstance(value, (dict, list)):
        return f"'{json.dumps(value)}'"
    elif isinstance(value, str):
        return f"'{value}'"
    elif value is None:
        return "NULL"
    else:
        return str(value)


def put_report_to_db(report: DataFrame):
    columns = ", ".join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(get_db_value, row)) for row in rows]
    values_str = ", ".join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""
                INSERT INTO {REPORTS_TABLE_NAME} ({columns}) 
                VALUES {values_str}
                ON CONFLICT (date, activity_id, placement_id, dv360_advertiser_id,
                dv360_campaign_id, advertiser_id, dv360_insertion_order_id,
                dv360_line_item_id) 
                DO UPDATE SET  total_conversions=Excluded.total_conversions,total_revenue=Excluded.total_revenue,
                rich_media_video_completions=Excluded.rich_media_video_completions,
                active_view_viewable_impressions=Excluded.active_view_viewable_impressions,
                click_through_conversions=Excluded.click_through_conversions,
                view_through_conversions=Excluded.view_through_conversions,
                rich_media_video_plays=Excluded.rich_media_video_plays,
                rich_media_video_first_quartile_completes=Excluded.rich_media_video_first_quartile_completes,
                rich_media_video_midpoints=Excluded.rich_media_video_midpoints,
                rich_media_video_third_quartile_completes=Excluded.rich_media_video_third_quartile_completes;
          """
    pg_hook.run(query, True)


def log_hist_req(query: QueryData):
    campaign_detail = query.campaign_detail
    date_from = query.data["criteria"]["dateRange"]["startDate"]
    date_to = query.data["criteria"]["dateRange"]["endDate"]
    sql = f"""
        INSERT INTO reports_nifilog(date_from, date_to, campaign_type, campaign_detail)
        Values (%(date_from)s, %(date_to)s, %(campaign_type)s, %(campaign_detail)s)
        """
    data = {
        "date_from": date_from,
        "date_to": date_to,
        "campaign_type": "gcm_dv360",
        "campaign_detail": campaign_detail,
    }
    pg_hook.run(sql, True, data)


def run_query(query):
    logging.debug('start load historical query')
    report_url = GOOGLE_REPORTS_URL_FORMAT.format(user_id=query.profile['profile_id'])
    token_creds = {'client_id': query.client_id,
                   'client_secret': query.client_secret,
                   'refresh_token': query.refresh_token}
    report_id = create_report(report_url, query.data, query.type, token_creds,
                              query.profile.get('company_id'))
    logging.debug(f'report_id is {report_id}')
    file_id = create_file(GOOGLE_RUN_QUERY_URL_FORMAT.format(user_id=query.profile['profile_id'],
                                                             report_id=report_id),
                          query.type,
                          token_creds,
                          query.profile.get('company_id'))
    logging.debug(f'file_id is {file_id}')
    return file_id, report_id


def run_single_report(query_data):
    query = QueryData(**query_data)
    try:
        file_id, report_id = run_query(query)
    except QuotaIsReached:
        capture_message(f'Quota is reached for {FLOW_NAME} when process {json.dumps(query_data)}')
        raise
    return [file_id, report_id, query_data, query.type]


def download_report(url, report_type, token_creds, company_id):
    report_response = send_google_request(url, "GET", token_creds, report_type=report_type, company_id=company_id)
    try:
        text = (
            report_response.text.replace('Campi del rapporto', 'Report Fields')
            .replace('Totale complessivo', 'Grand Total')
            .split("Report Fields")[1:][0]
            .split("Grand Total")[0]
            .strip()
            .replace("Activity ID", "activity_id")
            .replace("DV360 Advertiser ID", "dv360_advertiser_id")
            .replace("DV360 Campaign ID", "dv360_campaign_id")
            .replace("Date", "date")
            .replace("Placement ID", "placement_id")
            .replace("Advertiser ID", "advertiser_id")
            .replace("DV360 Insertion Order ID", "dv360_insertion_order_id")
            .replace("DV360 Line Item ID", "dv360_line_item_id")
            .replace("Total Conversions", "total_conversions")
            .replace("Total Revenue", "total_revenue")
            .replace("ID attività", "activity_id")
            .replace("Data", "date")
            .replace("ID inserzionista", "advertiser_id")
            .replace("Conversioni totali", "total_conversions")
            .replace("ID posizionamento", "placement_id")
            .replace("Visualizzazioni complete di un video", "rich_media_video_completions")
            .replace(
                "Visualizzazione attiva: impressioni visibili",
                "active_view_viewable_impressions",
            )
            .replace("Video Completions", "rich_media_video_completions")
            .replace(
                "Active View: Viewable Impressions", "active_view_viewable_impressions"
            )
            .replace("'", " ")
            .replace("Click-through Conversions", "click_through_conversions")
            .replace("View-through Conversions", "view_through_conversions")
            .replace("Video Plays", 'rich_media_video_plays', 1)
            .replace("Video First Quartile Completions", 'rich_media_video_first_quartile_completes', 1)
            .replace("Video Midpoints", 'rich_media_video_midpoints', 1)
            .replace("Video Third Quartile Completions", 'rich_media_video_third_quartile_completes', 1)
        )
        df = pd.read_csv(io.StringIO(text), sep=",")
        df = df.replace("(not set)", None)
        df.columns = [
            "activity_id",
            "advertiser_id",
            "date",
            "placement_id",
            "dv360_advertiser_id",
            "dv360_campaign_id",
            "dv360_insertion_order_id",
            "dv360_line_item_id",
            "total_conversions",
            "active_view_viewable_impressions",
            "rich_media_video_completions",
            "total_revenue",
            "click_through_conversions",
            "view_through_conversions",
            "rich_media_video_plays",
            "rich_media_video_first_quartile_completes",
            "rich_media_video_midpoints",
            "rich_media_video_third_quartile_completes"
        ]
        df['activity_id'] = df['activity_id'].fillna(0)
        return df
    except IndexError as e:
        logging.error('encounted unexpected error while processing report!!!')
        logging.error(f'got unexpected response {report_response.text}')
        raise IndexError


def log_scheduled_req(query: QueryData, report_id: str):
    campaign_detail = query.campaign_detail
    start_date = query.data["schedule"]["startDate"]
    end_date = query.data["schedule"]["expirationDate"]
    sql = """
    INSERT INTO reports_airflow_cm_log(id, start_date, end_date, campaign_type, campaign_detail)
    Values (%(report_id)s, %(start_date)s, %(end_date)s, %(campaign_type)s, %(campaign_detail)s)
    """
    data = {
        "report_id": report_id,
        "start_date": start_date,
        "end_date": end_date,
        "campaign_type": FLOW_NAME,
        "campaign_detail": campaign_detail,
    }
    pg_hook.run(sql, True, parameters=data, mute=True)


@set_execution_dates(dates)
def run_reports(queries, **kwargs):
    result = []
    errors = {"historical": 0,
              "standard": 0}

    for query_data in queries:
        try:
            query_result = run_single_report(query_data)
            result.append(query_result)
        except Exception:
            errors.setdefault(query_data['type'], 0)
            errors[query_data['type']] += 1
            error_message = f'had an error on {json.dumps(query_data)} item \n' + traceback.format_exc()
            logging.error(error_message)
            capture_message(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x["type"] == error_type, queries)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')
    return result


def load_single_report(file_id, report_id, query_data, report_type):
    logging.info(f"load_reports {file_id} {report_id} {report_type} {query_data}")
    query = QueryData(**query_data)
    file_url_endpoint = GOOGLE_FILE_URL_FORMAT.format(user_id=query.profile['profile_id'],
                                                      report_id=report_id,
                                                      file_id=file_id)
    logging.debug('url ' + file_url_endpoint)
    company_id = query.profile.get('company_id')
    token_creds = {'client_id': query.client_id,
                   'client_secret': query.client_secret,
                   'refresh_token': query.refresh_token}
    url = get_file_download_url(file_url_endpoint, report_type, token_creds, company_id)
    logging.debug('found file url')
    report = download_report(url, report_type, token_creds, company_id)
    if not report.empty:
        put_report_to_db(report)
        increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                         [f"table:{REPORTS_TABLE_NAME}", f"client:{company_id}"])
        logging.debug('report is pushed')
    else:
        message = f'report {url} is empty with type {report_type}'
        logging.warning(message)
    if report_type == 'historical':
        log_hist_req(query)
        logging.debug('log is pushed')


@set_execution_dates(dates)
def load_reports(batch, **kwargs):
    errors = {"historical": 0,
              "standard": 0}
    for item in batch:
        try:
            load_single_report(*item)
        except Exception:
            errors.setdefault(item[3], 0)
            errors[item[3]] += 1
            error_message = f'had an error on {item} item \n' + traceback.format_exc()
            logging.error(error_message)
            capture_message(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x[3] == error_type, batch)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')


with DAG(
    "gcm-dv360",
    description="gcm-dv360 flow dag",
    schedule_interval=None,
    tags=['ingestion_flow'],
    start_date=datetime.datetime(2022, 1, 1),
    catchup=False,
) as dag:
    t1 = PythonOperator(
        task_id="get_profiles",
        python_callable=get_profiles,
        on_failure_callback=failure_callback,
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id="get_google_queries",
        on_failure_callback=failure_callback,
        python_callable=get_google_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="run_reports",
        python_callable=run_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"queries": XComArg(t2)},
        executor_config=K8S_SPECS
    )

    t4 = PythonOperator(
        task_id="load_reports",
        python_callable=load_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"batch": XComArg(t3)},
        executor_config=K8S_SPECS
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> t4 >> cleanup_xcoms
