import datetime
import io
import json
import logging
import traceback
import time
import pandas as pd
import requests

from copy import deepcopy
from dataclasses import dataclass
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pandas import DataFrame

from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.commons import (NIfiEndpointProcessor, failure_callback, set_execution_dates, _get_db_value, set_config,
                                     enable_nifilog_check_based_on_conf, get_historical_date_from, get_date_to_execute_on)
from myn_airflow_lib.constants import (GOOGLE_REPORTS_URL_FORMAT,
                                       GOOGLE_FILE_URL_FORMAT, CM360_NIFI_ENDPOINT, GOOGLE_RUN_QUERY_URL_FORMAT,
                                       EXECUTOR_STABLE_NODES)
from myn_airflow_lib.exceptions import QuotaIsReached, NotPaidSearchAccount
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.sentry import capture_message
from myn_airflow_lib.datadog import increment_metric, init_datadog
from myn_airflow_lib.exceptions import AuthError


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True
FLOW_NAME = 'GCM_PAID_SEARCH'
CAMPAIGN_TYPE = 'gcm_paid_search_external'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
REPORTS_TABLE_NAME = 'campaign_manager_paid_search_external_report'
state = {
    'dates': {}
}
dates = state['dates']
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
access_tokens = {}


@dataclass
class QueryData:
    data: dict
    type: str
    profile: dict
    setting: dict
    report_id: int = None

    @property
    def campaign_detail(self):
        campaign_detail = {
            "advertiser_ids": self.profile['advertiser_ids'],
            "campaigns": self.setting['campaigns']
        }
        return json.dumps(campaign_detail, sort_keys=True)

    @property
    def client_id(self):
        return self.profile['client_id']

    @property
    def client_secret(self):
        return self.profile['client_secret']

    @property
    def refresh_token(self):
        return self.profile['refresh_token']

    def to_dict(self):
        return {'data': self.data,
                'type': self.type,
                'profile': self.profile,
                'setting': self.setting,
                'report_id': self.report_id}


def filter_profiles(profile):
    return all([profile['advertiser_ids'],
                profile['profile_id'],
                profile['settings'],
                profile['refresh_token'],
                profile['company_id']])


def get_profiles(**kwargs):
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    processor = NIfiEndpointProcessor(CM360_NIFI_ENDPOINT, **kwargs)
    profiles = processor.apply_all()
    # use this mock for testing on stage
    if config_mock:
        profiles = config_mock
    profiles = list(filter(filter_profiles, profiles))
    return profiles


def get_setting_dimensions(profile, setting):
    dimensions = []
    for advertiser_id in profile['advertiser_ids']:
        dimension = {'dimensionName': 'advertiserId',
                     'kind': 'dfareporting#dimensionValue',
                     'value': int(advertiser_id)}
        dimensions.append(dimension)

    for c in setting['campaigns']:
        if c:
            dimension = {'dimensionName': 'campaign',
                         'kind': "dfareporting#dimensionValue",
                         'id': c}
            dimensions.append(dimension)
    return dimensions


def get_report_template():
    with open('static/report_template_gcm_paid_search_external.json') as file:
        report_template = json.load(file)
    return report_template


def get_request_data(date_from, date_to, dimensions, name) -> dict:
    report_template = get_report_template()
    report_template['name'] = name
    report_template['criteria']['dateRange'] = {
        "kind": "dfareporting#dateRange",
        "startDate": date_from,
        "endDate": date_to
    }
    report_template['criteria']['dimensionFilters'] = dimensions
    return report_template


@enable_nifilog_check_based_on_conf(state)
def does_nifilog_exist(hist_request: QueryData):
    campaign_detail = hist_request.campaign_detail
    query = f"""
        select id as nifilog_id
        from reports_nifilog
        where campaign_type = '{CAMPAIGN_TYPE}'
        and campaign_detail = '{campaign_detail}'
        limit 1;
    """
    df = pg_hook.get_pandas_df(query)
    return not df.empty


def get_historical_query(profile, setting):
    dimensions = get_setting_dimensions(profile, setting)

    date_from = get_historical_date_from(state, dates['YEAR_AGO'].strftime('%Y-%m-%d'))
    date_from = date_from.strftime('%Y-%m-%d') if not isinstance(date_from, str) else date_from
    request_data = get_request_data(date_from,
                                    dates['TODAY'].strftime('%Y-%m-%d'),
                                    dimensions,
                                    "Custom_dates")
    hist_req = QueryData(request_data, 'historical', profile, setting)
    if len(hist_req.setting['campaigns']) == 0:
        return None
    if not does_nifilog_exist(hist_req):
        return hist_req


def get_standard_query(profile, **kwargs):
    settings_campaigns = list(set([campaign for setting in profile['settings'] for campaign in setting['campaigns']]))
    sorted_campaigns = sorted(settings_campaigns)
    if len(sorted_campaigns) == 0:
        return
    setting = {'campaigns': sorted_campaigns}
    dimensions = get_setting_dimensions(profile, setting)
    week_ago = get_date_to_execute_on(dates, '%Y-%m-%d', **kwargs)
    standard_req_data = get_request_data(week_ago,
                                         dates['TODAY'].strftime('%Y-%m-%d'),
                                         dimensions,
                                         "Weekly")
    standard_query = QueryData(standard_req_data, 'standard', profile, setting)
    return standard_query


@set_config(state)
@set_execution_dates(dates)
def get_google_queries(profiles, **kwargs):
    queries = []
    for profile in profiles:
        settings = profile['settings']
        for setting in settings:
            historical_query = get_historical_query(profile, setting)
            if historical_query is not None:
                queries.append(historical_query)
        standard_query = get_standard_query(profile, **kwargs)
        if standard_query:
            queries.append(standard_query)
    dict_queries = list(map(lambda x: x.to_dict(), queries))
    logging.info(f'Going to process {len(dict_queries)} queries')
    return dict_queries


def generate_google_access_token(client_id, client_secret, refresh_token, force_update=False):
    if refresh_token in access_tokens and not force_update:
        return access_tokens[refresh_token]
    response = requests.post('https://accounts.google.com/o/oauth2/token', json={'client_id': client_id,
                                                                                 'client_secret': client_secret,
                                                                                 'refresh_token': refresh_token,
                                                                                 'grant_type': 'refresh_token'})
    logging.info('Access token resp received')
    if 'access_token' not in response.json():
        logging.error(f"Access token requested but not received. Response `{response.text}`")
        return None
    access_tokens[refresh_token] = response.json()['access_token']
    return access_tokens[refresh_token]


def send_google_request(url: str, method: str, token_creds: dict, json_data: dict = None, headers: dict = None,
                        report_type: str = None, company_id: int = None):
    bad_statuses = [502, 504, 503, 429, 500, 403, 401]
    access_token = generate_google_access_token(**token_creds)
    request_data = {
        "headers": {
            "Authorization": f"Bearer {access_token}"
        },
        "url": url,
        "method": method,
    }
    if json_data:
        request_data["json"] = json_data
    if headers:
        request_data["headers"].update(headers)
    for _ in range(5):
        response = requests.request(**request_data)
        if response.status_code in bad_statuses:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:error",
                                              f"client:{company_id}"])
            logging.warning(
                f"send request with data = {json.dumps(request_data)} \n"
                f"received {response.status_code}"
            )
            if response.status_code == 401:
                logging.info('need new access token')
                access_token_retry = generate_google_access_token(**token_creds, force_update=True)
                request_data['headers']['Authorization'] = f"Bearer {access_token_retry}"
            elif response.status_code == 403:
                logging.warning("Skipped. Not paid search account")
                raise NotPaidSearchAccount(FLOW_NAME)
        else:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:success",
                                              f"client:{company_id}"])
            return response
    data_to_log = deepcopy(request_data)
    data_to_log.pop("headers")
    capture_message("can't process the request " + json.dumps(data_to_log))
    logging.info(f'request response: {response.text}')
    raise QuotaIsReached(FLOW_NAME)


def create_report(url, data, report_type, token_creds, company_id):
    response = send_google_request(url, 'POST', token_creds, data, report_type=report_type, company_id=company_id)
    try:
        report_id = response.json()['id']
    except Exception as e:
        logging.error(f"Response {response.text}")
        raise e
    return report_id


def create_file(url, report_type, token_creds, company_id):
    response = send_google_request(url, 'POST', token_creds, report_type=report_type, company_id=company_id)
    try:
        file_id = response.json()['id']
    except Exception as e:
        logging.error(f"Response {response.text}")
        raise e
    return file_id


def get_file_download_url(file_url, report_type, token_creds, company_id):
    # some reports are queued for 2-3 hours then processed (90*120 = 10800 sec = 3h)
    for _ in range(90):
        response = send_google_request(file_url, 'GET', token_creds, report_type=report_type, company_id=company_id)
        json_response = response.json()
        status = json_response['status']
        if status == 'REPORT_AVAILABLE':
            return json_response['urls']['apiUrl']
        time.sleep(120 if report_type == 'historical' else 10)
    message = f'Can not load file for file_url={file_url}'
    logging.error(message)
    capture_message(message)
    raise Exception(message)


def put_report_to_db(report: DataFrame):
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""INSERT INTO {REPORTS_TABLE_NAME} ({columns})
                VALUES
                {values_str} ON CONFLICT (date,activity_id,paid_search_external_campaign_id,
                                          paid_search_external_ad_group_id,paid_search_external_ad_id,
                                          placement_id)
                DO UPDATE 
                SET paid_search_actions=Excluded.paid_search_actions,
                    paid_search_revenue=Excluded.paid_search_revenue,
                    paid_search_transactions=Excluded.paid_search_transactions;
          """
    pg_hook.run(query, True)


def log_hist_req(query: QueryData):
    campaign_detail = query.campaign_detail
    date_from = query.data['criteria']['dateRange']['startDate']
    date_to = query.data['criteria']['dateRange']['endDate']
    query = f"""
        INSERT INTO reports_nifilog(date_from, date_to, campaign_type, campaign_detail)
        VALUES (%(date_from)s, %(date_to)s, %(campaign_type)s, %(campaign_detail)s)
    """
    data = {'date_from': date_from,
            'date_to': date_to,
            'campaign_type': CAMPAIGN_TYPE,
            'campaign_detail': campaign_detail}
    pg_hook.run(query, True, data)


def run_query(query):
    logging.debug('start load historical query')
    report_url = GOOGLE_REPORTS_URL_FORMAT.format(user_id=query.profile['profile_id'])
    token_creds = {'client_id': query.client_id,
                   'client_secret': query.client_secret,
                   'refresh_token': query.refresh_token}
    report_id = create_report(report_url, query.data, query.type, token_creds,
                              query.profile.get('company_id'))
    logging.debug(f'report_id is {report_id}')
    file_id = create_file(GOOGLE_RUN_QUERY_URL_FORMAT.format(user_id=query.profile['profile_id'],
                                                             report_id=report_id),
                          query.type,
                          token_creds,
                          query.profile.get('company_id'))
    logging.debug(f'file_id is {file_id}')
    return file_id, report_id


def run_single_report(query_data):
    query = QueryData(**query_data)
    try:
        file_id, report_id = run_query(query)
    except QuotaIsReached:
        capture_message(f'Quota is reached for {FLOW_NAME} when process {json.dumps(query_data)}')
        raise
    return [file_id, report_id, query_data, query.type]


def download_report(url, report_type, token_creds, company_id):
    report_response = send_google_request(url, 'GET', token_creds, report_type=report_type, company_id=company_id)
    text = report_response.text \
        .replace('Campi del rapporto', 'Report Fields') \
        .replace('Totale complessivo', 'Grand Total') \
        .split('Report Fields')[1].split('Grand Total')[0].strip()
    df = pd.read_csv(io.StringIO(text), sep=",")
    df = df.replace('(not set)', None)
    df.columns = ['date', 'activity_id', 'paid_search_external_campaign_id',
                  'paid_search_external_ad_group_id', 'paid_search_external_ad_id',
                  'placement_id', 'paid_search_actions',
                  'paid_search_transactions', 'paid_search_revenue']
    return df


@set_execution_dates(dates)
def run_reports(queries, **kwargs):
    result = []
    errors = {"historical": 0, "standard": 0}

    for query_data in queries:
        try:
            query_result = run_single_report(query_data)
            result.append(query_result)
        except Exception:
            errors.setdefault(query_data['type'], 0)
            errors[query_data['type']] += 1
            error_message = f'had an error on {json.dumps(query_data)} item \n' + traceback.format_exc()
            logging.error(error_message)
            capture_message(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x["type"] == error_type, queries)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')
    return result


def load_single_report(file_id, report_id, query_data, report_type):
    logging.info(f"load_reports {file_id} {report_id} {report_type} {query_data}")
    query = QueryData(**query_data)
    file_url_endpoint = GOOGLE_FILE_URL_FORMAT.format(user_id=query.profile['profile_id'],
                                                      report_id=report_id,
                                                      file_id=file_id)
    logging.debug('url ' + file_url_endpoint)
    company_id = query.profile.get('company_id')
    token_creds = {'client_id': query.client_id,
                   'client_secret': query.client_secret,
                   'refresh_token': query.refresh_token}
    url = get_file_download_url(file_url_endpoint, report_type, token_creds, company_id)
    logging.debug('found file url')
    report = download_report(url, report_type, token_creds, company_id)
    if not report.empty:
        logging.info(f"Pushing {len(report)} rows to db")
        put_report_to_db(report)
        increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                         [f"table:{REPORTS_TABLE_NAME}", f"client:{company_id}"])
        logging.debug('report is pushed')
    else:
        message = f'report {url} is empty with type {report_type}'
        logging.warning(message)
    if report_type == 'historical':
        log_hist_req(query)
        logging.debug('log is pushed')


@set_execution_dates(dates)
def load_reports(batch, **kwargs):
    errors = {"historical": 0,
              "standard": 0}
    for item in batch:
        try:
            load_single_report(*item)
        except Exception:
            errors.setdefault(item[3], 0)
            errors[item[3]] += 1
            error_message = f'had an error on {item} item \n' + traceback.format_exc()
            logging.error(error_message)
            capture_message(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x[3] == error_type, batch)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')


with DAG('GCM_PAID_SEARCH_EXTERNAL',
         description='gcm_paid_search_external flow dag',
         schedule_interval=None,
         tags=['ingestion_flow'],
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False) as dag:
    t1 = PythonOperator(
        task_id='get_profiles',
        python_callable=get_profiles,
        on_failure_callback=failure_callback,
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id="get_google_queries",
        on_failure_callback=failure_callback,
        python_callable=get_google_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="run_reports",
        python_callable=run_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"queries": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t4 = PythonOperator(
        task_id="load_reports",
        python_callable=load_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"batch": XComArg(t3)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> t4 >> cleanup_xcoms
