import datetime
import json
import logging
import time
import traceback
import requests
from copy import deepcopy
from dataclasses import dataclass
from typing import List, Tuple
import pandas as pd

from pandas import DataFrame

from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import Postg<PERSON>Hook

from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.constants import (THEOUTPLAY_ETL_ENDPOINT, EXECUTOR_STABLE_NODES, THEOUTPLAY_CLOSED_CAMPAIGNS,
                                       THEOUTPLAY_CLOSED_ACCOUNTS, HISTORICAL_LOG_TABLE)
from myn_airflow_lib.datadog import increment_metric, init_datadog
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.exceptions import MintFlowFailed
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.commons import (failure_callback, filter_out_standard_queries, set_execution_dates, _get_db_value,
                                     iter_batch, set_config, enable_hist_log_check_based_on_conf,
                                     get_historical_date_from, EtlEndpointProcessor, get_date_to_execute_on)


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True
FLOW_NAME = 'Theoutplay'
CAMPAIGN_TYPE = 'theoutplay_v2'
REPORTS_TABLE_NAME = 'video_campaigns_reports'
WAIT_SLEEP = 30
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
state = {
    'dates': {},
    'access_tokens': {},
}
dates = state['dates']
access_tokens = state['access_tokens']
with open("static/theoutplay_tempate.gql") as f:
    query_tpl = f.read()


@dataclass
class QueryData:
    query_type: str  # standard or historical
    profile: dict
    campaign: dict
    query_body: str

    @property
    def company_id(self):
        return self.profile['company_id']

    @property
    def campaign_details(self):
        return json.dumps({
            'campaign_id': self.campaign['external_campaign_id'],
            'username': self.profile['username']},
            sort_keys=True)

    def to_dict(self):
        return {'query_type': self.query_type,
                'profile': self.profile,
                'campaign': self.campaign,
                'query_body': self.query_body}


def filter_profiles(profile):
    attributes_are_valid = (profile.get('username')
                            and profile.get('password')
                            and profile.get('campaigns')
                            and f"{profile['username']}-{profile['password']}" not in THEOUTPLAY_CLOSED_ACCOUNTS)
    if not attributes_are_valid:
        return False
    access_token = get_access_token(profile['username'], profile['password'])
    return access_token is not None


def get_profiles(**kwargs):
    mocked_resp = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    if mocked_resp:
        raw_profiles = mocked_resp
    else:
        processor = EtlEndpointProcessor(THEOUTPLAY_ETL_ENDPOINT, **kwargs)
        raw_profiles = processor.apply_all()
    return [profile for profile in raw_profiles if filter_profiles(profile)]


def filter_out_existing_queries(new_batch, queries):
    get_query_key = lambda q: (q.campaign_details, q.query_type)
    existing_keys = {get_query_key(q) for q in queries}
    filtered_queries = []
    for query in new_batch:
        key = get_query_key(query)
        if key not in existing_keys:
            filtered_queries.append(query)
            existing_keys.add(key)
    return filtered_queries


def filter_campaigns(campaigns):
    if not campaigns:
        return []
    filtered_campaigns = []
    for campaign in campaigns:
        campaign_is_valid = (campaign.get('external_campaign_id')
                             and campaign.get('external_campaign_id') not in THEOUTPLAY_CLOSED_CAMPAIGNS)
        if campaign_is_valid:
            filtered_campaigns.append(campaign)
    return filtered_campaigns


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(query: QueryData):
    campaign_detail = query.campaign_details
    query_string = f"""
        select id from {HISTORICAL_LOG_TABLE}
        where
            campaign_type = '{CAMPAIGN_TYPE}'
            and campaign_detail = '{campaign_detail}'
        limit 1
    """
    df = pg_hook.get_pandas_df(query_string)
    return not df.empty


def _get_query(profile, campaign, query_type, start_date, end_date):
    date_presets = f'(startDate:"{start_date}T00:00:00.000Z",endDate:"{end_date}T00:00:00.000Z")'
    query_body = query_tpl.replace(
        "${campaign_id}", campaign["external_campaign_id"]).replace(
        "${date_presets}", date_presets)
    return QueryData(query_type, profile, campaign, query_body)


def get_standard_query(profile, campaign, **kwargs):
    start_date = get_date_to_execute_on(dates, '%Y-%m-%d', **kwargs)
    end_date = dates['TODAY'].strftime('%Y-%m-%d')
    query = _get_query(profile, campaign, 'standard', start_date, end_date)
    return query


def get_historical_query(profile, campaign, historical_range_mock):
    start_date = dates['YEAR_AGO']
    end_date = dates['TODAY'].strftime('%Y-%m-%d')
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        start_date = dates['TODAY'] - datetime.timedelta(historical_range_mock)
    start_date = start_date.strftime('%Y-%m-%d')
    query = _get_query(profile, campaign, 'historical', start_date, end_date)
    if not does_hist_log_exist(query):
        return query


@set_config(state)
@set_execution_dates(dates)
def generate_queries(profiles, **kwargs):
    logging.info(f"Generating queries from {len(profiles)} profiles {profiles}")
    historical_range_mock = state['dag_run'].conf.get('historical_range_in_days')
    standard_queries_count, historical_queries_count = 0, 0
    queries = []
    for profile in profiles:
        logging.info(f"Processing profile {profile}")

        # getting filters to append to query
        campaigns = filter_campaigns(profile['campaigns'])
        if not campaigns:
            logging.warning("No valid campaigns for the profile. Going to skip it")
            continue

        # standard queries
        standard_queries = []
        for campaign in campaigns:
            if campaign.get('is_archived'):
                continue
            query = get_standard_query(profile, campaign, **kwargs)
            standard_queries.append(query)
        standard_queries = filter_out_existing_queries(standard_queries, queries)
        queries.extend(standard_queries)
        standard_queries_count += len(standard_queries)

        # historical queries
        historical_campaigns = [c for c in campaigns if c.get('is_imported')]
        historical_queries = []
        for campaign in historical_campaigns:
            query = get_historical_query(profile, campaign, historical_range_mock)
            if query is not None:
                historical_queries.append(query)
        historical_queries = filter_out_existing_queries(historical_queries, queries)
        queries.extend(historical_queries)
        historical_queries_count += len(historical_queries)
    logging.info(f"Generated {len(queries)} queries total."
                 f" {standard_queries_count} STANDARD and {historical_queries_count} HISTORICAL")
    return (
        filter_out_standard_queries([query.to_dict() for query in queries], **kwargs), state['access_tokens']
    )


def get_access_token(username, password, retries=3):
    if (username, password) in access_tokens:
        return access_tokens[(username, password)]
    exception = None
    response = None
    while retries > 0:
        retries -= 1
        try:
            response = requests.post(
                env_resolver.theoutplay_api_auth_endpoint,
                data={
                    'username': username,
                    'password': password,
                    'client_id': 'theplatform-cli',
                    'grant_type': 'password'
                }
            )
            break
        except Exception as e:
            exception_message = (
                f'Network error occurred while getting access_token (username={username}...) {e}')
            logging.error(exception_message)
            exception = MintFlowFailed(exception_message)
            time.sleep(5)
    if exception is not None:
        return False
    logging.info('Access token resp received')
    if 'access_token' not in response.json():
        logging.error(f"No `access_token_key` in resp {response.text}")
        return None
    access_tokens[(username, password)] = response.json()['access_token']
    return access_tokens[(username, password)]


def request_report(access_token: str, query: QueryData, **kwargs):
    url = env_resolver.theoutplay_api_reports_endpoint
    data = {"query": query.query_body}
    logging.info(f'Processing campaign id = {query.campaign.get("external_campaign_id")}')
    response = send_api_request(access_token, url, 'POST', query.query_type, data, query.profile.get('company_id'), **kwargs)
    return response.json()


def send_api_request(access_token: str, url: str, method: str, query_type: str, json_data: dict = None,
                     company_id: int = None, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    bad_statuses = [429, 403, 404, 401, 400, 500, 502, 503, 504]
    headers = {
        'Authorization': f"Bearer {access_token}",
        'Content-Type': 'application/json'
    }
    last_status_code = None
    for i_retry in range(5):
        response = requests.request(method=method, url=url, headers=headers,
                                    data=json.dumps(json_data))
        last_status_code = response.status_code

        # request can have errors even if code == 200
        if response.status_code in bad_statuses or 'errors' in response.text:
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}", "request_status:error", f"client:{company_id}", f'run_number:{current_run_number}'])
            logging.error(f'Sending request with data = {json.dumps(headers)} \n'
                          f'Received {response.status_code} {response.text}')
            time.sleep(WAIT_SLEEP)

        # code == 200 and no errors in response
        else:
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}", "request_status:success", f"client:{company_id}", f'run_number:{current_run_number}'])
            time.sleep(5)
            return response
    data_to_log = deepcopy(headers)
    data_to_log['Authorization'] = data_to_log['Authorization'][:30]
    data_to_log['last_status_code'] = last_status_code
    raise MintFlowFailed(f"{FLOW_NAME} can't process the request, {i_retry + 1} tries done."
                         f" {json.dumps(data_to_log)}")


@set_config(state)
@set_execution_dates(dates)
def process_queries(queries_and_tokens: Tuple[List[dict], dict], **kwargs):
    generated_batch, access_tokens = queries_and_tokens
    state['access_tokens'].update(access_tokens)
    logging.info(f"STARTING PROCESSING QUERIES {len(generated_batch)} {generated_batch}")
    errors_count = 0
    historical_errors_count = 0
    standard_errors_count = 0
    errors_occurred_with_this_profiles = set()
    for i, query_data in enumerate(generated_batch):
        logging.info(f"Current query_data [{i} / {len(generated_batch)}] {query_data}")
        query = QueryData(**query_data)
        try:
            process_query(query, **kwargs)
            logging.info("PROCESSING SINGLE QUERY DONE")
        except Exception as e:
            logging.error(f"Exception while processing query {query_data} ; {e}")
            logging.error(traceback.format_exc())
            errors_count += 1
            if query.query_type == 'historical':
                historical_errors_count += 1
            else:
                standard_errors_count += 1
            errors_occurred_with_this_profiles.add(
                json.dumps(query.profile, indent=4, sort_keys=True)
            )
    logging.info(f"Processed {len(generated_batch)} queries. There were {errors_count} errors")
    logging.info(f"Historical_errors_count={historical_errors_count};")
    logging.info(f"Standard_errors_count={standard_errors_count};")
    logging.info(f"Errors_occurred_with_this_profiles={errors_occurred_with_this_profiles};")
    return [{"total": len(generated_batch), "errors": errors_count,
             "historical_errors_count": historical_errors_count,
             "standard_errors_count": standard_errors_count,
             "errors_occurred_with_this_profiles": list(errors_occurred_with_this_profiles)}]


def print_processing_summary(results):
    total, errors = 0, 0
    historical_errors_count, standard_errors_count = 0, 0
    errors_occurred_with_this_profiles = set()
    for row in results:
        total += row['total']
        errors += row['errors']
        historical_errors_count += row['historical_errors_count']
        standard_errors_count += row['standard_errors_count']
        errors_occurred_with_this_profiles.update(row['errors_occurred_with_this_profiles'])
    errors_occurred_with_this_profiles = [
        json.loads(p) for p in errors_occurred_with_this_profiles
    ]
    logging.info(f"PROCESSED {total} QUERIES. There were {errors} ERRORS"
                 f" ({historical_errors_count} HISTORICAL errors & {standard_errors_count} STANDARD errors)")
    logging.info(f"errors_occurred_with_this_profiles (count={len(errors_occurred_with_this_profiles)})"
                 f" {json.dumps(errors_occurred_with_this_profiles, indent=4)}")


def log_hist_request(query: QueryData):
    campaign_detail = query.campaign_details
    company_id = query.company_id
    query = f"""
                INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
                Values (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
                """
    data = {'company_id': company_id,
            'campaign_type': CAMPAIGN_TYPE,
            'campaign_detail': campaign_detail}
    pg_hook.run(query, True, data)


def _get_sql_columns_and_values(report, columns_order):
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    return columns_str, values_str


def put_report_to_db(report: DataFrame, company_id: int, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    columns_order = ['date', 'campaign_id', 'line_item_id', 'provider', 'clicks', 'impressions',
                     'completed_views_third', 'completed_views_mid', 'completed_views_first',
                     'full_viewability', 'iab_viewability', 'completed_views_full', 'be_event_count',
                     'budget_spent', 'booked_volume']
    batch_size = 5000
    for i, batch_report in enumerate(iter_batch(report.reset_index(drop=True), batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        columns_str, values_str = _get_sql_columns_and_values(batch_report, columns_order)
        sql = f"""
            INSERT INTO {REPORTS_TABLE_NAME} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (date, campaign_id, line_item_id)
            DO UPDATE
                SET clicks=Excluded.clicks,
                    impressions=Excluded.impressions,
                    completed_views_third=Excluded.completed_views_third,
                    completed_views_mid=Excluded.completed_views_mid,
                    completed_views_first=Excluded.completed_views_first,
                    full_viewability=Excluded.full_viewability,
                    iab_viewability=Excluded.iab_viewability,
                    completed_views_full=Excluded.completed_views_full,
                    be_event_count=Excluded.be_event_count,
                    budget_spent=Excluded.budget_spent,
                    booked_volume=Excluded.booked_volume
            ;
        """
        pg_hook.run(sql, True)
        increment_metric('airflow_data_pushed.increment', env_type,
                         FLOW_NAME, len(batch_report), [f"table:{REPORTS_TABLE_NAME}", f"client:{company_id}", f'run_number:{current_run_number}'])


def parse_report_data(report_data, query):
    line_items_data = (
        report_data.get('data', {}).get('getCampaignV3', {}).get("lineItems", {}).get("lineItems", [])
    )
    rows = []
    for line_item in line_items_data:
        for row in line_item.get('kpi', {}).get('nestedKpi', []):
            row['date'] = row.get('filterField', {}).get('value', "")[:10]
            row['line_item_id'] = line_item.get('id')
            row['booked_volume'] = line_item.get('bookedVolume')
            rows.append(row)
    df = pd.DataFrame(rows)
    df['campaign_id'] = query.campaign['external_campaign_id']
    df['provider'] = "theoutplay"
    df.rename(inplace=True,
              columns={
                'adClick': 'clicks',
                'adStart': 'impressions',
                'adThirdQuartile': 'completed_views_third',
                'adMidpoint': 'completed_views_mid',
                'adFirstQuartile': 'completed_views_first',
                'adFullViewability': 'full_viewability',
                'adViewable': 'iab_viewability',
                'adComplete': 'completed_views_full',
                'beEventCount': 'be_event_count',
                'budgetSpent': 'budget_spent'}
              )
    return df


def process_query(query, **kwargs):
    logging.info("PROCESSING SINGLE QUERY")
    access_token = get_access_token(query.profile['username'],
                                    query.profile['password'])
    if not access_token:
        msg = f"{FLOW_NAME} cannot get access_token for query={query}. Skip"
        logging.warning(msg)
        # this can happen when etl endpoint contains invalid or expired credentials
        return
    report_data = request_report(access_token, query, **kwargs)
    # saving reports in db
    report = parse_report_data(report_data, query)
    logging.info(f"Parsed report of len {len(report)}")
    if len(report) > 0:
        put_report_to_db(report, query.profile.get('company_id'), **kwargs)
        logging.info("Report processed")
    else:
        logging.warning("Empty report")
    if query.query_type == 'historical':
        # log historical to historical_log
        logging.info("Writing historical query to historical_log")
        log_hist_request(query)


with DAG(FLOW_NAME,
         description='theoutplay flow dag',
         schedule_interval=None,
         start_date=datetime.datetime(2022, 1, 1),
         params=DagConfigSetter(FLOW_NAME).get_params(),
         catchup=False,
         tags=['ingestion_flow']) as dag:
    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id='generate_queries',
        on_failure_callback=failure_callback,
        python_callable=generate_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="process_queries",
        on_failure_callback=failure_callback,
        python_callable=process_queries,
        op_kwargs={"queries_and_tokens": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    print_processing_summary = PythonOperator(
        task_id='print_processing_summary',
        on_failure_callback=failure_callback,
        python_callable=print_processing_summary,
        op_kwargs={"results": XComArg(t3)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> print_processing_summary >> cleanup_xcoms
