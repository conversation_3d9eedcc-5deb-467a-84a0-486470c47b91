import datetime
from datetime import date, timed<PERSON><PERSON>
from typing import List, Tuple
import json
import logging
import traceback
import time
from dataclasses import dataclass
from urllib import response

import jwt
import pandas as pd
import requests
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import Postg<PERSON>Hook
from myn_airflow_lib.datadog import increment_metric, init_datadog
from myn_airflow_lib.commons import (NIfiEndpointProcessor, failure_callback, _get_db_value, filter_out_standard_queries, set_execution_dates, iter_batch, set_config,
                                     enable_nifilog_check_based_on_conf, get_date_to_execute_on)
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
# GLOBALS
from myn_airflow_lib.constants import (
    BLACKLISTED_CLIENTS_LIST,
    GA_BAD_STATUSES,
    GA_V2_CAMPAIGN_TYPE,
    GA_V2_TABLE_NAME,
    GA_ADWORDS_TABLE_NAME,
    GA_SLEEP_TIME,
    GA_AlGORITHM,
    GA_REPORT_API_URL,
    GA_API_ENDPOINT,
    EXECUTOR_STABLE_NODES
)
from myn_airflow_lib.exceptions import MintFlowFailed, MintAuthFailed
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.sentry import capture_message
from sentry_sdk import capture_exception


FLOW_NAME = 'GOOGLE_ANALYTICS_V2'
pg_hook = PostgresHook("uri_reports")
pg_hook.log.disabled = True
env = EnvironmentResolver()
env_type = env.environment
state = {
    'dates': {},
    'access_tokens': {},
}
dates = state['dates']
access_tokens = state['access_tokens']
with open("static/ga_v2_metrics.json") as file:
    METRICS_JSON = json.load(file)
init_datadog(env.datadog_host, env.datadog_port)


@dataclass
class QueryData:
    body: dict
    query_type: str
    view_id: str
    query_type_nifi_id: int
    refresh_token: str
    date_from: str
    date_to: str
    company_id: int

    @property
    def campaign_detail(self):
        campaign_detail = {
            "view_id": self.view_id,
            "query_type_nifi_id": self.query_type_nifi_id
        }
        return json.dumps(campaign_detail, sort_keys=True)

    def to_dict(self):
        return {'body': self.body,
                'query_type': self.query_type,
                'view_id': self.view_id,
                'query_type_nifi_id': self.query_type_nifi_id,
                'refresh_token': self.refresh_token,
                'date_from': self.date_from,
                'date_to': self.date_to,
                'company_id': self.company_id}


def insert_into_nifilog(date_from, date_to, campaign_detail):
    sql_query = f"""
        INSERT INTO reports_nifilog(date_from, date_to, campaign_type, campaign_detail)
        VALUES ('{date_from}','{date_to}','{GA_V2_CAMPAIGN_TYPE}','{campaign_detail}')
    """
    pg_hook.run(sql_query)


@enable_nifilog_check_based_on_conf(state)
def does_nifilog_exist(query: QueryData):
    query = f"""
        SELECT id
        FROM reports_nifilog
        WHERE campaign_type = '{GA_V2_CAMPAIGN_TYPE}'
            AND campaign_detail = '{query.campaign_detail}'
        limit 1;
    """
    df = pg_hook.get_pandas_df(query)
    return not df.empty


def split_to_weekly_date_range(date_from: date, date_to: date) -> List[Tuple[date, date]]:
    # List to store the weekly date_from and date_to pairs
    weekly_date_ranges = []
    current_week_start = date_from
    current_week_end = current_week_start + timedelta(days=6)
    while current_week_end <= date_to:
        # Append the current week's date_from and date_to to the list
        weekly_date_ranges.append((current_week_start, current_week_end))
        # Move to the next week
        current_week_start = current_week_end + timedelta(days=1)
        current_week_end = current_week_start + timedelta(days=6)
    # Append the last week if it's not complete
    if current_week_start <= date_to:
        weekly_date_ranges.append((current_week_start, date_to))
    return weekly_date_ranges


def get_token_jwt():
    private_key_from_json = env.ga_private_key
    private_key_id_from_json = env.ga_private_key_id
    iat = time.time()
    exp = iat + 3600
    payload = {
        'iss': '<EMAIL>',
        'sub': '<EMAIL>',
        'aud': 'https://oauth2.googleapis.com/token',
        'scope': 'https://www.googleapis.com/auth/analytics.readonly',
        'iat': iat,
        'exp': exp
    }
    additional_headers = {
        'alg': GA_AlGORITHM,
        'typ': 'JWT',
        'kid': private_key_id_from_json
    }
    # jwt.register_algorithm('RS256', RSAAlgorithm(RSAAlgorithm.SHA256))
    signed_jwt = jwt.encode(payload, private_key_from_json,
                            headers=additional_headers, algorithm=GA_AlGORITHM)
    return signed_jwt


def _get_access_token_jwt_bearer():
    # this method is used when there is no refresh_token
    jwt_token = get_token_jwt()
    url = env.ga_audience
    payload = f'grant_type=urn%3Aietf%3Aparams%3Aoauth%3Agrant-type%3Ajwt-bearer&assertion={jwt_token}'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    return response


def _get_access_token_refresh_token(refresh_token):
    # this method is used when refresh_token is available for account
    url = env.ga_audience
    response = requests.request("POST", url, data={
        'grant_type': 'refresh_token',
        'client_id': env.ga_client_id,
        'client_secret': env.ga_client_secret,
        'refresh_token': refresh_token
    })
    return response


def get_access_token(refresh_token, force_update=False):
    existing_access_token = access_tokens.get(refresh_token)
    if existing_access_token is not None and not force_update:
        return existing_access_token
    
    if refresh_token is None:
        response = _get_access_token_jwt_bearer()
    else:
        response = _get_access_token_refresh_token(refresh_token)
    if 'access_token' not in response.text:
        msg = f"{FLOW_NAME} No token in response refresh_token=`{refresh_token[:20]}` {response.text}"
        raise MintAuthFailed(msg)
    new_access_token = response.json()['access_token']
    access_tokens[refresh_token] = new_access_token
    return new_access_token


def insert_data_into_reports(col_list, data_list, query_type, dimension_columns, db_table):
    on_conflict_clause = ", ".join(dimension_columns)
    col_list = ','.join(col_list)
    if query_type == 1:
        query_str = f"""
            INSERT INTO {db_table} ({col_list})
            VALUES {data_list}
            ON CONFLICT ({on_conflict_clause})
            DO UPDATE SET
                goal1_completions=Excluded.goal1_completions,goal2_completions=Excluded.goal2_completions,
                goal3_completions=Excluded.goal3_completions,goal4_completions=Excluded.goal4_completions,
                goal5_completions=Excluded.goal5_completions,goal6_completions=Excluded.goal6_completions,
                goal7_completions=Excluded.goal7_completions,goal8_completions=Excluded.goal8_completions,
                goal9_completions=Excluded.goal9_completions,goal10_completions=Excluded.goal10_completions;
        """
    elif query_type == 2:
        query_str = f"""
            INSERT INTO {db_table} ({col_list})
            VALUES {data_list}
            ON CONFLICT ({on_conflict_clause})
            DO UPDATE SET
                goal11_completions=Excluded.goal11_completions,goal12_completions=Excluded.goal12_completions,
                goal13_completions=Excluded.goal13_completions,goal14_completions=Excluded.goal14_completions,
                goal15_completions=Excluded.goal15_completions,goal16_completions=Excluded.goal16_completions,
                goal17_completions=Excluded.goal17_completions,goal18_completions=Excluded.goal18_completions,
                goal19_completions=Excluded.goal19_completions,goal20_completions=Excluded.goal20_completions;    
        """
    elif query_type == 3:
        query_str = f"""
            INSERT INTO {db_table} ({col_list})
            VALUES {data_list}
            ON CONFLICT ({on_conflict_clause})
            DO UPDATE SET
                transactions=Excluded.transactions,
                sessions=Excluded.sessions,
                adx_clicks=Excluded.adx_clicks;
        """
    pg_hook.run(query_str, True)


def create_query_body(view_id, metrics, start_date, end_date):
    return {
        "reportRequests": [
            {
                "viewId": str(view_id),
                "dateRanges": [
                    {
                        "startDate": start_date.strftime('%Y-%m-%d'),
                        "endDate": end_date.strftime('%Y-%m-%d')
                    }
                ],
                "pageSize": "10000",
                "metrics": metrics,
                "dimensions": [
                    {"name": "ga:Date"},
                    {"name": "ga:source"}, # utm_source
                    {"name": "ga:medium"}, # utm_medium
                    {"name": "ga:campaign"}, # utm_campaign
                    {"name": "ga:keyword"}, # utm_term
                    {"name": "ga:adContent"}, # utm_content
                ]
            },
            {
                "viewId": str(view_id),
                "dateRanges": [
                    {
                        "startDate": start_date.strftime('%Y-%m-%d'),
                        "endDate": end_date.strftime('%Y-%m-%d')
                    }
                ],
                "pageSize": "10000",
                "metrics": metrics,
                "dimensions": [
                    {"name": "ga:Date"},
                    {"name": "ga:adwordsCampaignID"},
                    {"name": "ga:adwordsAdGroupID"},
                    {"name": "ga:adwordsCreativeID"}, # adwords ad id
                ]
            }
        ]
    }


def create_queries_for_all_metric_types(df: pd.DataFrame,
                                        query_type: str,
                                        date_from: datetime.date,
                                        date_to: datetime.date):
    queries = []
    for _, row in df.iterrows():
        # GA API limits number of metrics you can get in single request
        # This is why metrics are split into `metric_types`
        for metrics_type in METRICS_JSON:
            query_type_nifi_id = metrics_type['query_type_nifi_id']
            metrics = metrics_type['metrics']
            body = create_query_body(row.view_id, metrics, date_from, date_to)
            queries.append(
                QueryData(
                    body=body,
                    query_type=query_type,
                    view_id=row.view_id,
                    query_type_nifi_id=query_type_nifi_id,
                    refresh_token=row.refresh_token,
                    date_from=date_from.strftime('%Y-%m-%d'),
                    date_to=date_to.strftime('%Y-%m-%d'),
                    company_id=row.company_id
                )
            )
    return queries


def hit_datintell_endpoint(**kwargs):
    logging.info("Getting data from internal GA API")
    processor = NIfiEndpointProcessor(GA_API_ENDPOINT, **kwargs)
    response = processor.apply_all()
    mocked_response = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    # use this mock for testing
    if mocked_response:
        return mocked_response
    return response


def generate_weekly_queries(df, **kwargs):
    date_from = get_date_to_execute_on(dates, **kwargs)
    date_from = datetime.datetime.strptime(date_from, '%Y%m%d').date()
    date_to = dates['YESTERDAY']

    # check date difference to find out whether we use sanity pulls or weekly pulls
    date_diff = (date_to - date_from).days

    # only for sanity pulls - if we have the last Saturday of the month
    if date_diff > 40:
        sanity_splitted_queries = []
        for i in split_to_weekly_date_range(date_from, date_to):
            sanity_splitted_queries.extend(create_queries_for_all_metric_types(df, 'weekly', i[0], i[1]))
        return sanity_splitted_queries

    # only for standard weekly pulls
    else:
        return create_queries_for_all_metric_types(df, 'weekly', date_from, date_to)


def generate_historical_queries(df):
    date_from = dates['YEAR_AGO']
    date_to = dates['YESTERDAY']
    all_queries = []
    for i in split_to_weekly_date_range(date_from, date_to):
        all_queries.extend(create_queries_for_all_metric_types(df, 'historical', i[0], i[1]))
    return [
        query for query in all_queries
        if not does_nifilog_exist(query)
    ]


def get_views(**kwargs):
    nifi_resp = hit_datintell_endpoint(**kwargs)
    views = {
        view["id"]: {
            "view_id": view["id"],
            "refresh_token": item.get("refresh_token"),
            "company_id": item.get("company_id")
        }
        for item in nifi_resp
        for view in item.get("views", [])
    }
    valid_creds_views = []
    for view in views.values():
        if view["refresh_token"] is not None:
            try:
                get_access_token(view["refresh_token"])
            except Exception as e:
                logging.error(f"Cannot obtain access_token for refresh_token='{view['refresh_token']}'."
                              f" All views with these creds will be skipped")
                capture_exception(e)
                continue
        valid_creds_views.append(view)
    return valid_creds_views


@set_config(state)
@set_execution_dates(dates)
def generate_requests(**kwargs):
    logging.info("Getting views")
    views = get_views(**kwargs)
    df = pd.DataFrame(views)
    weekly_queries = generate_weekly_queries(df, **kwargs)
    historical_queries = generate_historical_queries(df)
    logging.info(f"len(historical_queries) = {len(historical_queries)};")
    # dont include scheduled query if we are getting historical for this view_id
    # because last week data is included into last year data
    historical_count = len(historical_queries)
    all_queries = historical_queries
    historical_set = {(q.query_type_nifi_id, q.view_id) for q in historical_queries}
    weekly_count = 0
    for q in weekly_queries:
        if (q.query_type_nifi_id, q.view_id) not in historical_set:
            all_queries.append(q)
            weekly_count += 1
    total_count = weekly_count + historical_count
    logging.info(f"Generated {total_count} queries;"
                 f" {historical_count} historical and {weekly_count} weekly")
    return filter_out_standard_queries([
        query.to_dict() for query in all_queries
    ], **kwargs)


def send_ga_api_request(payload, refresh_token, query, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    logging.info(f"Going to send query request with data = {payload}")
    access_token = get_access_token(refresh_token)
    url = GA_REPORT_API_URL
    for _ in range(5):
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        try:
            response = requests.post(
                url, headers=headers, data=json.dumps(payload),
                timeout=60 * 10)
        except requests.exceptions.Timeout:
            logging.warning(f"Request timeout reached")
            continue
        if response.status_code in GA_BAD_STATUSES:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{query.query_type}", "request_status:error",
                                              f"client:{query.company_id}", f'run_number:{current_run_number}'])
            logging.warning(
                f"sent query request with data = {payload} \n"
                f"received {response.status_code}"
            )
            if response.status_code == 401:
                logging.warning('Getting new access_token...')
                access_token = get_access_token(refresh_token, force_update=True)
            else:
                logging.warning(
                    f"Going to sleep({GA_SLEEP_TIME}) then retry ..."
                )
                time.sleep(GA_SLEEP_TIME)
        else:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{query.query_type}", "request_status:success",
                                              f"client:{query.company_id}", f'run_number:{current_run_number}'])
            return response.json()
    raise MintFlowFailed(f"{FLOW_NAME} can't process query request for {payload} {query}")


def parse_response(response, query, disable_parsing_i_reports):
    payload = query.body
    reports = response.get('reports', [])
    data_values = []
    next_page = {}
    for i_report in range(len(reports)):
        if i_report in disable_parsing_i_reports:
            logging.info(f"Skip parsing i_report={i_report} since its already parsed")
            continue
        i_data_values = []
        response_data = reports[i_report]['data']
        if 'rows' in response_data:
            logging.info(f"Going to parse {len(response_data['rows'])} rows")
            for row in response_data['rows']:
                date = datetime.datetime.strptime(
                    row['dimensions'][0], "%Y%m%d").strftime("%Y-%m-%d")

                dimension_names = [
                    # get dimension names from request
                    dim['name'] for dim in payload['reportRequests'][i_report]['dimensions']
                ]
                dimension_values = row['dimensions']

                metrics_names = [
                    # get metric names from request
                    metric['expression'] for metric in payload['reportRequests'][i_report]['metrics']
                ]
                metrics_values = row['metrics'][0]['values']

                i_data_values.append({
                    "date": date,
                    "view_id": query.view_id,
                    "i_report": i_report,
                    **dict(zip(dimension_names[1:], dimension_values[1:])), # first skipped dimension is `date`
                    **dict(zip(metrics_names, metrics_values))
                })
            data_values.extend(i_data_values)
            logging.info(f"Parsed {len(i_data_values)} rows for report {i_report}."
                         f" Request total {len(data_values)}")
            next_page_token = reports[i_report].get('nextPageToken')
            if next_page_token:
                next_page[i_report] = next_page_token
    if not data_values:
        logging.info(f'No rows in API response. {response}')
    return data_values, next_page


def execute_query(query: QueryData, **kwargs):
    # executes request. paginates through all pages if needed
    res_df_values = []
    next_page = True
    disable_parsing_i_reports = []
    had_errors = False
    payload = query.body
    # accessing reports until rows exhausted
    while next_page:
        # exit api call loop if no next page
        try:
            response = send_ga_api_request(payload=payload,
                                           refresh_token=query.refresh_token,
                                           query=query, **kwargs)
            data_values, next_page = parse_response(response, query, disable_parsing_i_reports)
            res_df_values.extend(data_values)
            logging.info(f"Report total rows {len(res_df_values)}")
            if next_page:
                logging.info(f'Putting next_page parameter in next request ({len(next_page)} queries)')
                disable_parsing_i_reports = []
                for i_report in range(len(payload['reportRequests'])):
                    if i_report in next_page:
                        payload['reportRequests'][i_report]['pageToken'] = next_page[i_report]
                    else:
                        disable_parsing_i_reports.append(i_report)
            else:
                logging.info('Next page is absent for all i_reports!')
        except Exception as e:
            logging.error(f"Exception during execute_query {e}\n{traceback.format_exc()}")
            capture_exception(e)
            had_errors = True
            break
    return res_df_values, had_errors


def put_parsed_df_into_db(res_df, query_type_nifi_id):
    try:
        had_errors = False
        # rename columns to match DB table
        res_df = res_df.rename(columns={
            'ga:source': 'source',
            'ga:medium': 'medium',
            'ga:campaign': 'campaign',
            'ga:keyword': 'keyword',
            'ga:adContent': 'ad_content',
            'ga:adwordsCampaignID': 'adwords_campaign_id',
            'ga:adwordsAdGroupID': 'adwords_adgroup_id',
            'ga:adwordsCreativeID': 'adwords_ad_id',
            'ga:goal1Completions': 'goal1_completions',
            'ga:goal2Completions': 'goal2_completions',
            'ga:goal3Completions': 'goal3_completions',
            'ga:goal4Completions': 'goal4_completions',
            'ga:goal5Completions': 'goal5_completions',
            'ga:goal6Completions': 'goal6_completions',
            'ga:goal7Completions': 'goal7_completions',
            'ga:goal8Completions': 'goal8_completions',
            'ga:goal9Completions': 'goal9_completions',
            'ga:goal10Completions': 'goal10_completions',
            'ga:goal11Completions': 'goal11_completions',
            'ga:goal12Completions': 'goal12_completions',
            'ga:goal13Completions': 'goal13_completions',
            'ga:goal14Completions': 'goal14_completions',
            'ga:goal15Completions': 'goal15_completions',
            'ga:goal16Completions': 'goal16_completions',
            'ga:goal17Completions': 'goal17_completions',
            'ga:goal18Completions': 'goal18_completions',
            'ga:goal19Completions': 'goal19_completions',
            'ga:goal20Completions': 'goal20_completions',
            'ga:transactions': 'transactions',
            'ga:sessions': 'sessions',
            'ga:adxClicks': 'adx_clicks',
        })
        # Putting data into the db
        for i_report in range(2):
            if i_report == 0:
                dimension_columns = ["date", "view_id", "source", "medium",
                                    "campaign", "keyword", "ad_content"]
                db_table = GA_V2_TABLE_NAME
            else:
                dimension_columns = ["date", "view_id", "adwords_campaign_id",
                                    "adwords_adgroup_id", "adwords_ad_id"]
                db_table = GA_ADWORDS_TABLE_NAME
            col_names_dict = {
                1: dimension_columns + ["goal1_completions", "goal2_completions", "goal3_completions",
                                        "goal4_completions", "goal5_completions", "goal6_completions",
                                        "goal7_completions", "goal8_completions", "goal9_completions",
                                        "goal10_completions"],
                2: dimension_columns + ["goal11_completions", "goal12_completions", "goal13_completions",
                                        "goal14_completions", "goal15_completions", "goal16_completions",
                                        "goal17_completions", "goal18_completions", "goal19_completions",
                                        "goal20_completions"],
                3: dimension_columns + ["transactions", "sessions", "adx_clicks"]
            }
            db_col_names = col_names_dict[query_type_nifi_id]
            i_res_df = res_df[res_df.i_report == i_report][db_col_names]
            result_rows = list(i_res_df.itertuples(index=False, name=None))
            for batch_rows in iter_batch(result_rows, 1000):
                sql_batch_rows = [tuple(map(_get_db_value, row)) for row in batch_rows]
                values_str = ', '.join(map(lambda x: f'({", ".join(x)})', sql_batch_rows))
                insert_data_into_reports(
                    db_col_names,
                    values_str,
                    query_type_nifi_id,
                    dimension_columns,
                    db_table
                )
    except Exception as e:
        logging.error(f"Exception during put_parsed_df_into_db {e}\n{traceback.format_exc()}")
        capture_exception(e)
        had_errors = True
    return had_errors


def process_queries(queries_data, **kwargs):
    logging.info(f"Processing len(queries_data)={len(queries_data)};")
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    execution_results = {}
    queries = [
        QueryData(**query_data) for query_data in queries_data
    ]
    for i_query, query in enumerate(queries):
        logging.info(f"Processing query {i_query} / {len(queries)}. {query}")
        res_df_values, had_errors = execute_query(query, **kwargs)
        logging.info(f"res_df_values {len(res_df_values)}; had_errors {had_errors}")
        # process further only if rows present
        if res_df_values:
            res_df = pd.DataFrame(res_df_values)
            insert_had_errors = put_parsed_df_into_db(res_df, query.query_type_nifi_id)
            had_errors = had_errors or insert_had_errors
            increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, res_df.shape[0],
                             [f"table:{GA_V2_TABLE_NAME}", f"client:{query.company_id}", f'run_number:{current_run_number}'])
        else:
            logging.info(f"No rows to insert into DB")
        # log into nifilog table if its historical query
        if query.query_type == 'historical' and not had_errors:
            insert_into_nifilog(
                date_from=query.date_from,
                date_to=query.date_to,
                campaign_detail=query.campaign_detail
            )
        # update results summary
        status_key = 'errors' if had_errors else 'success'
        execution_results[status_key] = execution_results.get(status_key, 0) + 1
        execution_results[f"{status_key}_{query.query_type}"] \
            = execution_results.get(f"{status_key}_{query.query_type}", 0) + 1
    total_historical = execution_results.get('success_historical', 0) + execution_results.get('errors_historical', 0)
    total_weekly = execution_results.get('success_weekly', 0) + execution_results.get('errors_weekly', 0)
    logging.info(f"Processed {len(queries_data)} queries ({total_historical} historical & {total_weekly} weekly);\n"
                 + ("\n".join(f"{k}={execution_results[k]}"
                              for k in sorted(execution_results))))


with DAG(
    FLOW_NAME,
    description=f'Google Analytics V2 flow',
    schedule_interval=None,
    tags=['ingestion_flow'],
    start_date=datetime.datetime(2022, 1, 1),
    catchup=False,
) as dag:
    t1 = PythonOperator(
        python_callable=generate_requests,
        task_id="generate_requests",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id="process_queries",
        python_callable=process_queries,
        on_failure_callback=failure_callback,
        op_kwargs={"queries_data": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> cleanup_xcoms
