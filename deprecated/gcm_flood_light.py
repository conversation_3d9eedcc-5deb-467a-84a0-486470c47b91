import datetime
import io
import json
import logging
import os
import time
import traceback
from copy import deepcopy
from dataclasses import dataclass

import pandas as pd
import requests
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pandas import DataFrame

from myn_airflow_lib.commons import (
    failure_callback, set_execution_dates, _get_db_value, set_config,
    enable_nifilog_check_based_on_conf, get_date_to_execute_on
)
from myn_airflow_lib.constants import (GOOGLE_REPORTS_URL_FORMAT,
                                       GOOGLE_FILE_URL_FORMAT, CM360_NIFI_ENDPOINT, GOOGLE_RUN_QUERY_URL_FORMAT,
                                       EXECUTOR_STABLE_NODES, K8S_SPECS)
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.exceptions import MintFlowFailed
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.sentry import capture_message
from myn_airflow_lib.datadog import increment_metric, init_datadog

pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True
FLOW_NAME = 'GCM_FLOODLIGHT'
env_resolver = EnvironmentResolver()
state = {
    'dates': {}
}
dates = state['dates']
CAMPAIGN_TYPE = 'gcm_floodlight'

env_type = env_resolver.environment
REPORTS_TABLE_NAME = 'campaign_manager_floodlight_base_report'

init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
access_tokens = {}


@dataclass
class QueryData:
    data: dict
    type: str
    profile: dict
    setting: dict
    report_id: int = None

    @property
    def campaign_detail(self):
        campaign_detail = {
            "advertiser_ids": self.profile['advertiser_ids'],
            "campaigns": self.setting['campaigns']
        }
        return json.dumps(campaign_detail, sort_keys=True)

    @property
    def client_id(self):
        return self.profile['client_id']

    @property
    def client_secret(self):
        return self.profile['client_secret']

    @property
    def refresh_token(self):
        return self.profile['refresh_token']

    def to_dict(self):
        return {'data': self.data,
                'type': self.type,
                'profile': self.profile,
                'setting': self.setting,
                'report_id': self.report_id}


def filter_profiles(profile):
    return all([profile['advertiser_ids'],
                profile['profile_id'],
                profile['settings'],
                profile['company_id']])


def get_profiles(**kwargs):
    logging.info(f'Environments are {json.dumps(dict(os.environ))}')
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    nifi_response = requests.get(CM360_NIFI_ENDPOINT)
    profiles = nifi_response.json()
    # use this mock for testing, stage profiles don't match with campaigns on CM360
    if config_mock:
        profiles = config_mock
    # use xcom only for small amount of data, not big-ass dataframes
    profiles = list(filter(filter_profiles, profiles))
    return profiles


def get_advertiser_and_fl_conf(dim_filters):
    result = {}
    for filtr in dim_filters:
        if filtr['dimensionName'] in ['advertiserId', 'floodlightConfigId']:
            dim_name = filtr['dimensionName']
            result[dim_name] = filtr['value']
    return result


def get_campaign_filter(dim_filters):
    result = []
    for filtr in dim_filters:
        if filtr['dimensionName'] == 'campaign':
            result.append(filtr)
    return result


def split_campaigns(campaigns: list):
    chunk_size = 500
    return [campaigns[i:i + chunk_size] for i in range(0, len(campaigns), chunk_size)]


def optimize_hist_requests(hist_requests):
    """
    this function takes all the historical queries and process them in a way
    to produce the least amount of needed requests to cover all of them
    it uses dimension filters
    finds unique combinations of adv_id and fl_configs
    and joins campaigns across all the same adv_id and fl_configs
    """
    optimized_list = []
    found_dims = []
    for req in hist_requests:
        dims = get_advertiser_and_fl_conf(
            req['data']['floodlightCriteria']['dimensionFilters'])
        if dims in found_dims:
            for request in optimized_list:
                if get_advertiser_and_fl_conf(
                request['data']['floodlightCriteria']['dimensionFilters']) == dims:
                    # get campaigns to add to the optimized request
                    campaigns_to_add = get_campaign_filter(req['data']['floodlightCriteria']['dimensionFilters'])
                    request['data']['floodlightCriteria']['dimensionFilters']+= campaigns_to_add
        else:
            found_dims.append(dims)
            optimized_list.append(req)

    # we need to split requests if we exceed 1000 campaigns in the same request
    splited_and_optimized = []
    for query in optimized_list:
        if len(query['data']['floodlightCriteria']['dimensionFilters']) > 1000:
            print(len(query['data']['floodlightCriteria']['dimensionFilters']))
            # get adv id and fl_conf
            main_dims = []
            campaigns = []
            for filtr in query['data']['floodlightCriteria']['dimensionFilters']:
                    if filtr['dimensionName'] in ['advertiserId', 'floodlightConfigId']:
                        main_dims.append(filtr)
                    else:
                        campaigns.append(filtr)
            campaigns = split_campaigns(campaigns)
            for campaign_collection in campaigns:
                temp_query = query
                temp_query['data']['floodlightCriteria']['dimensionFilters'] = main_dims+campaign_collection
                splited_and_optimized.append(temp_query)
        else:
            splited_and_optimized.append(query)

    return splited_and_optimized


@set_config(state)
@set_execution_dates(dates)
def get_google_queries(profiles, **kwargs):
    queries = []
    hist_queries = []
    for profile in profiles:
        settings = profile['settings']
        for setting in settings:
            historical_queries = get_historical_query(profile, setting)
            if historical_queries is not None:
                hist_queries += historical_queries
            standard_queries = get_standard_query(profile, setting, **kwargs)
        if standard_queries:
            queries += standard_queries
    hist_queries = optimize_hist_requests(list(map(lambda x: x.to_dict(), hist_queries)))
    dict_queries = list(map(lambda x: x.to_dict(), queries)) + hist_queries
    logging.info(f'Going to process {len(dict_queries)} queries')
    return dict_queries


def get_setting_dimensions(setting, floodlight_config_id):
    dimensions = []
    dimension = {
            "kind": "dfareporting#dimensionValue",
            "dimensionName": "floodlightConfigId",
            "value": floodlight_config_id
            }
    dimensions.append(dimension)
    for c in setting['campaigns']:
        if c:
            dimension = {'dimensionName': 'campaign',
                         'kind': "dfareporting#dimensionValue",
                         'id': c}
            dimensions.append(dimension)
    return dimensions


def get_historical_query(profile, setting):
    hist_req = []
    if 'floodlight_activity_configs' in setting.keys() and setting['floodlight_activity_configs'] is not None:
        for floodlight_config_id in setting['floodlight_activity_configs']:
            dimensions = get_setting_dimensions(setting, floodlight_config_id)
            two_month_ago = dates['TODAY'] - datetime.timedelta(1 * 59)

            hist_req_data = get_setting_historical_request(two_month_ago.strftime('%Y-%m-%d'),
                                                           dates['TODAY'].strftime('%Y-%m-%d'),
                                                           dimensions)
            hist_request = QueryData(hist_req_data, 'historical', profile, setting)

            if not does_nifilog_exist(hist_request) and len(hist_request.setting['campaigns']) != 0:
                hist_req.append(hist_request)
    return hist_req


def get_campaign_detail(req: QueryData):
    return req.campaign_detail


@enable_nifilog_check_based_on_conf(state)
def does_nifilog_exist(hist_request: QueryData):
    campaign_detail = get_campaign_detail(hist_request)
    query = f"""
    select id as nifilog_id
    from reports_nifilog
    where campaign_type = '{CAMPAIGN_TYPE}'
      and campaign_detail = '{campaign_detail}' limit 1;
    """
    df = pg_hook.get_pandas_df(query)
    return not df.empty


def get_report_template():
    with open('static/gcm_floodlight.json') as file:
        report_template = json.load(file)
    return report_template


def get_setting_historical_request(date_from, date_to, dimensions) -> dict:
    report_template = get_report_template()
    report_template['name'] = f"Custom_dates"
    report_template['floodlightCriteria']['dateRange'] = {
        "kind": "dfareporting#dateRange",
        "startDate": date_from,
        "endDate": date_to
    }
    report_template['floodlightCriteria']['dimensionFilters'] = dimensions
    return report_template


def get_standard_query(profile, setting, **kwargs):
    sch_req = []
    if 'floodlight_activity_configs' in setting.keys() and setting['floodlight_activity_configs'] is not None:
        for floodlight_config_id in setting['floodlight_activity_configs']:
            # we are using only campaigns as filter, but in different settings we can have same campaigns
            settings_campaigns = list(set([campaign for setting in profile['settings'] for campaign in setting['campaigns']]))
            sorted_campaigns = sorted(settings_campaigns)
            if len(sorted_campaigns) != 0:
                setting = {'campaigns': sorted_campaigns}
                dimensions = get_setting_dimensions(setting, floodlight_config_id)
                standard_req_data = get_standard_requests_body(dimensions, **kwargs)
                standard_query = QueryData(standard_req_data, 'standard', profile, setting)
                sch_req.append(standard_query)
    return sch_req


def get_standard_requests_body(dimensions, **kwargs):
    report_template = get_report_template()
    report_template['name'] = f"Yesterday"
    today = dates['TODAY'].strftime('%Y-%m-%d')
    week_ago = get_date_to_execute_on(dates, date_format='%Y-%m-%d', **kwargs)
    report_template['floodlightCriteria']['dateRange'] = {
        "kind": "dfareporting#dateRange",
        "startDate": week_ago,
        "endDate": today
    }
    report_template['floodlightCriteria']['dimensionFilters'] = dimensions
    return report_template


def generate_google_access_token(client_id, client_secret, refresh_token, force_update=False):
    if refresh_token in access_tokens and not force_update:
        return access_tokens[refresh_token]
    response = requests.post('https://accounts.google.com/o/oauth2/token', json={'client_id': client_id,
                                                                                 'client_secret': client_secret,
                                                                                 'refresh_token': refresh_token,
                                                                                 'grant_type': 'refresh_token'})
    logging.info('Access token resp received')
    if 'access_token' not in response.json():
        logging.error(f"Access token requested but not received. Response `{response.text}`")
        return None
    access_tokens[refresh_token] = response.json()['access_token']
    return access_tokens[refresh_token]


def send_google_request(url: str, method: str, token_creds: dict, json_data: dict = None, headers: dict = None,
                        report_type: str = None, company_id: int = None):
    bad_statuses = [502, 504, 503, 429, 500, 403, 401]
    access_token = generate_google_access_token(**token_creds)
    request_data = {'headers': {'Authorization': f"Bearer {access_token}"},
                    'url': url,
                    'method': method}
    if json_data:
        request_data['json'] = json_data
    if headers:
        request_data['headers'].update(headers)
    exc = None
    for tries_made in range(5):
        try:
            response = requests.request(**request_data)
        except Exception as e:
            logging.error(f"Network exception during request execution. {e}. {request_data}")
            exc = e
            time.sleep(env_resolver.gcm2_wait_sleep)
            continue
        if response.status_code in bad_statuses:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:error", f"client:{company_id}"])
            logging.warning(f'send request with data = {json.dumps(request_data)} \n'
                            f'received {response.status_code} with {response.text}')
            if response.status_code == 401:
                logging.info('need new access token')
                access_token_retry = generate_google_access_token(**token_creds, force_update=True)
                request_data['headers']['Authorization'] = f"Bearer {access_token_retry}"
            time.sleep(env_resolver.gcm2_wait_sleep)
        else:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:success", f"client:{company_id}"])
            return response
    data_to_log = deepcopy(request_data)
    data_to_log.pop('headers')
    msg = f'Cant process the request (tries_made={tries_made + 1}) {json.dumps(data_to_log)}. Exception `{exc}`'
    raise MintFlowFailed(msg)


def create_report(url, data, report_type, token_creds, company_id):
    response = send_google_request(url, 'POST', token_creds, data, report_type=report_type, company_id=company_id)
    if response.status_code != 200:
        logging.info(response.text)
    report_id = response.json()['id']
    return report_id


def create_file(url, report_type, token_creds, company_id):
    response = send_google_request(url, 'POST', token_creds, report_type=report_type, company_id=company_id)
    file_id = response.json()['id']
    return file_id


def get_file_download_url(file_url, report_type, token_creds, company_id):
    for _ in range(100):
        response = send_google_request(file_url, 'GET', token_creds, report_type=report_type, company_id=company_id)
        json_response = response.json()
        status = json_response['status']
        if status == 'REPORT_AVAILABLE':
            return json_response['urls']['apiUrl']
        if status == 'FAILED':
            break
        time.sleep(env_resolver.gcm2_wait_sleep * (3 if report_type == 'historical' else 1))
    message = f'Can\'t load file for file_url={file_url}'
    logging.error(message)
    capture_message(message)
    raise Exception(message)


def put_report_to_db(report: DataFrame):
    columns_order = ['date', 'floodlight_config_id', 'advertiser_id', 'placement_id', 'activity_id',
                     'activity_name', 'campaign_id', 'campaign_name', 'site_id', 'site_name',
                     'total_conversions_revenue', 'transaction_count', 'total_conversions']
    report = report[columns_order]
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""
    INSERT INTO {REPORTS_TABLE_NAME}({columns}) VALUES {values_str} 
    ON CONFLICT(date, activity_id, campaign_id, site_id, placement_id, advertiser_id, floodlight_config_id) 
    DO UPDATE SET activity_name = Excluded.activity_name, campaign_name = Excluded.campaign_name, 
    site_name = Excluded.site_name, total_conversions = Excluded.total_conversions,
     total_conversions_revenue = Excluded.total_conversions_revenue, transaction_count = Excluded.transaction_count; """

    pg_hook.run(query, True)


def log_hist_req(query: QueryData):
    campaign_detail = get_campaign_detail(query)
    date_from = query.data['floodlightCriteria']['dateRange']['startDate']
    date_to = query.data['floodlightCriteria']['dateRange']['endDate']
    query = """
        INSERT INTO reports_nifilog(date_from, date_to, campaign_type, campaign_detail)
        Values (%(date_from)s, %(date_to)s, %(campaign_type)s, %(campaign_detail)s)
        """
    data = {'date_from': date_from,
            'date_to': date_to,
            'campaign_type': CAMPAIGN_TYPE,
            'campaign_detail': campaign_detail}
    pg_hook.run(query, True, data)


def run_query(query):
    logging.debug('start load historical query')
    report_url = GOOGLE_REPORTS_URL_FORMAT.format(user_id=query.profile['profile_id'])
    token_creds = {'client_id': query.client_id,
                   'client_secret': query.client_secret,
                   'refresh_token': query.refresh_token}
    report_id = create_report(report_url, query.data, query.type, token_creds, query.profile.get('company_id'))
    logging.debug(f'report_id is {report_id}')
    file_id = create_file(GOOGLE_RUN_QUERY_URL_FORMAT.format(user_id=query.profile['profile_id'],
                                                             report_id=report_id,),
                          query.type,
                          token_creds,
                          query.profile.get('company_id'))
    logging.debug(f'file_id is {file_id}')
    return file_id, report_id


def fix_report(df: DataFrame) -> DataFrame:
    metric_cols = ['total_conversions', 'impressions', 'clicks',
                   'rich_media_video_completions', 'active_view_viewable_impressions', 'click_through_conversions',
                   'view_through_conversions', 'rich_media_video_plays', 'rich_media_video_first_quartile_completes',
                   'rich_media_video_midpoints', 'rich_media_video_third_quartile_completes']
    fillna_columns = ['advertiser_id', 'floodlight_config_id', 'activity_id', 'campaign_id', 'site_id',
                      'total_conversions', 'impressions',
                      'clicks', 'rich_media_video_completions', 'active_view_viewable_impressions',
                      'rich_media_video_plays', 'rich_media_video_first_quartile_completes',
                      'rich_media_video_midpoints', 'rich_media_video_third_quartile_completes'
                      ]

    df.columns = ['activity_id', 'campaign_id', 'site_id', 'date', 'advertiser_id', 'floodlight_config_id',
                  'activity_name',
                  'campaign_name', 'site_name', 'placement_id'] + metric_cols

    df[metric_cols] = df[metric_cols].replace(-1, 0)
    df[fillna_columns] = df[fillna_columns].fillna(0)
    df['activity_id'] = df['activity_id'].replace('(not set)', 0)
    return df


def download_report(url, report_type, token_creds, company_id):
    report_response = send_google_request(url, 'GET', token_creds, report_type=report_type, company_id=company_id)
    text = report_response.text.split('Report Fields')[1:][0]
    if len(text) > 1:
        text = text.split('Grand Total')[0].strip()
    else:
        text = report_response.text.split('Campi del rapporto')[1:][0].split('Totale complessivo')[0].strip()

    text = text.replace('Date', 'date', 1) \
        .replace('Data', 'date', 1) \
        .replace('Activity ID', 'activity_id', 1) \
        .replace('Campaign ID', 'campaign_id', 1) \
        .replace('ID attività', 'activity_id', 1) \
        .replace('ID campagna', 'campaign_id', 1) \
        .replace('Site ID (DCM)', 'site_id', 1) \
        .replace('Site (DCM)', 'site_name', 1) \
        .replace('ID sito (DCM)', 'site_id', 1) \
        .replace('Sito (DCM)', 'site_name', 1) \
        .replace('Site ID (CM360)', 'site_id', 1) \
        .replace('Site (CM360)', 'site_name', 1) \
        .replace('ID sito (CM360)', 'site_id', 1) \
        .replace('Sito (CM360)', 'site_name', 1) \
        .replace('Site ID \\(DCM\\)', 'site_id', 1) \
        .replace('Site \\(DCM\\)', 'site_name', 1) \
        .replace('ID sito \\(DCM\\)', 'site_id', 1) \
        .replace('Sito \\(DCM\\)', 'site_name', 1) \
        .replace('Site ID \\(CM360\\)', 'site_id', 1) \
        .replace('Site \\(CM360\\)', 'site_name', 1) \
        .replace('ID sito \\(CM360\\)', 'site_id', 1) \
        .replace('Sito \\(CM360\\)', 'site_name', 1) \
        .replace('Placement ID', 'placement_id', 1) \
        .replace('ID posizionamento', 'placement_id', 1) \
        .replace('Advertiser ID', 'advertiser_id', 1) \
        .replace('Floodlight Configuration', 'floodlight_config_id', 1) \
        .replace('Activity', 'activity_name', 1) \
        .replace('ID inserzionista', 'advertiser_id', 1) \
        .replace('Configurazione Floodlight', 'floodlight_config_id', 1) \
        .replace('Attività', 'activity_name', 1) \
        .replace('Campagna', 'campaign_name', 1) \
        .replace('Campaign', 'campaign_name', 1) \
        .replace('Total Conversions', 'total_conversions', 1) \
        .replace('Conversioni totali', 'total_conversions', 1) \
        .replace('Entrate totali', 'total_conversions_revenue', 1) \
        .replace('Total Revenue', 'total_conversions_revenue', 1) \
        .replace('Conteggio transazioni', 'transaction_count', 1) \
        .replace('Transaction Count', 'transaction_count', 1) \
        .replace("'", ' ', 1)
    df = pd.read_csv(io.StringIO(text), sep=",")
    df = df.replace('(not set)', None)

    return df


def run_single_report(query_data):
    query = QueryData(**query_data)
    try:
        file_id, report_id = run_query(query)
    except Exception as e:
        capture_message(f'{FLOW_NAME} Failed to run query. {str(e)} {json.dumps(query_data)}')
        raise
    return [file_id, report_id, query_data, query.type]


@set_execution_dates(dates)
def run_reports(queries, **kwargs):
    result = []
    errors = {"historical": 0,
              "standard": 0}

    for query_data in queries:
        try:
            query_result = run_single_report(query_data)
            result.append(query_result)
        except Exception as e:
            errors.setdefault(query_data['type'], 0)
            errors[query_data['type']] += 1
            error_message = f'had an error {e} on {json.dumps(query_data)} item \n' + traceback.format_exc()
            logging.error(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x["type"] == error_type, queries)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')
    return result


def load_single_report(file_id, report_id, query_data, report_type):
    logging.info(f"load_reports {file_id} {report_id} {report_type} {query_data}")
    query = QueryData(**query_data)
    file_url_endpoint = GOOGLE_FILE_URL_FORMAT.format(user_id=query.profile['profile_id'],
                                                      report_id=report_id,
                                                      file_id=file_id)
    logging.debug('url ' + file_url_endpoint)
    company_id = query.profile.get('company_id')
    token_creds = {'client_id': query.client_id,
                   'client_secret': query.client_secret,
                   'refresh_token': query.refresh_token}
    url = get_file_download_url(file_url_endpoint, report_type, token_creds, company_id)
    logging.debug('found file url')
    report = download_report(url, report_type, token_creds, company_id)
    if report.empty:
        message = f'report {url} is empty with type {report_type}'
        logging.warning(message)
    else:
        put_report_to_db(report)
        increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                         [f"table:{REPORTS_TABLE_NAME}", f"client:{company_id}"])
        logging.debug('report is pushed')
    if report_type == 'historical':
        log_hist_req(query)
        logging.debug('log is pushed')


@set_execution_dates(dates)
def load_reports(batch, **kwargs):
    errors = {"historical": 0,
              "standard": 0}
    for item in batch:
        try:
            load_single_report(*item)
        except Exception:
            errors.setdefault(item[3], 0)
            errors[item[3]] += 1
            error_message = f'had an error on {item} item \n' + traceback.format_exc()
            logging.error(error_message)
            capture_message(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x[3] == error_type, batch)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')


with DAG(FLOW_NAME,
         description='gcm2 flow dag',
         schedule_interval=None,
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False,
         tags=['ingestion_flow']) as dag:
    t1 = PythonOperator(
        task_id='get_profiles',
        python_callable=get_profiles,
        on_failure_callback=failure_callback,
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id="get_google_queries",
        python_callable=get_google_queries,
        on_failure_callback=failure_callback,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="run_reports",
        python_callable=run_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"queries": XComArg(t2)},
        executor_config=K8S_SPECS
    )
    t4 = PythonOperator(
        task_id="load_reports",
        python_callable=load_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"batch": XComArg(t3)},
        executor_config=K8S_SPECS
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
    )

    t1 >> t2 >> t3 >> t4 >> cleanup_xcoms
