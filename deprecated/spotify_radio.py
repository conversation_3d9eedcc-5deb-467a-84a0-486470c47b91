import datetime
import json
import re
import logging
from io import String<PERSON>
from typing import List, <PERSON>Return

from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
import pandas as pd
import numpy as np
from imap_tools import MailMessage, MailAttachment, EmailAddress, AND, A, MailBox

from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.sentry import capture_message
from myn_airflow_lib.commons import failure_callback
from myn_airflow_lib.datadog import increment_metric, init_datadog


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
FLOW_NAME = 'SPOTIFY_RADIO'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
REPORTS_TABLE_NAME = 'reports_spotify_radio_performance'
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)


def _init_mailbox() -> MailBox:
    """Initialize gmail mailbox"""
    return MailBox('imap.gmail.com').login(env_resolver.common_reports_email,
                                           env_resolver.common_reports_password,
                                           'SPOTIFY_RADIO_REPORTS')


def _get_db_value(value):
    if isinstance(value, (dict, list)):
        return f"'{json.dumps(value)}'"
    elif isinstance(value, str):
        return f"'{value}'"
    elif value is None:
        return 'NULL'
    else:
        return str(value)


def _extract_client_id(email):
    if isinstance(email, tuple):
        email = email[0]
    if isinstance(email, EmailAddress):
        email = email.email
    client_id_match = re.match(r'[^+@]+\+(\d+)@[^+@]+', email)
    return None if not client_id_match else client_id_match.group(1)


def validate_message_has_client_id(message: MailMessage) -> bool:
    client_id = _extract_client_id(message.to)
    if client_id is None:
        # unable to extract client id. should log error
        capture_message(f"{FLOW_NAME} | Unable to extract client id. message.to='{message.to}'")
        return False
    return True


def validate_attachment_is_csv(attachment: MailAttachment) -> bool:
    if not attachment.filename.lower().endswith('.csv'):
        # log error
        capture_message(
            f"{FLOW_NAME} | Email message has non-csv attachment. Skip this attachment."
            f" attachment.filename={attachment.filename}")
        return False
    return True


def process_attachment(message: MailMessage, attachment: MailAttachment) -> None:
    logging.info(
        f"Processing message attachment"
        f" message.id={message.uid} attachment.filename={attachment.filename}")
    content = attachment.payload.decode()
    # skip first 2 lines
    csv_str = content.split('\n', 2)[2].rstrip()
    df = pd.read_csv(StringIO(csv_str))

    df['report_received_at'] = message.date.isoformat()
    df['company_id'] = _extract_client_id(message.to)
    df['ad_set_id'] = df['Ad Set ID']
    today = datetime.datetime.utcnow().strftime("%Y-%m-%d")
    df['date'] = df['Ad Set End Date'].str.slice(stop=10).apply(
        lambda end_date: min(end_date, today)
    )
    df['impressions'] = df['Impressions'].astype(float).astype(int)
    df['clicks'] = df['Clicks'].astype(float).astype(int)
    spend_cols = [c for c in df.columns if re.match(r'Spend \([A-Z]+\)', c)]
    if len(spend_cols) != 1:
        capture_message(
            f"{FLOW_NAME} | Could not determine spend column."
            f" csv columns {df.columns}; spend columns {spend_cols}"
        )
        df['spend'] = None
    else:
        df['spend'] = df[spend_cols[0]].str.replace(",", "").astype(float)
    ceil = lambda x: np.ceil(x).astype(int)
    df['audio_25_completion'] = ceil(df['impressions'] * df['Ad played to: 25%'].str.rstrip("%").astype(float) / 100)
    df['audio_50_completion'] = ceil(df['impressions'] * df['Ad played to: 50%'].str.rstrip("%").astype(float) / 100)
    df['audio_75_completion'] = ceil(df['impressions'] * df['Ad played to: 75%'].str.rstrip("%").astype(float) / 100)
    df['audio_100_completion'] = ceil(df['impressions'] * df['Ad played to: 100%'].str.rstrip("%").astype(float) / 100)

    df = df[[
        'company_id', 'ad_set_id', 'report_received_at', 'date', 'impressions', 'clicks',
        'spend', 'audio_25_completion', 'audio_50_completion',
        'audio_75_completion', 'audio_100_completion'
    ]]
    _execute_sql_query(df.to_json())
    increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, df.shape[0],
                     [f"table:{REPORTS_TABLE_NAME}"])


def _execute_sql_query(report_data: json) -> NoReturn:
    """Prepare and runs sql UPSERT query"""
    logging.info("Starting pushing to DB")
    report = pd.DataFrame.from_dict(json.loads(report_data))
    if len(report) == 0:
        logging.info("Empty csv. Skip")
        return

    columns = ', '.join(report.columns)

    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    upd_expressions = [
        f"{col} = CASE WHEN Excluded.report_received_at >= {REPORTS_TABLE_NAME}.report_received_at THEN Excluded.{col} ELSE {REPORTS_TABLE_NAME}.{col} END"
        for col in ["impressions", "clicks", "spend", "report_received_at", "date",
                    "audio_25_completion", "audio_50_completion",
                    "audio_75_completion", "audio_100_completion"]
    ]
    query = f"""
        INSERT INTO {REPORTS_TABLE_NAME} ({columns})
        VALUES {values_str}
        ON CONFLICT (company_id,ad_set_id)
        DO UPDATE SET
            {", ".join(upd_expressions)}
    """
    pg_hook.run(query, True)


def get_message_ids() -> List:
    """Gets all unseen message ids"""
    logging.info("Starting retrieving emails ids")
    with _init_mailbox() as mailbox:
        mailbox_fetched = mailbox.fetch(criteria=AND(seen=False), bulk=True)
        return [[message.uid] for message in mailbox_fetched]


def process_email(msg_id: str) -> NoReturn:
    """Retrieves, process and push emails"""
    logging.info("Starting parsing emails")
    with _init_mailbox() as mailbox:
        mailbox_message = mailbox.fetch(A(uid=msg_id))
        for message in mailbox_message:
            if not validate_message_has_client_id(message):
                logging.info(
                    f"Message skipped. Cannot extract client_id from recipient."
                    f" message.id={message.uid} message.to='{message.to}'")
                continue
            for attachment in message.attachments:
                if not validate_attachment_is_csv(attachment):
                    logging.info(
                        f"Attachment skipped. Not csv extension."
                        f" message.id={message.uid}"
                        f" attachment.filename={attachment.filename}")
                    continue
                process_attachment(message, attachment)


with DAG(FLOW_NAME,
         description='spotify radio flow dag',
         schedule_interval=None,
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False,
         tags=['ingestion_flow'],
         max_active_runs=1,
         on_failure_callback=failure_callback) as dag:
    t1 = PythonOperator(
        task_id='get_message_ids',
        python_callable=get_message_ids,
        on_failure_callback=failure_callback
    )

    t2 = PythonOperator.partial(
        task_id='process_email',
        on_failure_callback=failure_callback,
        python_callable=process_email,
    ).expand(op_args=XComArg(t1))

    t1 >> t2
