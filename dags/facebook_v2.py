import datetime

import json
import logging
import time
import re
from dataclasses import dataclass
from typing import List, Optional, Union
from urllib import response
import urllib.parse
import traceback
import pandas as pd
import requests
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import Postg<PERSON>Hook
from pandas import DataFrame
from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.exceptions import <PERSON><PERSON><PERSON>Failed

from myn_airflow_lib.constants import (FACEBOOK_ETL_ENDPOINT, FACEBOOK_DOMAIN, CLOSED_FB_ACCOUNTS_LIST,
                                       FACEBOOK_BAD_REQUEST_RETRY_WAIT, FACEBOOK_MAX_DAYS_REQUESTED_IN_A_QUERY,
                                       FACEBOOK_PAGE_SIZE, FACEBOOK_MAX_WAIT_POLL_REPORT, FACEBOOK_CUSTOM_CONVERSIONS_ENDPOINT,
                                       FACEBOOK_QUOTA_REACHED_RETRIES, FACEBOOK_QUOTA_REACHED_RETRY_WAIT,
                                       FACEBOOK_BAD_REQUEST_RETRIES, FACEBOOK_STANDARD_QUERIES_BATCH_SIZE,
                                       FACEBOOK_HISTORICAL_QUERIES_BATCH_SIZE, EXECUTOR_STABLE_NODES,
                                       HISTORICAL_LOG_TABLE, FACEBOOK_DEFAULT_ACTION_TYPES)
from myn_airflow_lib.commons import (EtlEndpointProcessorWithParams, failure_callback, filter_out_standard_queries,
                                     set_execution_dates, _get_db_value, iter_batch, set_config,
                                     enable_hist_log_check_based_on_conf, get_historical_date_from,
                                     get_date_to_execute_on)
from myn_airflow_lib.datadog import increment_metric, init_datadog
from myn_airflow_lib.custom_operators import CleanupXcomsOperator

from myn_airflow_lib.resolvers import EnvironmentResolver

pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True
FLOW_NAME = 'facebook_v2'
CAMPAIGN_TYPE = 'facebook_v2'
DATADOG_AIRFLOW_PUSHED_METRIC = 'airflow_data_pushed.increment'
REPORTS_CONV_TABLE_NAME = 'meta_conversions_report'
REPORTS_CONV_MAP_TABLE_NAME = 'meta_conversions_mapping'
REPORTS_TABLE_NAME = 'social_campaigns_reports_v3'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
state = {
    'dates': {},
    'failed_queries': {},
    'api_account_currencies': {},
    'db_account_currencies': {},
    'failed_access_tokens': set()
}
dates = state['dates']
historical_log_cache = None
last_fb_request = 0
failed_queries = state['failed_queries']


def get_cols_to_insert(report):
    columns = ",".join(['"{0}"'.format(col) for col in report.columns])
    return columns

@dataclass
class QueryData:
    data: dict
    type: str
    profile: dict
    campaign: dict
    date_from: Union[str, datetime.date]
    date_to: Union[str, datetime.date]
    report_id: int = None
    report_id_created_at: float = None

    def __post_init__(self):
        # need to ensure deserialization from json
        if isinstance(self.date_from, str):
            self.date_from = datetime.datetime.strptime(self.date_from, '%Y-%m-%d').date()
        if isinstance(self.date_to, str):
            self.date_to = datetime.datetime.strptime(self.date_to, '%Y-%m-%d').date()

    @property
    def company_id(self):
        return self.profile['company_id']

    @property
    def campaign_detail(self):
        return json.dumps({
                           "account_id": self.profile['account_id'],
                           "campaign_id": self.campaign['external_campaign_id']
                           },
                          sort_keys=True)

    def to_dict(self):
        profile = {**self.profile}
        profile.pop('campaigns', None)
        return {
            'data': self.data,
            'type': self.type,
            'profile': profile,
            'campaign': self.campaign,
            'date_from': self.date_from.strftime('%Y-%m-%d'),
            'date_to': self.date_to.strftime('%Y-%m-%d'),
            'report_id': self.report_id,
            'report_id_created_at': self.report_id_created_at
        }


def filter_profiles(profile):
    return (
        profile.get('account_id') is not None
        and profile.get('account_id') not in CLOSED_FB_ACCOUNTS_LIST
        and profile.get('campaigns')
    )


def get_profiles(**kwargs):
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    processor = EtlEndpointProcessorWithParams(FACEBOOK_ETL_ENDPOINT, **kwargs)
    if config_mock:
        logging.info('endpoing mock was found using that...')
        raw_profiles = config_mock
    else:
        raw_profiles = processor.apply_all()
    return [profile for profile in raw_profiles if filter_profiles(profile)]


def create_query_body(profile, campaign, date_from, date_to):
    fields = "date_start,account_id,campaign_id,adset_id,unique_inline_link_clicks,impressions,frequency," \
             "cpc,cpm,cpp,ctr,spend,clicks,unique_clicks,ad_id,actions,action_values,social_spend,reach,unique_ctr," \
             "video_p100_watched_actions,video_thruplay_watched_actions,video_play_actions," \
             "video_avg_time_watched_actions,video_p25_watched_actions,video_p50_watched_actions," \
             "video_p75_watched_actions,account_currency"

    date_range = f'{{"since": "{date_from.strftime("%Y-%m-%d")}", "until": "{date_to.strftime("%Y-%m-%d")}"}}'
    # need to include this filter otherwise API won't return deleted campaigns
    filtering = ("[{'field':'ad.effective_status','operator':'IN',"
                 "'value':['ACTIVE','PAUSED','PENDING_REVIEW','CREDIT_CARD_NEEDED',"
                 "'PENDING_BILLING_INFO','CAMPAIGN_PAUSED','ADSET_PAUSED','IN_PROCESS',"
                 "'WITH_ISSUES','PREAPPROVED','DISAPPROVED','DISABLED','DELETED','ARCHIVED']}]")
    req_body_data = {"access_token": profile['access_token'],
                     "fields": fields,
                     "level": "ad",
                     "limit": FACEBOOK_PAGE_SIZE,
                     "time_range": date_range,
                     "time_increment": 1,
                     "filtering": filtering}
    return req_body_data


def _get_dates_between(date_from, date_to):
    dates = set()
    d = date_from
    while d <= date_to:
        # cast to string to avoid issues comparing date() to datetime()
        dates.add(d.strftime('%Y-%m-%d'))
        d += datetime.timedelta(days=1)
    return dates


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(query: QueryData):
    global historical_log_cache
    campaign_detail = query.campaign_detail

    # check if historical_log_cache is empty
    if historical_log_cache is None:
        query = f"""
            select campaign_detail from {HISTORICAL_LOG_TABLE}
            where campaign_type = '{CAMPAIGN_TYPE}';
        """
        df = pg_hook.get_pandas_df(query)
        if df.empty:
            historical_log_cache = set()
        else:
            df['campaign_detail'] = df['campaign_detail'].apply(
                lambda cd: json.dumps(cd, sort_keys=True))
            historical_log_cache = set(df['campaign_detail'])
    return campaign_detail in historical_log_cache


def _safe_parse_date(dt):
    if not dt:
        return None
    return datetime.datetime.strptime(dt[:10], '%Y-%m-%d').date()


def _iter_dates_by_interval(date_from, date_to, interval_days):
    default_delta = datetime.timedelta(days=interval_days)
    i_date_from = date_from
    while i_date_from < date_to:
        detla_left = date_to - i_date_from
        delta = min(detla_left, default_delta)
        i_date_to = i_date_from + delta
        yield (i_date_from, i_date_to)
        i_date_from = i_date_to + datetime.timedelta(days=1)


def get_historical_queries(profile, campaign, historical_range_mock):
    today = dates['TODAY']
    two_years_ago = today - datetime.timedelta(365*2)
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        two_years_ago = today - datetime.timedelta(historical_range_mock)
    max_date_to = today
    min_date_from = two_years_ago
    historical_queries = []
    date_from = max(min_date_from, two_years_ago)
    date_from = get_historical_date_from(state, date_from)
    date_to = min(max_date_to, today)
    if date_from > date_to:
        return []

    # split queries into intervals. Otherwise, Facebook will return error if too much data requested
    for i_date_from, i_date_to in _iter_dates_by_interval(date_from, date_to,
                                                          FACEBOOK_MAX_DAYS_REQUESTED_IN_A_QUERY):
        hist_query_data = create_query_body(profile, campaign, i_date_from, i_date_to)
        hist_query = QueryData(hist_query_data, 'historical', profile, campaign, i_date_from, i_date_to)
        historical_queries.append(hist_query)
    return [
        query for query in historical_queries
        if not does_hist_log_exist(query)
    ]


def get_standard_queries(profile, campaign, **kwargs):
    today = dates['TODAY']
    lastweek = get_date_to_execute_on(dates, **kwargs)
    lastweek = datetime.datetime.strptime(lastweek, '%Y%m%d')
    standard_queries = {}
    standard_query_data = create_query_body(profile, campaign, lastweek, today)
    standard_query = QueryData(standard_query_data, 'standard', profile, campaign,
                                lastweek, today)
    standard_queries[standard_query.campaign_detail] = standard_query
    return list(standard_queries.values())


def get_db_account_currencies(profile):
    profile_account_id = str(profile['account_id']).replace('act_', '')
    if profile_account_id in state['db_account_currencies']:
        return state['db_account_currencies'][profile_account_id]
    sql = f'''
        SELECT DISTINCT account_currency FROM {REPORTS_TABLE_NAME}
        WHERE account_id = '{profile_account_id}'
    '''
    df = pg_hook.get_pandas_df(sql)
    if df.empty:
        currencies = []
    else:
        currencies = [None if pd.isna(v) else str(v)
                      for v in df['account_currency']]
    state['db_account_currencies'][profile_account_id] = currencies
    return currencies


def get_api_account_currency(profile, **kwargs):
    access_token = profile['access_token']
    if access_token in state['api_account_currencies']:
        return state['api_account_currencies'][access_token]
    data = {"access_token": access_token,
            "fields": "currency"}
    try:
        response = send_fb_request(
            f'{FACEBOOK_DOMAIN}/{profile["account_id"]}',
            'GET', data, access_token=access_token, company_id=profile.get('company_id'), **kwargs)
        currency = response.json()['currency']
        state['api_account_currencies'][access_token] = currency
        return currency
    except Exception as e:
        logging.warning(f"Exception when trying to get API account ({profile['account_id']}) currency."
                        f" {e} {traceback.format_exc()}")


def get_facebook_queries_for_profile(profile, **kwargs):
    historical_queries = []
    standard_queries = []
    campaigns = profile['campaigns']
    historical_range_mock = kwargs['dag_run'].conf.get('historical_range_in_days')
    for campaign in campaigns:
        if not campaign.get('external_campaign_id'):
            continue
        campaign_hist_queries = get_historical_queries(profile, campaign, historical_range_mock)
        historical_queries.extend(campaign_hist_queries)
        campaign_standard_queries = get_standard_queries(profile, campaign, **kwargs)
        standard_queries.extend(campaign_standard_queries)
    batched_standard_queries = list(
        iter_batch(standard_queries, FACEBOOK_STANDARD_QUERIES_BATCH_SIZE)
    )
    batched_historical_queries = list(
        iter_batch(historical_queries, FACEBOOK_HISTORICAL_QUERIES_BATCH_SIZE)
    )
    return historical_queries, standard_queries, batched_standard_queries, batched_historical_queries


@set_config(state)
@set_execution_dates(dates)
def get_facebook_queries(profiles, **kwargs):
    logging.info(f"get_google_queries from {len(profiles)} profiles {profiles}")
    all_batched_historical_queries, all_batched_standard_queries = [], []
    standard_queries_count, historical_queries_count = 0, 0
    for profile in profiles:
        logging.info(f"Processing profile {profile}")
        historical_queries, standard_queries, batched_standard_queries, batched_historical_queries = \
            get_facebook_queries_for_profile(profile, **kwargs)
        all_batched_historical_queries.extend(batched_historical_queries)
        all_batched_standard_queries.extend(batched_standard_queries)
        # upd logging counts
        standard_queries_count += len(standard_queries)
        historical_queries_count += len(historical_queries)
    all_queries = all_batched_standard_queries + all_batched_historical_queries
    logging.info(f"Generated {len(all_queries)} queries total."
                 f" {historical_queries_count} historical"
                 f" (historical batch count {len(all_batched_historical_queries)})"
                 f" and {standard_queries_count} standard"
                 f" (standard batch count {len(all_batched_standard_queries)})")
    return filter_out_standard_queries([
        query.to_dict() if isinstance(query, QueryData) else [q.to_dict() for q in query]
        for query in all_queries
    ], **kwargs)


def send_fb_request(url: str, method: str, json_data: dict=None, query_type=None, access_token=None, company_id=None, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    if access_token in state['failed_access_tokens']:
        message = (f"send_fb_request is intentionally skipped because"
                   f" provided access_token previously failed")
        logging.info(message)
        raise MintFlowFailed(message)
    global last_fb_request
    # ensure 1 request per second
    if time.time() - last_fb_request < 1:
        time.sleep(1)
    last_fb_request = time.time()

    request_data = {'url': url, 'method': method}
    if json_data:
        request_data['params'] = json_data
    retries_quota_exceeded = FACEBOOK_QUOTA_REACHED_RETRIES
    retries_other = FACEBOOK_BAD_REQUEST_RETRIES
    while retries_other > 0 and retries_quota_exceeded > 0:
        try:
            response = requests.request(**request_data)
            status_code = response.status_code
        except Exception as e:
            logging.warning(
                f"Exception {e} occurred when making request."
                f" This can happen if Facebook is unavailable or because of network issues"
            )
            status_code = 0
        response_success = 200 <= status_code <= 299
        if query_type is not None:
            # log datadog
            response_status_str = 'success' if response_success else 'error'
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}", f"request_status:{response_status_str}",
                                 f"client:{company_id}", f'run_number:{current_run_number}'])
        if response_success:
            # additionally throttle if we are close to limit
            throttle_header = response.headers.get('x-fb-ads-insights-throttle')
            if throttle_header:
                throttle_header = json.loads(throttle_header)
                # filter throttle dict with ints only and take max value
                if float(max(filter(lambda x: type(x) == int, list(throttle_header.values())))) > 95:
                    logging.warning(f"Close to API Quota. {throttle_header}. Going to sleep")
                    time.sleep(FACEBOOK_BAD_REQUEST_RETRY_WAIT)
            return response
        elif status_code == 0:
            # exception during making request
            retries_other -= 1
            time.sleep(FACEBOOK_BAD_REQUEST_RETRY_WAIT)
        else:
            logging.warning(f'send request with data = {json.dumps({"url": url, "method": method})} \n'
                            f'received {response.status_code} \n {response.text}')
            if 'limit' in response.text:
                time.sleep(FACEBOOK_QUOTA_REACHED_RETRY_WAIT)
                retries_other -= 1
            elif status_code == 400:
                # problems with access_token
                if not re.findall(r"Object with ID '[^']*' does not exist", response.text) \
                        and access_token is not None:
                    state['failed_access_tokens'].add(access_token)
                break
            else:
                time.sleep(FACEBOOK_BAD_REQUEST_RETRY_WAIT)
                retries_other -= 1
    data_to_log = {'url': url, 'method': method}
    message = 'can\'t process the request ' + json.dumps(data_to_log)
    logging.error(message)
    raise MintFlowFailed(f"{FLOW_NAME} [latest_code={status_code}] {message}")


def create_report_id(query: QueryData, **kwargs):
    data = query.data
    campaign_id = query.campaign['external_campaign_id']
    response = send_fb_request(f'{FACEBOOK_DOMAIN}/{campaign_id}/insights',
                               'POST', data, query_type=query.type,
                               access_token=data['access_token'], company_id=query.profile.get('company_id'), **kwargs)
    report_id = response.json()['report_run_id']
    return report_id


def process_queries_batch(queries: List[QueryData], **kwargs):
    # take first one. all of them are same
    access_token = queries[0].profile['access_token']
    query_type = queries[0].type
    # get list of company ids
    company_ids = list(set([q.profile.get('company_id') for q in queries]))
    # leave single company id if it's only one in a list else return list of ids
    company_ids = company_ids[0] if len(company_ids) == 1 else company_ids
    batch_data = [
        {
            "method": "GET",
            "relative_url":
                f"{query.campaign['external_campaign_id']}/insights?"
                + urllib.parse.urlencode(query.data)
        }
        for query in queries
        if not check_if_needs_to_skip_query(query)
    ]
    if not batch_data:
        logging.warning("All queries are skipped")
        return []
    failed_queries = []
    try:
        batch_response = send_fb_request(
            FACEBOOK_DOMAIN, 'POST',
            {
                'access_token': access_token,
                'batch': json.dumps(batch_data)
            },
            query_type=query_type,
            access_token=access_token,
            company_id=company_ids,
            **kwargs
        )
    except Exception as e:
        logging.warning("batch GET failed. Sending all reports as async")
        logging.warning('batch failed with:')
        traceback.print_exc()
        logging.exception(f"{type(e).__name__} at line {e.__traceback__.tb_lineno} of {__file__}: {e}")
        return queries
    for query, response in zip(queries, batch_response.json()):
        if response['code'] != 200:
            failed_queries.append(query)
            continue
        load_report(query, json.loads(response['body']), **kwargs)
    return failed_queries


def check_report_ready(query: QueryData, **kwargs):
    access_token = query.data['access_token']
    data = {"access_token": access_token}
    try:
        response = send_fb_request(
            f'{FACEBOOK_DOMAIN}/{query.report_id}',
            'GET', data, query_type=query.type,
            access_token=access_token, company_id=query.profile.get('company_id'), **kwargs)
    except Exception as e:
        logging.error(f"check_report_ready failed. {e}")
        return None, None
    json_response = response.json()
    status = json_response['async_status']
    completion_perct = json_response['async_percent_completion']
    logging.info(f"async report status {status} completion_perct {completion_perct} {json_response}")
    if status == 'Job Completed' and completion_perct == 100:
        return True, json_response
    elif status in ['Job Failed', 'Job Skipped']:
        logging.error(f"Job failed on Facebook side status {status} {json_response}")
        return None, json_response
    else:
        return False, json_response


def parse_action_values(json_response: dict):
    """
    function parses action values
    to join conversions revenue with action types
    """
    new_resp = []
    # select only needed cols
    cols = {
        'date_start': 'date',
        'action_values': 'action_values',
        'account_currency': 'conversion_currency',
        "adset_id": "ad_set_id",
        'ad_id': 'ad_id',
        'account_id': 'account_id',
        'campaign_id': 'campaign_id'
    }
    for row in json_response:
        if 'action_values' in row:
            new_row = [row[idx] for idx in cols.keys()]
            new_dict = {}
            for col_name, row_val in zip(cols.keys(), new_row):
                new_dict[col_name] = row_val
            new_resp.append(new_dict)
    
    if len(new_resp) == 0:
        logging.info('Action values was not found in the response, skipping that response...')
        return None

    meta = list(cols.keys())
    meta.remove('action_values')
    df = pd.json_normalize(
            new_resp,
            record_path="action_values",
            meta=meta)
    df = df.rename(cols, axis=1)
    df = df.rename({'value': 'revenue'}, axis=1)
    df["ad_set_id"] = df["ad_set_id"].replace("", '0')
    df["ad_id"] = df["ad_id"].replace("", '0')
    return df


def parse_actions(json_response: dict):
    """
    function parses action values
    flattening 
    """
    new_resp = []
    # select only needed cols
    cols = {
        'date_start': 'date',
        "adset_id": "ad_set_id",
        'ad_id': 'ad_id',
        'actions': 'actions',
        'account_currency': 'conversion_currency',
        'account_id': 'account_id',
        'campaign_id': 'campaign_id'
    }
    for row in json_response:
        if 'actions' in row:
            new_row = [row[idx] for idx in cols.keys()]
            new_dict = {}
            for col_name, row_val in zip(cols.keys(), new_row):
                new_dict[col_name] = row_val
            new_resp.append(new_dict)
    
    if len(new_resp) == 0:
        logging.info('Actions was not found in the response, skipping that response...')
        return None

    meta = list(cols.keys())
    meta.remove('actions')
    df = pd.json_normalize(
            new_resp,
            record_path="actions",
            meta=meta)
    df = df.rename(cols, axis=1)
    df["ad_set_id"] = df["ad_set_id"].replace("", '0')
    df["ad_id"] = df["ad_id"].replace("", '0')
    df["campaign_type"] = CAMPAIGN_TYPE
    return df


def parse_report_response(response_json):
    next_url = response_json.get('paging', {}).get('next')
    if not response_json['data']:
        logging.warning(f"Empty data {response_json}")
        return [None, None], next_url

    text_data = json.dumps(response_json) \
        .replace('offsite_conversion.fb_pixel_lead', 'offsite_conversion_fb_pixel_lead') \
        .replace('onsite_conversion.', 'onsite_conversion_')

    json_data = json.loads(text_data)["data"]
    video_metrics = [
        'video_p100_watched_actions', 'video_thruplay_watched_actions',
        'video_15_sec_watched_actions', 'video_avg_time_watched_actions',
        'video_p25_watched_actions', 'video_p50_watched_actions',
        'video_p75_watched_actions', 'video_play_actions'
    ]

    # grab actions metrics
    df_actions = parse_actions(json_data)
    # grab conversion revenue from action values
    df_action_values = parse_action_values(json_data)

    # merge actions and action_values if action_values exist
    df_conv = pd.merge(
        df_actions,
        df_action_values,
        how='left',
        on=['action_type', 'date', 'ad_set_id', 'ad_id', 'account_id', 'campaign_id', 'conversion_currency'],
        validate='1:1'
        ) if df_action_values is not None else df_actions

    # fill None conv revenue
    df_conv.fillna(value={"revenue": 0}, inplace=True)

    # flattening `actions` column from list of dicts into dict
    for row in json_data:
        row['video_metrics'] = {}
        if 'actions' in row:
            row['actions'] = {action['action_type']: action['value']
                              for action in row['actions']}
        for video_metric_key in video_metrics:
            video_metric_value = row.get(video_metric_key, [])
            for item in video_metric_value:
                if item['action_type'] == 'video_view':
                    row['video_metrics'][video_metric_key] = item['value']
            if video_metric_key in row:
                del row[video_metric_key]

    df = pd.json_normalize(json_data) \
        .rename(columns={
            "date_start": "date",
            "adset_id": "ad_set_id",
            "actions.app_engagement": "app_engagement",
            "actions.click_to_call_call_confirm": "click_to_call_call_confirm",
            "actions.rsvp": "event_responses",
            "actions.instagram_profile_engagement": "instagram_profile_engagement",
            "actions.landing_page_view": "landing_page_views",
            "actions.offsite_conversion_fb_pixel_lead": "leads",
            "actions.like": "like",
            "actions.link_click": "link_clicks",
            "actions.onsite_conversion_messaging_conversation_started_7d": "messaging_conversation_started",
            "actions.offsite_conversion": "offsite_conversion",
            "actions.offsite_engagement": "offsite_engagement",
            "actions.onsite_conversion_lead_grouped": "on_facebook_lead",
            "actions.page_engagement": "page_engagement",
            "actions.comment": "post_comments",
            "actions.post_engagement": "post_engagement",
            "actions.post_reaction": "post_reaction",
            "actions.onsite_conversion_post_save": "post_saves",
            "actions.post": "post_shares",
            "actions.video_view": "three_sec_video_views",
            "video_metrics.video_p100_watched_actions": "completed_views_full",
            "video_metrics.video_thruplay_watched_actions": "thruplays",
            "video_metrics.video_15_sec_watched_actions": "video_p15_watched_actions",
            "video_metrics.video_avg_time_watched_actions": "video_average_play_time",
            "video_metrics.video_p25_watched_actions": "video_p25_watched_actions",
            "video_metrics.video_p50_watched_actions": "video_p50_watched_actions",
            "video_metrics.video_p75_watched_actions": "video_p75_watched_actions",
            "video_metrics.video_play_actions": "video_plays"})
    df["ad_set_id"] = df["ad_set_id"].replace("", '0')
    df["ad_id"] = df["ad_id"].replace("", '0')
    df["campaign_type"] = CAMPAIGN_TYPE

    col_list = ['date', 'account_id', 'campaign_id', 'ad_set_id', 'clicks', 'unique_inline_link_clicks', 'impressions',
                'frequency', 'cpc', 'cpm', 'cpp', 'ctr', 'spend', 'ad_id',
                'leads', 'like', 'landing_page_views', 'reach', 'shares', 'social_spend', 'unique_ctr',
                'app_engagement', 'instagram_profile_engagement', 'offsite_conversion', 'offsite_engagement',
                'campaign_type', 'link_clicks', 'completed_views_full', 'three_sec_video_views', 'thruplays',
                'page_engagement', 'post_engagement', 'post_reaction', 'event_responses', 'post_shares',
                'post_comments', 'post_saves', 'on_facebook_lead', 'messaging_conversation_started', 'video_plays',
                'video_average_play_time', 'video_p25_watched_actions', 'video_p50_watched_actions',
                'video_p75_watched_actions', 'click_to_call_call_confirm', 'video_p15_watched_actions',
                'account_currency']
    for col in col_list:
        if col not in df.columns:
            df[col] = None
    df["shares"] = df["post_shares"]
    return [df[col_list], df_conv], next_url


def map_action_types(conv_reports: DataFrame, custom_conv_dict: list, account_id: str) -> DataFrame:
    """
    function maps action types to the human readble name
    """
    # start with default ones 
    def _map_default_type(returned_api_value):
        try:
            human_readable = FACEBOOK_DEFAULT_ACTION_TYPES[returned_api_value]
            return human_readable
        except KeyError:
            return returned_api_value

    def extract_conversion_numbers(value):
        pattern = r'conversion\.custom\.(\d+)'  
        match = re.search(pattern, value)
        if match:
            return match.group(1)  
        return None  

    def find_account_by_id(convs, acc_id):
        for conv in convs:
            if conv['acc_id'] == acc_id:
                return conv
        return None

    def _map_custom_type(account_id, returned_api_value):
        try:
            related_custom_convs = find_account_by_id(custom_conv_dict,account_id)
            extracted_conv_id = extract_conversion_numbers(returned_api_value)
            if related_custom_convs and extracted_conv_id:
                data = related_custom_convs['data']
                for custom_conv in data:
                    if custom_conv['id']==extracted_conv_id:
                        return custom_conv['name']
            return returned_api_value
        except Exception:
            return returned_api_value

    conv_reports['conversion_label'] = conv_reports['action_type'].apply(_map_default_type)
    conv_reports['conversion_label'] = conv_reports.apply(lambda row: _map_custom_type(account_id, row['conversion_label']), axis=1)
    conv_reports = conv_reports.rename(columns={'action_type': 'conversion_name'})
    return conv_reports


def get_custom_conversions(query: QueryData, **kwargs) -> Optional[dict]:
    """
    This function performs request to get custom conversions for the integration
    """
    acc_id = query.profile.get('account_id')
    endpoint = FACEBOOK_CUSTOM_CONVERSIONS_ENDPOINT.format(
        access_token=query.data['access_token'],
        account_id=acc_id)
    try:
        result_data = []
        while True:
            response = send_fb_request(endpoint,'get', company_id=query.profile.get('company_id'), **kwargs)
            print(response.json())
            endpoint = response.json().get("paging", {}).get("next")
            result_data += response.json()['data']
            if not endpoint:
                break

        
        return {'acc_id' : acc_id,
                'data' : result_data}
    except Exception:
        logging.info(f'Failed to get custom convs for {acc_id}')
        return

def format_conversion_mapping_to_insert(conv_mapp: list) -> pd.DataFrame:
    """
    Flattens and convert to pandas df
    """
    rows = []
    for entry in conv_mapp:
        acc_id = entry["acc_id"]
        for conv in entry["data"]:
            rows.append({
                "account_id": acc_id,
                "conversion_id": conv["id"],
                "conversion_label": conv["name"]
            })
    
    df = pd.DataFrame(rows, columns=["account_id", "conversion_id", "conversion_label"])
    return df


def put_custom_convs_into_db(report: DataFrame, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    columns = get_cols_to_insert(report)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""
          INSERT INTO {REPORTS_CONV_MAP_TABLE_NAME} ({columns})
          VALUES {values_str}
          ON CONFLICT (account_id, conversion_id
                       ) 
          DO UPDATE SET
            conversion_label = Excluded.conversion_label"""
    logging.info(query)
    pg_hook.run(query, True)
    increment_metric('DATADOG_AIRFLOW_PUSHED_METRIC', env_type,
                     FLOW_NAME, report.shape[0], [f"table:{REPORTS_CONV_MAP_TABLE_NAME}", f'run_number:{current_run_number}'])


def create_custom_conversions_mapping_dict(queries_data, **kwargs):
    resulting_mapping = []
    all_queries = []
    for i_q, query_data in enumerate(queries_data):
        is_batch = isinstance(query_data, list)
        if is_batch:
            # multiple queries groupped into a batch
            queries = [QueryData(**qd) for qd in query_data]
            all_queries.extend(queries)
        else:
            query = QueryData(**query_data)
            all_queries.append(query)
    
    # process quesries and skip already processed ones
    logging.info(f'going to process {len(all_queries)} queries, before optimization')
    processed = []
    for q in all_queries:
        acc_id = q.profile.get('account_id')
        if acc_id in processed:
            logging.info('Found already processed account, skipping it...')
        else:
            convs = get_custom_conversions(q, **kwargs)
            if convs:
                resulting_mapping.append(convs)
            processed.append(acc_id)
    logging.info(f"Processed {len(processed)} queries")



    logging.info(resulting_mapping)


    convs_df = format_conversion_mapping_to_insert(resulting_mapping)
    if not convs_df.empty:
        put_custom_convs_into_db(
            convs_df,
            **kwargs
        )
    else:
        logging.info('no conversion data was found...')
    return resulting_mapping


def download_report(query: QueryData, report_response_json=None, **kwargs):
    report_dfs = []
    report_conv_dfs = []
    if report_response_json is None:
        access_token = query.data['access_token']
        data = {"access_token": access_token,
                "limit": FACEBOOK_PAGE_SIZE}
        report_response = send_fb_request(
            f'{FACEBOOK_DOMAIN}/{query.report_id}/insights/',
            'GET', data, query_type=query.type,
            access_token=access_token, company_id=query.profile.get('company_id'), **kwargs)
        report_response_json = report_response.json()
    [report_df, report_conv], next_url = parse_report_response(report_response_json)
    if report_df is not None:
        report_dfs.append(report_df)
    if report_conv is not None:
        report_conv_dfs.append(report_conv)
    while next_url:
        report_response = send_fb_request(next_url, 'GET', query_type=query.type, company_id=query.profile.get('company_id'), **kwargs)
        [report_df, report_conv], next_url = parse_report_response(report_response.json())
        if report_df is not None:
            report_dfs.append(report_df)
        if report_conv is not None:
            report_conv_dfs.append(report_conv)
    standart_reports = pd.concat(report_dfs, axis=0, ignore_index=True) if report_dfs else None
    conv_reports = pd.concat(report_conv_dfs, axis=0, ignore_index=True) if report_conv_dfs else None

    if conv_reports is not None:
        conv_reports = conv_reports.rename(columns={'action_type': 'conversion_name'})
    return standart_reports, conv_reports


def put_conv_report_to_db(report: DataFrame, company_id: int, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    columns = get_cols_to_insert(report)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""
          INSERT INTO {REPORTS_CONV_TABLE_NAME} ({columns})
          VALUES {values_str}
          ON CONFLICT (date, ad_set_id, campaign_id, account_id, ad_id, conversion_name
                       ) 
          DO UPDATE SET
            value = Excluded.value,
            revenue = Excluded.revenue,
            conversion_currency = Excluded.conversion_currency,
            campaign_type = Excluded.campaign_type"""
    pg_hook.run(query, True)
    increment_metric('DATADOG_AIRFLOW_PUSHED_METRIC', env_type,
                     FLOW_NAME, report.shape[0], [f"table:{REPORTS_CONV_TABLE_NAME}", f"client:{company_id}", f'run_number:{current_run_number}'])


def put_reports_to_db(report: DataFrame, company_id: int, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    columns = get_cols_to_insert(report)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        query = f"""
              INSERT INTO {REPORTS_TABLE_NAME} ({columns})
              VALUES {values_str}
              ON CONFLICT (date,account_id, campaign_id, ad_set_id, ad_id
                           ) 
              DO UPDATE SET
                clicks=Excluded.clicks, unique_inline_link_clicks=Excluded.unique_inline_link_clicks,
                impressions=Excluded.impressions, frequency=Excluded.frequency,cpc=Excluded.cpc,cpm=Excluded.cpm,cpp=Excluded.
                cpp, ctr=Excluded.ctr, spend=Excluded.spend, leads=Excluded.leads, "like"=Excluded."like", 
                landing_page_views=Excluded.landing_page_views, reach=Excluded. reach,shares=Excluded.shares,
                social_spend=Excluded. social_spend, unique_ctr=Excluded.unique_ctr,app_engagement=Excluded.app_engagement,
                instagram_profile_engagement=Excluded.instagram_profile_engagement,
                offsite_conversion=Excluded.offsite_conversion,offsite_engagement=Excluded.offsite_engagement,
                campaign_type=Excluded.campaign_type,link_clicks=Excluded.link_clicks,
                completed_views_full=Excluded.completed_views_full,three_sec_video_views=Excluded.three_sec_video_views,
                thruplays=Excluded.thruplays,page_engagement=Excluded.page_engagement,
                post_engagement=Excluded.post_engagement,post_reaction=Excluded.post_reaction,
                event_responses=Excluded.event_responses,post_shares=Excluded.post_shares,
                post_comments=Excluded.post_comments,post_saves=Excluded.post_saves,
                on_facebook_lead=Excluded.on_facebook_lead,
                messaging_conversation_started=Excluded.messaging_conversation_started,video_plays=Excluded.video_plays,
                video_average_play_time=Excluded.video_average_play_time,
                video_p25_watched_actions=Excluded.video_p25_watched_actions,
                video_p50_watched_actions=Excluded.video_p50_watched_actions,
                video_p75_watched_actions=Excluded.video_p75_watched_actions,
                click_to_call_call_confirm=Excluded.click_to_call_call_confirm,
                video_p15_watched_actions=Excluded.video_p15_watched_actions,
                account_currency=Excluded.account_currency; """
        pg_hook.run(query, True)
        increment_metric('DATADOG_AIRFLOW_PUSHED_METRIC', env_type,
                         FLOW_NAME, report.shape[0], [f"table:{REPORTS_TABLE_NAME}", f"client:{company_id}", f'run_number:{current_run_number}'])


def log_hist_request(query: QueryData):
    campaign_detail = query.campaign_detail
    company_id = query.company_id
    query = f"""
            INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
            Values (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
            """
    data = {'company_id': company_id,
            'campaign_type': CAMPAIGN_TYPE,
            'campaign_detail': campaign_detail}
    pg_hook.run(query, True, data)


def load_report(query, report_response_json=None, **kwargs):
    logging.info(f'Starting loading report for {query}')
    try:
        report, conv_report = download_report(query, report_response_json, **kwargs)
        if report is None or report.empty:
            message = f'report is empty for {query.type}'
            logging.warning(message)
        else:
            company_id = query.profile.get('company_id')
            put_reports_to_db(report, company_id, **kwargs)
            logging.info('report is pushed')
        
        if conv_report is None or conv_report.empty:
            message = f'conv report is empty for {query.type}'
            logging.warning(message)
        else:
            company_id = query.profile.get('company_id')
            put_conv_report_to_db(conv_report, company_id, **kwargs)
            logging.info('conv report is pushed')
        
        if query.type == 'historical':
            log_hist_request(query)
            logging.debug('log is pushed')
        return True
    except Exception as e:
        logging.error(f"load_report failed query={query}\n{traceback.format_exc()}")
        return False


def register_query_failed(query, exception):
    key = (query.profile['access_token'],
           query.profile['account_id'],
           query.campaign['external_campaign_id'])
    if 'code=400' in str(exception):
        failed_queries[key] = failed_queries.get(key, 0) + 1


def check_if_needs_to_skip_query(query):
    key = (query.profile['access_token'],
           query.profile['account_id'],
           query.campaign['external_campaign_id'])
    return failed_queries.get(key, 0) >= 5


def _send_async_query(query, **kwargs):
    if check_if_needs_to_skip_query(query):
        logging.warning(f"Going to skip query {query} because other queries"
                        f" with the same access_token and campaign failed repeatedly")
        return False
    try:
        query.report_id = create_report_id(query, **kwargs)
        query.report_id_created_at = time.time()
        return True
    except Exception as e:
        register_query_failed(query, e)
        return False


def process_waiting_async_queries(waiting_queries, **kwargs):
    success_count, failed_count = 0, 0
    while waiting_queries:
        logging.info(f"Polling for status. len(waiting_queries) = {len(waiting_queries)}"
                     f" queue_success_count={success_count}, queue_failed_count={failed_count}")
        query = waiting_queries.pop(0)
        if time.time() - query.report_id_created_at < 60:
            # give report at least 1 minute before polling for result
            # insert back to queue
            waiting_queries.insert(0, query)
            time.sleep(10)
            continue
        report_ready, last_response = check_report_ready(query, **kwargs)
        if report_ready:
            logging.info(f'Report {query.report_id} is ready')
            loaded = load_report(query, **kwargs)
            if loaded:
                success_count += 1
            else:
                failed_count += 1
        elif report_ready is None:
            message = (f'Cant load file for query={query}; last_response={last_response}')
            logging.error(message)
            failed_count += 1
        else:
            in_progress_time = time.time() - query.report_id_created_at
            logging.info(f"Report {query.report_id} is in progress for {in_progress_time}")
            if in_progress_time > FACEBOOK_MAX_WAIT_POLL_REPORT:
                message = (
                    f'Facebook creating report for more than {FACEBOOK_MAX_WAIT_POLL_REPORT} seconds.'
                    f' Going to skip query={query}; last_response={last_response}')
                logging.error(message)
                failed_count += 1
            else:
                # insert back into the queue
                waiting_queries.append(query)
    return success_count, failed_count


def run_queries(queries_data: List[Union[dict, List[dict]]], **kwargs):
    all_queries_count = sum(len(qd) if isinstance(qd, list) else 1
                            for qd in queries_data)
    logging.info(f"run_queries all_queries_count={all_queries_count};"
                 f" {len(queries_data)} queries and batches")
    success_count, failed_count, failed_to_batch = 0, 0, 0
    waiting_queries = []
    for i_q, query_data in enumerate(queries_data):
        is_batch = isinstance(query_data, list)
        logging.info(f'run_queries creating report ids {i_q} / {len(queries_data)}. is_batch={is_batch}')
        if is_batch:
            # multiple queries groupped into a batch
            queries = [QueryData(**qd) for qd in query_data]
            failed_queries = process_queries_batch(queries, **kwargs)
            logging.info(f'process_queries_batch end. failed_queries_batch={len(failed_queries)}')
            # retry failed queries with async method
            if failed_queries:
                logging.info(f"{len(failed_queries)} queries failed to batch GET."
                             f" Trying them async")
                for q in failed_queries:
                    if _send_async_query(q, **kwargs):
                        waiting_queries.append(q)
                    else:
                        logging.error('_send_async_query failed')
                        failed_count += 1
            success_count += len(queries) - len(failed_queries)
            failed_to_batch += len(failed_queries)
        else:
            query = QueryData(**query_data)
            if _send_async_query(query, **kwargs):
                logging.info(f'report_id is {query.report_id}')
                waiting_queries.append(query)
            else:
                failed_count += 1
        if len(waiting_queries) > 100:
            logging.info(f"Intermediate process_waiting_async_queries, in queue {len(waiting_queries)}")
            queue_success_count, queue_failed_count = process_waiting_async_queries(waiting_queries, **kwargs)
            success_count += queue_success_count
            failed_count += queue_failed_count
    logging.info(f"Processed batched queries. success_count={success_count} failed_count={failed_count};"
                 f" those failed but will be retried async {failed_to_batch}")
    logging.info(f"Starting to process_waiting_async_queries, in queue {len(waiting_queries)}")
    queue_success_count, queue_failed_count = process_waiting_async_queries(waiting_queries, **kwargs)
    success_count += queue_success_count
    failed_count += queue_failed_count
    logging.info(f"Processed all queries. success_count={success_count}, failed_count={failed_count}")
    logging.info("Finish")


with DAG(FLOW_NAME,
         description='Facebook flow dag',
         start_date=datetime.datetime(2022, 1, 1),
         schedule_interval=None,
         tags=['ingestion_flow'],
         params=DagConfigSetter(FLOW_NAME).get_params(),
         catchup=False) as dag:
    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id='get_facebook_queries',
        on_failure_callback=failure_callback,
        python_callable=get_facebook_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    custom_conv_map = PythonOperator(
        task_id='generate_custom_convs_map',
        on_failure_callback=failure_callback,
        python_callable=create_custom_conversions_mapping_dict,
        op_kwargs={"queries_data": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="run_queries",
        on_failure_callback=failure_callback,
        python_callable=run_queries,
        op_kwargs={"queries_data": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> custom_conv_map >> t3 >> cleanup_xcoms
