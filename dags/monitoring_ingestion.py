# TASK - https://mint-ai.atlassian.net/browse/PDT-43906
# DOCS - https://mint-ai.atlassian.net/wiki/spaces/TEAMJ/pages/**********/POST+IMPLEMENTATION+DOC+MONITORING+REPORTS+INGESTION+VIA+SLACK
import datetime
import time
import logging
import requests

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook

from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.commons import failure_callback
from myn_airflow_lib.constants import EXECUTOR_STABLE_NODES
from myn_airflow_lib.datadog import increment_metric, init_datadog

FLOW_NAME = 'MONITORING_REPORTS_INGESTION'

env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
msteams_webhook_url = env_resolver.msteams_monitoring_webhook

pg_hook = PostgresHook(postgres_conn_id='uri_reports')


service_mapping = {
    'Adform': 'reports_adformconversion_v2',
    'Amazon': 'reports_amazon_performance',
    'Appsflyer': 'reports_appsflyer_performance_v2',
    'CM360 Basic': 'campaign_manager_base_report',
    'CM360 DV360linkage': 'campaign_manager_dv360_report',
    'CM360 Floodlight': 'campaign_manager_floodlight_base_report',
    'CM360 Paid Search': 'campaign_manager_paid_search_external_report',
    'DV360': 'dv360_base_reports',
    'Google Ads ad level': 'reports_adwords_ad_performance',
    'Google Ads campaign level': 'search_campaign_performance',
    'Google Ads Mobile': 'search_campaign_performance_mobile',
    'Google Ads Conversion campaign level': 'search_campaign_conversion_performance',
    'Google Ads Conversion ad level': 'reports_adwords_ad_conversion_performance',
    'Google Analytics 4 Autotagging': 'reports_ga4_autotag_performance_v2',
    'Google Analytics 4': 'reports_ga4_base_performance_v2',
    'Google Analytics 4 Autotagging Sessions': 'reports_ga4_autotag_sessions_performance_v2',
    'Google Analytics 4 Sessions': 'reports_ga4_base_sessions_performance_v2',
    'LinkedIn': 'reports_linkedin_performance',
    'Meta (Facebook)': 'social_campaigns_reports_v3',
    'Meta (Facebook) conversions': 'meta_conversions_report',
    'Microsoft Ads (Bing) ad level': 'microsoft_advertising_performance_ad_level',
    'Microsoft Ads (Bing) campaign level': 'microsoft_advertising_estimated_campaign_level_v2',
    'TikTok': 'reports_tiktok_performance',
    'Zemanta': 'reports_zemanta_performance_v2',
    'Xandr': 'reports_xandr_performance_v3',
}


def send_msteams_msg(webhook_url: str, msg: str) -> None:
    """Send message to MSTeams channel using a webhook URL"""
    max_retries = 3
    jsondata = {
        "text": msg
    }
    success = False
    for _ in range(max_retries):
        try:
            response = requests.post(webhook_url, json=jsondata)
            response.raise_for_status()
            success = True
            logging.info('Message sent successfully!')
            break
        except Exception as e:
            logging.info(f'Something went wrong while sending message: {e}')
            time.sleep(10)
    if not success:
        error_message = f'Failed to post message after {max_retries} retries.'
        logging.error(error_message)
        raise Exception(error_message)


def o_str(text):
    return f'''<small style="color:orange;">`{text}`</small>'''


def evaluate_the_count(service: str, yesterday_count: int, past_counts: list):
    """Evaluates the given number to an array of numbers and returns a message line to append.
    Making k1 and k2 values smaller than 1 shrinks the thresholds for warnings and vice versa.
    The lowest possible warning threshold fixed at 1/2 of one standard deviation.
    The lowest possible alert threshold fixed at 1/3 of one standard deviation."""
    k1 = 1.9
    k2 = 3.8
    mean = round(sum(past_counts) / len(past_counts))
    variance = round(sum((count - mean) ** 2 for count in past_counts) / len(past_counts))
    std_dev = round(variance ** 0.5)
    one_step_down = max(mean - round(std_dev * k1), round(std_dev/2))
    one_step_up = mean + round(std_dev * k1)
    two_step_down = max(mean - round(std_dev * k2), round(std_dev/3))
    two_step_up = mean + round(std_dev * k2)
    debug_str = (f'mean={mean}, variance={variance}, std_dev={std_dev} | Alert << {two_step_down} << Warning '
                 f'<< {one_step_down} - OK Zone - {one_step_up} >> bit notice >> {two_step_up} >> much notice')
    logging.info(f"Evaluation details for {service}: yesterday {yesterday_count} comparing to {debug_str}")
    if one_step_down <= yesterday_count <= one_step_up:
        return (f"&#x2705; {o_str(service)} Row count is {o_str(yesterday_count)} "
                f"for yesterday compared to median of {o_str(round(mean))}")
    elif two_step_down >= yesterday_count:
        return (f"&#x23EC; {o_str(service)} &#x2757;**ALERT** "
                f"Count is much lower than usual! We have {o_str(yesterday_count)} "
                f"for yesterday compared to median of {o_str(round(mean))}")
    elif two_step_up <= yesterday_count:
        return (f"&#x23EB; {o_str(service)} Data is much higher than usual! We have {o_str(yesterday_count)} "
                f"for yesterday compared to median of {o_str(round(mean))}. Any new client(s) joined?")
    elif one_step_down > yesterday_count:
        return (f"&#x1F53D; {o_str(service)} &#x2755;**WARNING** "
                f"Count is bit lower than usual! We have {o_str(yesterday_count)}"
                f"for yesterday compared to median of {o_str(round(mean))}")
    elif one_step_up < yesterday_count:
        return (f"&#x1F53C; {o_str(service)} Count is bit higher than usual! We have {o_str(yesterday_count)} "
                f"for yesterday compared to median of {o_str(round(mean))}")
    else:
        return f":black_large_square: {o_str(service)} &#x2049;Can't evaluate reports for some reason..."


def monitor_data_ingestion():
    """Main function for monitoring data ingestion into Reports DB on daily basis.
    Runs after all Airflow ingestion DAGs.
    """
    days_for_evaluation = 30  # it will be +1 because of inclusion of end dates of the date range
    today = datetime.datetime.utcnow()
    one_day = datetime.timedelta(days=1)
    execution_time = today.strftime('%Y-%m-%d %H:%M')
    yesterday_raw = today - one_day
    yesterday = yesterday_raw.strftime('%Y-%m-%d')
    date_to_raw = yesterday_raw - one_day
    date_to = date_to_raw.strftime('%Y-%m-%d')
    date_from = (date_to_raw - datetime.timedelta(days=days_for_evaluation)).strftime('%Y-%m-%d')
    message_lines = []
    for service, table in service_mapping.items():
        past_x_days = pg_hook.get_records(f"select count(*), date from {table} where "
                                          f"date between '{date_from}' and '{yesterday}' GROUP BY date;")
        mapped_data = {date.strftime('%Y-%m-%d'): count for count, date in past_x_days}
        if not mapped_data:
            message_lines.append(f"&#x274C; {o_str(service)} &#x2757;**ALERT** There is no data at all! "
                                 f"Is this service was just released or is it abandoned?")
            continue
        try:
            yesterday_count = mapped_data.pop(yesterday)
        except KeyError:
            yesterday_count = None
        if not yesterday_count:
            message_lines.append(f"&#x274C; {o_str(service)} &#x2757;**ALERT** "
                                 f"Count is absent for yesterday!")
            continue
        message_lines.append(evaluate_the_count(service, yesterday_count, list(mapped_data.values())))
        increment_metric('airflow_daily_records.increment', env_type, service, yesterday_count)
    execution_time_lines = "." * 12 + f"{execution_time} UTC" + "." * 12
    info_line = (f"   \n&#x2139; Yesterday row count in {o_str('reports')} tables will be compared to a values taken "
                 f"for the past {o_str(str(days_for_evaluation + 1))} day(s) prio   \n_evaluation done for {yesterday} and "
                 f"taking median count of the period from {date_from} to {date_to}_   \n")
    end_lines = "   \n" + "." * 64
    message = execution_time_lines + info_line + '   \n'.join(message_lines) + end_lines
    send_msteams_msg(msteams_webhook_url, message)


with DAG(FLOW_NAME,
         description='monitoring of data ingestion for reports db',
         schedule_interval='0 9 * * *',
         start_date=datetime.datetime(2022, 1, 1),
         tags=['monitoring_flow'],
         catchup=False) as dag:
    t1 = PythonOperator(
        task_id='monitor_data_ingestion',
        python_callable=monitor_data_ingestion,
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )
