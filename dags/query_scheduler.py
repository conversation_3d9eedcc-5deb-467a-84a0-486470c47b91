import datetime
import logging
from myn_airflow_lib.constants import EXECUTOR_STABLE_NODES
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from kubernetes.client import models as k8s
import mailchimp_transactional as MailchimpTransactional
from mailchimp_transactional.api_client import ApiClientError
from myn_airflow_lib.commons import _get_db_value, failure_callback, QueryHandler
from myn_airflow_lib.resolvers import EnvironmentResolver



def insert_new_queries_if_needed(**kwargs):
    """
    parses dag config to find query keyword
    and inserts new query if needed
    """
    config = kwargs['dag_run'].conf.get('query')
    if config:
        logging.info('NEW QUERY WAS FOUND! Inserting...')
        hook = PostgresHook(postgres_conn_id='uri_reports')
        hook.run(
            f"""insert into query_scheduler_table 
            values ({_get_db_value(config['query_name'])},
            {_get_db_value(config['query'])},
            {_get_db_value(config['schedule'])},
            {_get_db_value(config['database'])},
            {_get_db_value(config['recipient'])})""",
                 True
        )
    else:
        logging.info('no config for new query was found... SKIPPING STEP')

def send_message(recipient: str, text: str, file_base64: str, filename: str):
    """
    sends mail using mailchimp
    file - arg should be base64 encoded
    """
    env_resolver = EnvironmentResolver()
    try:
        logging.critical('.'.join(str(env_resolver.mailchimp_token)))
        mailchimp = MailchimpTransactional.Client(env_resolver.mailchimp_token)
        message = {'message' :{
            'text': text,
            'to': [{'email': recipient}],
            'from_email': '<EMAIL>',
            'from_name': 'query_scheduler',
            'subject': 'MINT Query Results',
            'tags': ['team_j_query_scheduler'],
            'attachments': [{
            'name': filename,
            'content': file_base64}]
        }}
        mailchimp.messages.send(message)
    except ApiClientError as error:
        logging.info("An exception occurred while sending mail: {}".format(error.text))

def filter_queries(queries):
        def _is_monday_today():
            """
            Returns True if today is Monday, False otherwise.
            """
            today = datetime.date.today()
            return today.weekday() == 0  # Monday is represented by 0 in the weekday() function.

        def _is_first_day_of_month():
            """
            Returns True if today is the first day of the month, False otherwise.
            """
            today = datetime.date.today()
            return today.day == 1

        filter_map = {
            'weekly': _is_monday_today,
            'monthly': _is_first_day_of_month
        }
        return  [query for query in queries if query['schedule']=='daily' or filter_map[query['schedule']]()]

def extract_queries():
    """this func will extranct queries from source"""
    pg_hook = PostgresHook(postgres_conn_id='uri_reports')
    queries = pg_hook.get_pandas_df(
        sql='select * from query_scheduler_table'
    )
    queries = queries.to_dict(orient='records')
    logging.info(queries)
    return queries

def get_query_data(queries):
    """
    function reading source of the queries
    and returns a list of filtered queries to be executed in the run
    """
    filtered_queries = filter_queries(queries)
    return(filtered_queries)

    
def run_queries(filtered_queries):
    
    for query in filtered_queries:
        query = QueryHandler(query)
        if not query.parse_sql_query():
            logging.warning(f'Error while proceesing {query.QueryData.query} The query does not pass a check to contain only select')
            continue
        try:
            attachment = query.get_data()
        except Exception as e:
            logging.error(f'Error while processing query {query.QueryData.query_name} {e}')
        try:
            logging.info(f'sending message to {query.QueryData.recipient}')
            send_message(
                query.QueryData.recipient,
                f'Query scheduler result for {query.QueryData.query_name}',
                attachment,
                query.QueryData.query_name
            )
        except Exception as e:
            logging.error(f'Error while sending mail for {query.QueryData.query_name} {e}')



with DAG('QUERY_SCHEDULER',
         description='QUERY_SCHEDULER flow dag',
         schedule_interval="30 7 * * *",
         tags=None,
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False) as dag:
    
    
    t1 = PythonOperator(
        task_id='insert_new',
        python_callable=insert_new_queries_if_needed,
        on_failure_callback=failure_callback,
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id='extract_queries',
        python_callable=extract_queries,
        on_failure_callback=failure_callback,
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )


    t3 = PythonOperator(
        task_id="filter_queries",
        python_callable=get_query_data,
        on_failure_callback=failure_callback,
        op_kwargs={"queries": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t4 = PythonOperator(
        task_id="run_queries",
        python_callable=run_queries,
        on_failure_callback=failure_callback,
        op_kwargs={"filtered_queries": XComArg(t3)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> t4