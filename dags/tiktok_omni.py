import logging
import requests
import time
import datetime
import json
import pandas as pd

from pandas import DataFrame
from copy import deepcopy
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import <PERSON>gresHook

from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.commons import (set_config, set_execution_dates, failure_callback,
                                     enable_hist_log_check_based_on_conf)
from myn_airflow_lib.constants import EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.datadog import init_datadog, increment_metric
from myn_airflow_lib.exceptions import OmniFlowFailed

from omni.utils import QueryData
from omni.processors import ProfileProcessor, QueryDataProcessor, ReportLoadProcessor
from omni.exceptions import TikTokReportFailed


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True

state = {
    'dates': {}
}
dates = state['dates']

env_resolver = EnvironmentResolver()
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)

FLOW_NAME = 'TikTok'
PLATFORM_NAME = 'tiktok_integrated'
API_FIELDS = ["campaign_id", "adgroup_id", "ad_id", "spend", "impressions", "clicks", "video_views_p25",
              "video_views_p50", "video_views_p75", "video_views_p100", "video_play_actions", "conversion", "follows"]

env_resolver = EnvironmentResolver()
env_type = env_resolver.environment

REPORTS_TABLE_NAME = 'reports_tiktok_performance'


# TikTok classes initialisation -> TikTokProfileProcessor, TikTokQueryDataProcessor, TikTokReportLoadProcessor

class TikTokProfileProcessor(ProfileProcessor):
    def __init__(self, platform_name, **kwargs):
        super().__init__(platform_name, **kwargs)


class TikTokQueryDataProcessor(QueryDataProcessor):
    def __init__(self, profiles, dates, flow_name, api_fields, template_file, sanity_period, chunk_historical_query,
                 chunk_periods, historical_period, campaigns_plural):
        super().__init__(profiles, dates, flow_name, api_fields, template_file, sanity_period, chunk_historical_query,
                         chunk_periods, historical_period, campaigns_plural)

    def _create_query_body(self, campaign: dict, date_from: str, date_to: str, account_id: str = None) -> dict:
        """Re-defined function creates filters"""
        campaign_id = campaign['external_id']
        filters = self._read_query_template()
        filters["advertiser_id"] = account_id
        filters["metrics"] = self.api_fields
        filters["start_date"] = date_from
        filters["end_date"] = date_to
        filters["filtering"] = [{"field_name": "campaign_ids", "filter_type": "IN", "filter_value": f'[{campaign_id}]'},
                                {"field_name": "ad_status", "filter_type": "IN", "filter_value": '["STATUS_ALL"]'}]
        return filters

    @enable_hist_log_check_based_on_conf(state)
    def _does_hist_log_exist(self, hist_request: QueryData) -> bool:
        """Re-defined function filters historical request if it has been already processed"""
        campaign_detail = hist_request.campaign_detail
        query = f"""
            select id from {HISTORICAL_LOG_TABLE}
            where campaign_type = '{self.flow_name}'
                  and campaign_detail = '{campaign_detail}'
            limit 1;
        """
        logging.info(f"Filtering historical data with query - {query}")
        df = pg_hook.get_pandas_df(query)
        return not df.empty


class TikTokReportLoadProcessor(ReportLoadProcessor):
    def __init__(self, query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type,
                 report_table_name, postgres_hook, **kwargs):
        self.current_run_number = kwargs['dag_run'].conf.get('current_run_number')
        super().__init__(query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type,
                         report_table_name, postgres_hook)

    def _send_tiktok_request(self, query: QueryData, url: str, method: str, json_data: dict):
        """Special method to request the report for TikTok only"""
        request_data = {'headers': {'Content-Type': 'application/json',
                                    'Accept': 'application/json',
                                    'Access-Token': query.access_token},
                        'url': url,
                        'method': method,
                        'json': json_data}
        exc = None
        for tries_made in range(self.retries):
            try:
                response = requests.request(**request_data).json()
            except Exception as e:
                logging.error(f"Network exception during request execution. {e}. {request_data}")
                exc = e
                time.sleep(self.error_sleep_time)
                continue
            status_report_msg = response.get("message")
            if status_report_msg != 'OK':
                logging.error(f'Report is failed due to the following exception - {status_report_msg}')
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:error",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                time.sleep(self.error_sleep_time)
                raise TikTokReportFailed
            else:
                time.sleep(self.success_sleep_time)
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:success",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                return response
        data_to_log = deepcopy(request_data)
        data_to_log.pop('headers')
        msg = f'FLOW - {self.flow_name}. Cant process the request (tries_made={tries_made + 1}) {json.dumps(data_to_log)}. Exception `{exc}`'
        raise OmniFlowFailed(msg)

    def _request_report(self, query: QueryData) -> list:
        """Re-defined function requests single report"""
        tiktok_url = 'https://business-api.tiktok.com/open_api/v1.3/report/integrated/get/'
        data = []
        report = self._send_tiktok_request(query=query,
                                           url=tiktok_url,
                                           method='GET',
                                           json_data=query.query_body)
        report_exists = bool(report.get("data").get("list"))
        if not report_exists:
            logging.info(
                f'report is empty with type {query.query_type} for campaign_id {query.campaign_data.get("external_id")}')
        else:
            # add report data if exists
            data.extend(report.get("data").get("list"))
            total_page = report.get('data').get('page_info').get('total_page')
            if total_page > 1:
                logging.info('Start paginating. Response has more than 1 page...')
                # iterate starting 2 page because 1 page has been added already
                for page in list(range(total_page + 1))[2:]:
                    query.query_body['page'] = page
                    report = self._send_tiktok_request(query=query,
                                                       url=tiktok_url,
                                                       method='GET',
                                                       json_data=query.query_body)
                    data.extend(report.get("data").get("list"))

            data = [{**i["dimensions"], **i["metrics"]} for i in data]
        return data

    def _parse_report_data(self, responses: list, query: QueryData = None) -> DataFrame:
        """Re-defined function handles data before pushing to DB"""
        # parse data if it exists
        if responses:
            report = pd.DataFrame(responses)
            col_mapping = {'stat_time_day': 'date', 'campaign_id': 'campaign_id', 'adgroup_id': 'ad_group_id',
                           'ad_id': 'ad_id', 'spend': 'spend', 'impressions': 'impressions', 'clicks': 'clicks',
                           'conversion': 'platform_conversions', 'video_play_actions': 'video_start',
                           'video_views_p25': 'completed_views_first', 'video_views_p50': 'completed_views_mid',
                           'video_views_p75': 'completed_views_third', 'video_views_p100': 'completed_views_full',
                           'follows': 'paid_follower'}
            report = report.rename(columns=col_mapping)

            # video_views = video_start
            report['video_views'] = report['video_start']

            final_report = report[['date', 'campaign_id', 'ad_group_id', 'ad_id', 'spend', 'impressions', 'clicks',
                                   'platform_conversions', 'video_start', 'completed_views_first', 'completed_views_mid',
                                   'completed_views_third', 'completed_views_full', 'video_views', 'paid_follower']]

            final_report = final_report.drop_duplicates()
            return final_report
        # returns empty df if data is absent
        else:
            return pd.DataFrame([])

    def _create_report_db_query(self, columns: str, values_str: str) -> str:
        """Re-defined function creates upsert sql statement"""
        query = f"""
          INSERT INTO {self.report_table_name} ({columns})
          VALUES {values_str} ON CONFLICT (date,campaign_id,ad_group_id,ad_id)
          DO UPDATE SET completed_views_full=Excluded.completed_views_full, 
          completed_views_first=Excluded.completed_views_first,
          completed_views_mid=Excluded.completed_views_mid, 
          completed_views_third=Excluded.completed_views_third,
          spend=Excluded.spend, 
          video_start=Excluded.video_start,
          video_views=Excluded.video_views, 
          platform_conversions=Excluded.platform_conversions,
          clicks=Excluded.clicks,
          impressions=Excluded.impressions,
          paid_follower=Excluded.paid_follower;"""
        return query

# Wrapping initialised TikTok classes into functions -> get_profiles, get_queries, load_reports


def get_profiles(**kwargs):
    """Get profiles list"""
    logging.info('Starting getting profiles ...')
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    return TikTokProfileProcessor(platform_name=PLATFORM_NAME, **kwargs).get_profiles(config_mock)


@set_config(state)
@set_execution_dates(dates)
def get_queries(profiles: list, **kwargs):  # **kwargs must be present for @set_execution_dates
    """Create queries list based on profiles"""
    logging.info('Starting preparing queries ...')
    return TikTokQueryDataProcessor(profiles=profiles,
                                    dates=dates,
                                    flow_name=FLOW_NAME,
                                    api_fields=API_FIELDS,
                                    template_file='tiktok_template',
                                    sanity_period='SANITY_PULL_30_DAYS',
                                    chunk_historical_query=True,
                                    chunk_periods=28,
                                    historical_period='TWO_YEARS_AGO',
                                    campaigns_plural=False).prepare_queries(**kwargs)


def load_reports(query_data: list, **kwargs):
    """Handle and load reports"""
    logging.info('Starting loading reports...')
    return TikTokReportLoadProcessor(query_data=query_data,
                                     flow_name=FLOW_NAME,
                                     token_header='Access-Token',
                                     retries=2,
                                     error_sleep_time=20,
                                     success_sleep_time=3,
                                     env_type=env_type,
                                     report_table_name=REPORTS_TABLE_NAME,
                                     postgres_hook=pg_hook, **kwargs).process_reports()

# Using DAG context with wrapped functions to run Airflow step by step -> t1, t2, t3, cleanup_xcoms


with DAG(FLOW_NAME,
         description='TikTok OMNI DAG',
         start_date=datetime.datetime(2022, 1, 1),
         schedule_interval=None,
         params=DagConfigSetter(FLOW_NAME).get_params(),
         tags=['ingestion_flow'],
         catchup=False) as dag:

    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )
    t2 = PythonOperator(
        task_id='get_queries',
        on_failure_callback=failure_callback,
        python_callable=get_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="load_reports",
        on_failure_callback=failure_callback,
        python_callable=load_reports,
        op_kwargs={"query_data": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> cleanup_xcoms
