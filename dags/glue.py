import datetime
from typing import List
from airflow import DAG
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.constants import TRIGGER_GLUE_JOB_POD, GLUE_FLOW_NAME
from myn_airflow_lib.custom_operators import CustomGlueJobOperatorNoParallel
from myn_airflow_lib.datadog import init_datadog
from myn_airflow_lib.resolvers import EnvironmentResolver
from airflow.exceptions import AirflowException

from myn_airflow_lib.constants import GLUE_PODS

pg_hook = PostgresHook(postgres_conn_id='uri_reports')
FLOW_NAME = GLUE_FLOW_NAME
env_resolver = EnvironmentResolver()
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
AWS_S3_BUCKET = env_resolver.s3_datalake_bucket
environment = env_resolver.environment
config = dict(retries=dict(max_attempts=10))
glue_client_params = dict(region_name='eu-west-1', config=config)


pre_base_dumping = [
    "pg_dump", "pg_dump_reports"]

pre_base = [
    "ga4_join_preparation", "taxonomy_preparation", "campaignmargins"]

base = [
     "ghost_adform", "ghost_appsflyer", "ghost_cm", "ghost_csv", "integrated_adform", "integrated_appsflyer",
     "integrated_cm", "integrated_cm_linkage", "integrated_cm_paid_search", "integrated_omni_platform",
     "integrated_platform", "ghost_ga4", "integrated_ga4", "brands_and_geo", "platform_dim", "media_plan_dim",
     "media_plan_source"]

final = [
    "adwords_impression_share", "adwords_v2", "aire_historical_rec_report", "auto_rates_filtered",
    "bing_impression_share", "conversions_campaign_level_v3", "druid_v3", "facebook_instagram_v2",
    "historical_budget", "historical_rules_engine_alerts", "offline_tv", "pivot_static", "recommendations"]


with DAG(
        FLOW_NAME,
        schedule_interval=None,
        start_date=datetime.datetime(2021, 1, 1),
        params=DagConfigSetter(FLOW_NAME).get_params(),
        catchup=False,
        max_active_tasks=100
) as dag:
    
    dumping_operator = CustomGlueJobOperatorNoParallel(
        task_id='pre_base_pg_dump',
        env=env_resolver.environment,
        layer='pre_base',
        glue_client_params=glue_client_params,
        job_list=pre_base_dumping,
        with_light_run=False
    )

    pre_base_operator = CustomGlueJobOperatorNoParallel(
        task_id='pre_base_jobs',
        env=env_resolver.environment,
        layer='pre_base',
        glue_client_params=glue_client_params,
        job_list=pre_base,
        with_light_run=True
    )

    base_operator = CustomGlueJobOperatorNoParallel(
        task_id='base_jobs',
        env=env_resolver.environment,
        layer='base',
        glue_client_params=glue_client_params,
        job_list=base,
        with_light_run=True
    )

    final_operator = CustomGlueJobOperatorNoParallel(
        task_id='final_jobs',
        env=env_resolver.environment,
        layer='final',
        glue_client_params=glue_client_params,
        job_list=final,
        with_light_run=True
    )

    dumping_operator >> pre_base_operator >> base_operator >> final_operator
