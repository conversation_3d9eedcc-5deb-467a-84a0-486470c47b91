import datetime
import itertools
import json
import logging
import re
from io import String<PERSON>
from typing import List, Dict, Union, Iterator, NoReturn, Any
from airflow_common.dag_config_setter import DagConfigSetter
from msal import ConfidentialClientApplication
import pandas as pd
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from imap_tools import MailBox, AND, A
from pandas import Data<PERSON>rame

from myn_airflow_lib.commons import failure_callback
from myn_airflow_lib.constants import EXECUTOR_STABLE_NODES
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog

pg_hook = PostgresHook(postgres_conn_id='uri_reports')
FLOW_NAME = 'OFFLINE_TV'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
WAIT_SLEEP = 100
REPORTS_TABLE_NAME = 'reports_offline_tv_performance'
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)


def get_access_token():
    tenant_id = env_resolver.reports_tenant_id
    authority = 'https://login.microsoftonline.com/' + tenant_id
    client_id = env_resolver.reports_client_id
    client_secret = env_resolver.reports_client_secret
    scope = ['https://outlook.office365.com/.default']
    app = ConfidentialClientApplication(client_id, authority=authority, 
          client_credential = client_secret)
    access_token = app.acquire_token_for_client(scopes=scope)
    print(access_token)
    return access_token['access_token']


def _init_mailbox() -> MailBox:
    """Initialize mailbox"""
    return MailBox('outlook.office365.com').xoauth2('<EMAIL>', access_token=get_access_token(), initial_folder='OFFLINE_TV_REPORTS')


def _check_empty_mailbox(iterable: Iterator) -> Union[Iterator, None]:
    """Checks if mailbox generator has message objects"""
    try:
        first = next(iterable)
    except StopIteration:
        return None
    return first, itertools.chain([first], iterable)


def _validate_reqs(message_attrs: Dict[str, bool], check_header: bool = True) -> bool:
    """Validate email requirements"""

    uid = message_attrs.pop('uid')
    date = message_attrs.pop('date')
    validation_type = 'header' if check_header else 'attachment'
    statuses = ('FAILED.', 'SUCCEEDED.')
    message_header = f'{FLOW_NAME}. Message id = {uid}, date = {date}. Validation {validation_type}'
    success_message_body = 'Company id is present, attachment is present' if check_header else 'Attachment is CSV'
    message_mapp = dict(has_to=f'{message_header} - {statuses[0]} Company id is absent in email header (to)',
                        has_attachment=f'{message_header} - {statuses[0]} Attachment is absent in email',
                        has_csv=f'{message_header} - {statuses[0]} Attachment is not a CSV')

    for key, bool_value in message_attrs.items():

        if not bool_value:
            logging.error(message_mapp.get(key))

            return False

    logging.info(f'{message_header} - {statuses[1]} {success_message_body}')
    return True


def _parse_payload(payload: Dict[str, str]) -> DataFrame:
    """Cleansing the dataframe"""
    logging.info("Starting parsing email")
    df = pd.read_csv(StringIO(payload['payload']), sep=';')
    company_id = payload['company_id']

    df = df.assign(company_id=company_id)

    # columns renaming
    col_rename_map = {'livello1 <Rig/Col>': 'livello_1', 'livello2 <Rig/Col>': 'livello_2',
                      'Azienda Proprietaria <Rig/Col>': 'azienda_proprietaria', 'Prodotto <Rig/Col>': 'prodotto',
                      'Brand <Rig/Col>': 'brand', 'Marca <Rig/Col>': 'marca',
                      'Filmato SpotSystem <Rig/Col>': 'filmato_spot_system', 'Filmato <Rig/Col>': 'filmato',
                      'Concessionario <Rig/Col>': 'concessionario', 'Tipo Rete <Rig/Col>': 'tipo_rete',
                      'Rete <Rig/Col>': 'rete', 'Tipo Pubblicità <Rig/Col>': 'tipo_pubblicita',
                      'Durata Spot <Rig/Col>': 'durata_spot', 'Mese auditel <Rig/Col>': 'mese_auditel',
                      'Settimana Auditel <Rig/Col>': 'settimana_auditel', 'Raggr. Fasce <Rig/Col>': 'raggr_fasce',
                      'Grp target : Adulti 15+ <Dati>': 'grp_target_adulti_15_plus', 'Avvisi <Dati>': 'avvisi'}
    df = df.rename(columns=col_rename_map)

    df['grp_target_adulti_15_plus'] = df['grp_target_adulti_15_plus'].str.replace(r',', '.')

    # retrieve last date and format it
    df['date'] = df['settimana_auditel'].str.split(pat=' - ').str[-1]
    df['date'] = pd.to_datetime(df.date, format='%d-%m-%Y').dt.strftime('%Y-%m-%d')

    # columns ordering
    df_cleared = df[['company_id', 'date', 'livello_1', 'livello_2', 'azienda_proprietaria', 'prodotto', 'brand',
                     'marca', 'filmato_spot_system', 'filmato', 'concessionario', 'tipo_rete', 'rete',
                     'tipo_pubblicita', 'durata_spot', 'mese_auditel', 'settimana_auditel', 'raggr_fasce',
                     'grp_target_adulti_15_plus', 'avvisi']]

    return df_cleared


def _get_db_value(value: Any) -> str:
    """Stringify value"""
    if isinstance(value, (dict, list)):
        return f"'{json.dumps(value)}'"
    elif isinstance(value, str):
        return f"'{value}'"
    elif value is None:
        return 'NULL'
    else:
        return str(value)


def _execute_sql_query(report: DataFrame) -> NoReturn:
    """Prepare and runs sql UPSERT query"""
    logging.info("Starting pushing to DB")

    columns = ', '.join(report.columns)

    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""INSERT INTO {REPORTS_TABLE_NAME} ({columns})
                    VALUES
                    {values_str} ON CONFLICT (company_id, date, livello_1, livello_2, azienda_proprietaria, prodotto,
                     brand, marca, filmato_spot_system, filmato, concessionario, tipo_rete, rete, tipo_pubblicita,
                      durata_spot, mese_auditel, settimana_auditel, raggr_fasce)
                    DO UPDATE
                    SET grp_target_adulti_15_plus=Excluded.grp_target_adulti_15_plus, 
                    avvisi=Excluded.avvisi;
                  """
    pg_hook.run(query, True)


def get_message_ids() -> List:
    """Gets all unseen message ids"""

    logging.info("Starting retrieving emails ids")

    with _init_mailbox() as mailbox:

        mailbox_fetched = mailbox.fetch(criteria=AND(seen=False), bulk=True)

        checked_empty_mailbox = _check_empty_mailbox(mailbox_fetched)
        if checked_empty_mailbox is None:

            logging.info("Gmail mailbox has no any new messages")
            return []
        else:
            # return first checked element back to generator after checking it
            _, mailbox_fetched = checked_empty_mailbox
            emails_ids = [[message.uid] for message in mailbox_fetched]

            return emails_ids


def process_email(msg_id: str) -> NoReturn:
    """Retrieves, process and push emails"""

    logging.info("Starting parsing emails")
    with _init_mailbox() as mailbox:
        mailbox_message = mailbox.fetch(A(uid=msg_id))

        for msg_attr in mailbox_message:
            to_attr_match = re.search(r'[+](\d+)[@]', msg_attr.to[0])
            to_attr = to_attr_match.group(1) if to_attr_match is not None else None
            msg_attachments_attr = msg_attr.attachments
            header_dict = dict(uid=msg_attr.uid,
                               date=msg_attr.date_str,
                               has_to=bool(to_attr),
                               has_attachment=bool(msg_attachments_attr)
                               )
            is_valid_header = _validate_reqs(header_dict)
            if is_valid_header:
                for attachment in msg_attachments_attr:
                    attachment_dict = dict(uid=msg_attr.uid,
                                           date=msg_attr.date_str,
                                           has_csv='.csv' in attachment.filename
                                           )
                    is_valid_attachment = _validate_reqs(attachment_dict, check_header=False)
                    if is_valid_attachment:
                        payload = dict(company_id=to_attr,
                                       payload=attachment.payload.decode())
                        parsed_payload = _parse_payload(payload)
                        _execute_sql_query(parsed_payload)
                        increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, parsed_payload.shape[0],
                                         [f"table:{REPORTS_TABLE_NAME}"])


with DAG(FLOW_NAME,
         description='offline_tv flow dag',
         schedule_interval=None,
         start_date=datetime.datetime(2022, 1, 1),
         params=DagConfigSetter(FLOW_NAME).get_params(),
         tags=['ingestion_flow'],
         catchup=False) as dag:
    t1 = PythonOperator(
        task_id='get_message_ids',
        python_callable=get_message_ids,
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator.partial(
        task_id='process_email',
        on_failure_callback=failure_callback,
        python_callable=process_email,
        executor_config=EXECUTOR_STABLE_NODES
    ).expand(op_args=XComArg(t1))

    t1 >> t2
