import logging
import datetime
import os
from airflow.operators.python import ShortCircuitOperator
from airflow.models import DAG
from airflow.models.param import Param
from airflow.utils.task_group import TaskGroup
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from kubernetes.client import models as k8s
from myn_airflow_lib.commons import failure_callback
from myn_airflow_lib.resolvers import EnvironmentResolver

resolver = EnvironmentResolver()

DEFAULT_TIMEZONE_GROUP = str(os.environ.get('TIMEZONE_GROUP', '1'))  # fallback default

rules_engine_memory_request_map = {
    '1': os.getenv("RULES_ENGINE_MEMORY_CET", "13Gi"),
    '2': os.getenv("RULES_ENGINE_MEMORY_EDT", "2Gi")
}

APP_CONFIGS = {
    "rules-engine": {
        "namespace": os.getenv("RULES_ENGINE_NAMESPACE", "ds"),
        "service_account": os.getenv("RULES_ENGINE_SA", "ds"),
        "cpu_request": os.getenv("RULES_ENGINE_CPU_REQUEST", "1800m"),
        "cpu_limit": os.getenv("RULES_ENGINE_CPU_LIMIT", "1800m"),
        "memory_request_limit": rules_engine_memory_request_map[DEFAULT_TIMEZONE_GROUP],
        "configmap_app": os.getenv("RULES_ENGINE_CONFIGMAP", "rules-engine"),
        "node_selector": os.getenv("RULES_ENGINE_NODE_SELECTOR", "dag"),
    },
    "bagger": {
        "namespace": os.getenv("BAGGER_NAMESPACE", "ds"),
        "service_account": os.getenv("BAGGER_SA", "ds"),
        "cpu_request": os.getenv("BAGGER_CPU_REQUEST", "500m"),
        "cpu_limit": os.getenv("BAGGER_CPU_LIMIT", "1000m"),
        "memory_request_limit": os.getenv("BAGGER_MEMORY", "6Gi"),
        "configmap_app": os.getenv("BAGGER_CONFIGMAP", "bagger"),
        "node_selector": os.getenv("BAGGER_NODE_SELECTOR", "dag"),
    },
}


def create_all_images():
    re_hash = str(os.environ.get('RULES-ENGINE_VER'))
    bagger_hash = str(os.environ.get('BAGGER_VER'))

    images_to_create = {
        "rules-engine": f'{os.getenv("ECR_DOMAIN", "************.dkr.ecr.eu-west-1.amazonaws.com")}/rules-engine:{re_hash}',
        "bagger": f'{os.getenv("ECR_DOMAIN", "************.dkr.ecr.eu-west-1.amazonaws.com")}/bagger:{bagger_hash}',
    }
    logging.info(f'Pulling the following images for the repos \n {str(images_to_create)}')
    return images_to_create


def k8s_pod_op_factory(app_name: str, image: str, timezone_group: str) -> KubernetesPodOperator:
    config = APP_CONFIGS[app_name]

    return KubernetesPodOperator(
        task_id=f'k8s_pod_{app_name}',
        namespace=config["namespace"],
        name=app_name,
        startup_timeout_seconds=300,
        image=image,
        labels={"app": app_name},
        service_account_name=config["service_account"],
        is_delete_operator_pod=True,
        in_cluster=True,
        image_pull_policy="Always",
        get_logs=True,
        env_vars={
            "TIMEZONE_GROUP": timezone_group,
            "S3_DATALAKE_BUCKET": str(resolver.s3_datalake_bucket),
            "STACK": str(resolver.environment),
            "ENVIRONMENT": str(resolver.environment),
        },
        env_from=[
            k8s.V1EnvFromSource(config_map_ref=k8s.V1ConfigMapEnvSource(name="infra")),
            k8s.V1EnvFromSource(config_map_ref=k8s.V1ConfigMapEnvSource(name=config["configmap_app"]))
        ],
        container_resources=k8s.V1ResourceRequirements(
            requests={"cpu": config["cpu_request"], "memory": config["memory_request_limit"]},
            limits={"cpu": config["cpu_limit"], "memory": config["memory_request_limit"]}
        ),
        node_selector={config["node_selector"]: "true"},
        tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key=config["node_selector"],
                operator="Equal",
                value="true"
            )
        ]
    )


with DAG(
    dag_id='k8s_ds_jobs',
    schedule_interval=None,
    description="dag to trigger ds jobs",
    start_date=datetime.datetime(2022, 1, 1),
    catchup=False,
    on_failure_callback=failure_callback,
    params={
        "TIMEZONE_OVERRIDE": Param(DEFAULT_TIMEZONE_GROUP, type="string"),
        "rules_engine_enabled": Param(True, type="boolean"),
        "bagger_enabled": Param(True, type="boolean"),
    }
) as dag:

    with TaskGroup("k8s_tasks") as task_group:
        images = create_all_images()

        for app_name, image in images.items():
            param_key = f"{app_name.replace('-', '_')}_enabled"
            task_id = f"k8s_pod_{app_name}"

            def _should_run(param=param_key, **kwargs):
                return kwargs['params'].get(param, True)

            check_task = ShortCircuitOperator(
                task_id=f"check_enabled_{app_name}",
                python_callable=_should_run,
                provide_context=True
            )

            timezone_group = f"{{{{ (dag_run.conf.get('TIMEZONE_OVERRIDE') | default(dag_run.conf.get('current_run_number', '{DEFAULT_TIMEZONE_GROUP}'), true)) | string }}}}"



            # 3. Create the K8s task
            pod_task = k8s_pod_op_factory(app_name, image, timezone_group)
            check_task >> pod_task
