
import datetime
from airflow import DAG
from myn_airflow_lib.custom_operators import CustomGlueJobOperatorNoParallel
from myn_airflow_lib.resolvers import EnvironmentResolver

FLOW_NAME = "PgDumping"
env_resolver = EnvironmentResolver()
config = dict(retries=dict(max_attempts=10))
glue_client_params = dict(region_name='eu-west-1', config=config)

dump_jobs_ids = [
    "pg_dump", "pg_dump_reports"
]

with DAG(
        FLOW_NAME,
        schedule=None,
        start_date=datetime.datetime(2021, 1, 1),
        catchup=False,\
) as dag:
    
    dump_jobs = CustomGlueJobOperatorNoParallel(
        task_id='dump_jobs',
        env=env_resolver.environment,
        layer='pre_base',
        glue_client_params=glue_client_params,
        job_list=dump_jobs_ids
    )

    dump_jobs