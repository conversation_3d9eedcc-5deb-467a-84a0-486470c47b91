import logging
import requests
import time
import datetime
import json
import pandas as pd

from pandas import DataFrame
from copy import deepcopy
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook

from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.commons import (set_config, set_execution_dates, failure_callback,
                                     enable_hist_log_check_based_on_conf)
from myn_airflow_lib.constants import EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.datadog import init_datadog, increment_metric
from myn_airflow_lib.exceptions import OmniFlowFailed

from omni.utils import QueryData
from omni.processors import ProfileProcessor, QueryDataProcessor, ReportLoadProcessor, ReportOrderProcessor


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True

state = {
    'dates': {}
}
dates = state['dates']

env_resolver = EnvironmentResolver()
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)

FLOW_NAME = 'Amazon'
PLATFORM_NAME = 'amazon_integrated'
API_FIELDS = ["totalCost", "clickThroughs", "impressions", "videoComplete", "videoFirstQuartile", "videoMidpoint",
              "videoThirdQuartile", "videoStart"]

env_resolver = EnvironmentResolver()
env_type = env_resolver.environment

REPORTS_TABLE_NAME = 'reports_amazon_performance'
AMAZON_CLIENT_ID = 'amzn1.application-oa2-client.a986cd7c5a684d86944169c4fc13f817'
REGION_MAPPING = dict(north_america='https://advertising-api.amazon.com',
                      europe='https://advertising-api-eu.amazon.com',
                      far_east='https://advertising-api-fe.amazon.com')


# Init -> AmazonProfileProcessor, AmazonQueryDataProcessor, AmazonReportOrderProcessor, AmazonReportLoadProcessor

class AmazonProfileProcessor(ProfileProcessor):
    def __init__(self, platform_name, **kwargs):
        super().__init__(platform_name, **kwargs)


class AmazonQueryDataProcessor(QueryDataProcessor):
    def __init__(self, profiles, dates, flow_name, api_fields, template_file, sanity_period, chunk_historical_query,
                 chunk_periods, historical_period, campaigns_plural):
        super().__init__(profiles, dates, flow_name, api_fields, template_file, sanity_period, chunk_historical_query,
                         chunk_periods, historical_period, campaigns_plural)

    def _create_query_body(self, campaigns: list, date_from: str, date_to: str,
                           account_id: str = None) -> dict:
        """Re-defined function creates filters"""
        filters = self._read_query_template()
        filters["startDate"] = date_from
        filters["endDate"] = date_to
        filters["metrics"] = self.api_fields
        return filters

    @enable_hist_log_check_based_on_conf(state)
    def _does_hist_log_exist(self, hist_request: QueryData) -> bool:
        """Re-defined function filters historical request if it has been already processed"""
        campaign_detail = hist_request.campaign_detail
        query = f"""
                    select id from {HISTORICAL_LOG_TABLE}
                    where campaign_type = '{self.flow_name}'
                          and campaign_detail = '{campaign_detail}'
                    limit 1;
                """
        logging.info(f"Filtering historical data with query - {query}")
        df = pg_hook.get_pandas_df(query)
        return not df.empty


class AmazonReportOrderProcessor(ReportOrderProcessor):
    def __init__(self, query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type, **kwargs):
        self.current_run_number = kwargs['dag_run'].conf.get('current_run_number')
        super().__init__(query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type)

    def _create_report_id(self, query_data) -> str:
        """Creates report id by calling API"""
        query = QueryData(**query_data)
        bad_statuses = [401, 403, 429, 500, 502, 504, 503]
        request_data = {'headers': {'Content-Type': 'application/json',
                                    'Accept': 'application/vnd.dspcreatereports.v3+json',
                                    'Amazon-Advertising-API-ClientId': AMAZON_CLIENT_ID,
                                    'Authorization': f'Bearer {query.access_token}'},
                        'url': f'{REGION_MAPPING[query.region]}/accounts/{query.account_id}/dsp/reports',
                        'method': 'POST',
                        'json': query.query_body}
        exc = None
        for tries_made in range(self.retries):
            try:
                response = requests.request(**request_data)
            except Exception as e:
                logging.error(f"Network exception during request execution. {e}. {request_data}")
                exc = e
                time.sleep(self.error_sleep_time)
                continue
            if response.status_code in bad_statuses:
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:error",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                logging.warning(f'send request with data = {json.dumps(request_data)} \n'
                                f'received {response.status_code}')
                time.sleep(self.error_sleep_time)
            else:
                time.sleep(self.success_sleep_time)
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:success",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                return response.json()['reportId']
        data_to_log = deepcopy(request_data)
        data_to_log.pop('headers')
        msg = f'FLOW - {self.flow_name}. Cant process the request (tries_made={tries_made + 1}) {json.dumps(data_to_log)}. Exception `{exc}`'
        raise OmniFlowFailed(msg)

    def _check_report_status(self, query_data: dict, report_id: str) -> str:
        """Checks report status by calling API"""
        query = QueryData(**query_data)
        bad_statuses = [401, 403, 429, 500, 502, 504, 503]
        request_data = {'headers': {'Content-Type': 'application/json',
                                    'Accept': 'application/vnd.dspgetreports.v3+json',
                                    'Amazon-Advertising-API-ClientId': AMAZON_CLIENT_ID,
                                    'Authorization': f'Bearer {query.access_token}'},
                        'url': f'{REGION_MAPPING[query.region]}/accounts/{query.account_id}/dsp/reports/{report_id}',
                        'method': 'GET'}
        exc = None
        for tries_made in range(self.retries):
            try:
                response = requests.request(**request_data)
            except Exception as e:
                logging.error(f"Network exception during request execution. {e}. {request_data}")
                exc = e
                time.sleep(self.error_sleep_time)
                continue
            if response.status_code in bad_statuses:
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:error",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                logging.warning(f'send request with data = {json.dumps(request_data)} \n'
                                f'received {response.status_code}')
                time.sleep(self.error_sleep_time)
            else:
                time.sleep(self.success_sleep_time)
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:success",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                resp_json = response.json()
                if resp_json['status'] in ['PENDING', 'IN_PROGRESS', 'PROCESSING']:
                    logging.info(f'Status - {resp_json["status"]}. Getting link is still in progress for report id = {report_id}')
                    time.sleep(self.error_sleep_time)
                    continue
                elif resp_json['status'] == 'SUCCESS':
                    report_link = resp_json['location']
                    logging.info(f"Report link received successfully - {report_link}")
                    return report_link
        data_to_log = deepcopy(request_data)
        data_to_log.pop('headers')
        msg = f'FLOW - {self.flow_name}. Cant process the request (tries_made={tries_made + 1}) {json.dumps(data_to_log)}. Exception `{exc}`'
        raise OmniFlowFailed(msg)

    def order_report(self, query_data: dict) -> str:
        """Order single report by processing single query and returns report link."""
        logging.info('Creating report ID')
        report_id = self._create_report_id(query_data)
        logging.info('Checking report status')
        report_link = self._check_report_status(query_data, report_id)
        return report_link


class AmazonReportLoadProcessor(ReportLoadProcessor):
    def __init__(self, query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type,
                 report_table_name, postgres_hook, **kwargs):
        self.current_run_number = kwargs['dag_run'].conf.get('current_run_number')
        super().__init__(query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type,
                         report_table_name, postgres_hook)

    def _request_report(self, query: QueryData) -> list:
        """Re-defined function requests single report"""
        amazon_url = query.report_link
        response = self._send_request(query=query,
                                      url=amazon_url,
                                      method='GET')
        report_data = response.json()
        if bool(report_data):
            return report_data
        else:
            logging.info(
                f'report is empty with type {query.query_type} for account_id {query.account_id}')
            return []

    def _parse_report_data(self, report_data: list, query: QueryData = None) -> DataFrame:
        """Re-defined function handles data before pushing to DB"""
        # parse data if it exists
        if report_data:
            report = pd.DataFrame(report_data)

            col_mapping = {'date': 'date', 'orderCurrency': 'currency', 'orderId': 'campaign_id',
                           'lineItemId': 'ad_group_id', 'creativeAdId': 'ad_id', 'totalCost': 'spend',
                           'clickThroughs': 'clicks', 'impressions': 'impressions',
                           'videoComplete': 'completed_views_full', 'videoFirstQuartile': 'completed_views_first',
                           'videoMidpoint': 'completed_views_mid', 'videoThirdQuartile': 'completed_views_third',
                           'videoStart': 'video_start'}
            report = report.rename(columns=col_mapping)

            # convert date to string
            report['date'] = pd.to_datetime(report['date'], unit='ms').dt.strftime('%Y-%m-%d')

            final_report = report[['date', 'campaign_id', 'ad_group_id', 'ad_id', 'spend', 'clicks',
                                   'impressions', 'completed_views_full', 'completed_views_first',
                                   'completed_views_mid', 'completed_views_third', 'video_start']]

            final_report = final_report.drop_duplicates()
            return final_report
        # returns empty df if data is absent
        else:
            return pd.DataFrame([])

    def _create_report_db_query(self, columns: str, values_str: str) -> str:
        """Re-defined function creates upsert sql statement"""
        query = f"""
          INSERT INTO {self.report_table_name} ({columns})
          VALUES {values_str} ON CONFLICT (date,campaign_id,ad_group_id,ad_id)
          DO UPDATE SET completed_views_full=Excluded.completed_views_full, 
          completed_views_first=Excluded.completed_views_first,
          completed_views_mid=Excluded.completed_views_mid, 
          completed_views_third=Excluded.completed_views_third,
          spend=Excluded.spend, 
          video_start=Excluded.video_start,
          clicks=Excluded.clicks,
          impressions=Excluded.impressions; """
        return query


# Wrapping initialised Amazon classes into functions -> get_profiles, get_queries, order_reports, load_reports


def get_profiles(**kwargs):
    """Get profiles list"""
    logging.info('Starting getting profiles ...')
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    return AmazonProfileProcessor(platform_name=PLATFORM_NAME, **kwargs).get_profiles(config_mock)


@set_config(state)
@set_execution_dates(dates)
def get_queries(profiles: list, **kwargs):  # **kwargs must be present for @set_execution_dates
    """Create queries list based on profiles"""
    logging.info('Starting preparing queries ...')
    return AmazonQueryDataProcessor(profiles=profiles,
                                    dates=dates,
                                    flow_name=FLOW_NAME,
                                    api_fields=API_FIELDS,
                                    template_file='amazon_template',
                                    sanity_period='SANITY_PULL_30_DAYS',
                                    chunk_historical_query=True,
                                    chunk_periods=29,
                                    historical_period='89_DAYS_AGO',
                                    campaigns_plural=True).prepare_queries(**kwargs)


def order_reports(query_data: list, **kwargs):
    """Order reports based on queries list"""
    logging.info('Starting ordering reports...')
    return AmazonReportOrderProcessor(query_data=query_data,
                                      flow_name=FLOW_NAME,
                                      token_header='Authorization',
                                      retries=5,
                                      error_sleep_time=60,
                                      success_sleep_time=5,
                                      env_type=env_type, **kwargs).order_reports()


def load_reports(query_data: list, **kwargs):
    """Handle and load reports"""
    logging.info('Starting loading reports...')
    return AmazonReportLoadProcessor(query_data=query_data,
                                     flow_name=FLOW_NAME,
                                     token_header='Access-Token',
                                     retries=3,
                                     error_sleep_time=30,
                                     success_sleep_time=5,
                                     env_type=env_type,
                                     report_table_name=REPORTS_TABLE_NAME,
                                     postgres_hook=pg_hook, **kwargs).process_reports()

# Using DAG context with wrapped functions to run Airflow step by step -> t1, t2, t3, t4 cleanup_xcoms


with DAG(FLOW_NAME,
         description='Amazon OMNI DAG',
         start_date=datetime.datetime(2022, 1, 1),
         schedule_interval=None,
         tags=['ingestion_flow'],
         params=DagConfigSetter(FLOW_NAME).get_params(),
         catchup=False) as dag:

    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )
    t2 = PythonOperator(
        task_id='get_queries',
        on_failure_callback=failure_callback,
        python_callable=get_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="order_reports",
        on_failure_callback=failure_callback,
        python_callable=order_reports,
        op_kwargs={"query_data": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t4 = PythonOperator(
        task_id="load_reports",
        on_failure_callback=failure_callback,
        python_callable=load_reports,
        op_kwargs={"query_data": XComArg(t3)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> t4 >> cleanup_xcoms
