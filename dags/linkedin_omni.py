import logging
import datetime
import pandas as pd
from airflow_common.dag_config_setter import DagConfigSetter
import numpy as np
from pandas import DataFrame
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import <PERSON>gresHook

from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.commons import (set_config, set_execution_dates, failure_callback,
                                     enable_hist_log_check_based_on_conf)
from myn_airflow_lib.constants import EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.datadog import init_datadog
from omni.utils import QueryData
from omni.processors import ProfileProcessor, QueryDataProcessor, ReportLoadProcessor


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True

state = {
    'dates': {}
}
dates = state['dates']

env_resolver = EnvironmentResolver()
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)

FLOW_NAME = 'Linkedin'
PLATFORM_NAME = 'linkedin_integrated'
API_FIELDS = ['externalWebsiteConversions', 'dateRange', 'costInLocalCurrency', 'clicks', 'impressions',
              'videoCompletions', 'videoFirstQuartileCompletions', 'videoMidpointCompletions',
              'videoThirdQuartileCompletions', 'videoStarts', 'videoViews', 'pivotValues']

env_resolver = EnvironmentResolver()
env_type = env_resolver.environment

REPORTS_TABLE_NAME = 'reports_linkedin_performance'


# LinkedIn classes initialisation -> LinkedinProfileProcessor, LinkedinQueryDataProcessor, LinkedinReportLoadProcessor

class LinkedinProfileProcessor(ProfileProcessor):
    def __init__(self, platform_name, **kwargs):
        super().__init__(platform_name, **kwargs)


class LinkedinQueryDataProcessor(QueryDataProcessor):
    def __init__(self, profiles, dates, flow_name, api_fields, template_file, sanity_period, chunk_historical_query,
                 chunk_periods, historical_period, campaigns_plural):
        super().__init__(profiles, dates, flow_name, api_fields, template_file, sanity_period, chunk_historical_query,
                         chunk_periods, historical_period, campaigns_plural)

    def _create_query_body(self, campaign: dict, date_from: str, date_to: str, account_id: str = None) -> dict:
        """Re-defined function creates filters"""
        filters = self._read_query_template()
        date_from_year, date_from_month, date_from_day = date_from.split('-')
        date_to_year, date_to_month, date_to_day = date_to.split('-')
        filters["dateRange.start.month"] = date_from_month
        filters["dateRange.start.day"] = date_from_day
        filters["dateRange.start.year"] = date_from_year
        filters["dateRange.end.month"] = date_to_month
        filters["dateRange.end.day"] = date_to_day
        filters["dateRange.end.year"] = date_to_year
        filters["campaigns"] = f"urn:li:sponsoredCampaign:{campaign['external_id']}"
        filters["accounts"] = f"urn:li:sponsoredAccount:{account_id}"
        filters["fields"] = ",".join(self.api_fields)
        return filters

    @enable_hist_log_check_based_on_conf(state)
    def _does_hist_log_exist(self, hist_request: QueryData) -> bool:
        """Re-defined function filters historical request if it has been already processed"""
        campaign_detail = hist_request.campaign_detail
        query = f"""
            select id from {HISTORICAL_LOG_TABLE}
            where campaign_type = '{self.flow_name}'
                  and campaign_detail = '{campaign_detail}'
            limit 1;
        """
        logging.info(f"Filtering historical data with query - {query}")
        df = pg_hook.get_pandas_df(query)
        return not df.empty


class LinkedinReportLoadProcessor(ReportLoadProcessor):
    def __init__(self, query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type,
                 report_table_name, postgres_hook, **kwargs):
        self.current_run_number = kwargs['dag_run'].conf.get('current_run_number')
        super().__init__(query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type,
                         report_table_name, postgres_hook)

    def _request_report(self, query: QueryData) -> list:
        """Re-defined function requests single report"""
        data = []
        response = self._send_request(query=query,
                                      url='https://api.linkedin.com/v2/adAnalyticsV2/',
                                      method='GET',
                                      params=query.query_body)
        report = response.json()
        report_exists = bool(report.get("elements"))
        if not report_exists:
            logging.info(
                f'report is empty with type {query.query_type} for campaign_id {query.campaign_data.get("external_id")}')
        else:
            # add report data if exists
            data.extend(report.get("elements"))
        return data

    def _parse_report_data(self, responses: list, query: QueryData = None) -> DataFrame:
        """Re-defined function handles data before pushing to DB"""
        col_mapping = {'date': 'date', 'campaign_id': 'campaign_id', 'ad_group_id': 'ad_group_id', 'ad_id': 'ad_id',
                       'videoCompletions': 'completed_views_full',
                       'videoFirstQuartileCompletions': 'completed_views_first',
                       'videoMidpointCompletions': 'completed_views_mid',
                       'videoThirdQuartileCompletions': 'completed_views_third',
                       'costInLocalCurrency': 'spend', 'videoStarts': 'video_start', 'videoViews': 'video_views',
                       'externalWebsiteConversions': 'platform_conversions', 'clicks': 'clicks',
                       'impressions': 'impressions'}
        cleared_data = []
        # clear each dict with dimensions and metrics
        for item in responses:
            # handle date col
            date_dict = item['dateRange']['start']
            date = datetime.datetime(
                year=date_dict['year'], month=date_dict['month'], day=date_dict['day']).strftime('%Y-%m-%d')
            item['date'] = date
            # handle dimensions
            item['ad_group_id'] = int(item['pivotValues'][0].split(':')[-1])
            item['campaign_id'] = int(item['pivotValues'][1].split(':')[-1])
            item['ad_id'] = int(item['pivotValues'][2].split(':')[-1])
            # remove useless keys
            item.pop('pivotValues')
            item.pop('dateRange')
            cleared_data.append(item)
        df = pd.DataFrame(cleared_data, columns=list(col_mapping.keys()))
        final_report = df.rename(columns=col_mapping)
        final_report = final_report.replace({np.nan: None})
        final_report = final_report.drop_duplicates()
        return final_report

    def _create_report_db_query(self, columns: str, values_str: str) -> str:
        """Re-defined function creates upsert sql statement"""
        query = f"""
          INSERT INTO {self.report_table_name} ({columns})
          VALUES {values_str} ON CONFLICT (date,campaign_id,ad_group_id,ad_id)
          DO UPDATE SET completed_views_full=Excluded.completed_views_full, 
          completed_views_first=Excluded.completed_views_first,
          completed_views_mid=Excluded.completed_views_mid, 
          completed_views_third=Excluded.completed_views_third,
          spend=Excluded.spend, 
          video_start=Excluded.video_start,
          video_views=Excluded.video_views, 
          platform_conversions=Excluded.platform_conversions,
          clicks=Excluded.clicks,
          impressions=Excluded.impressions; """
        return query

# Wrapping initialised LinkedIn classes into functions -> get_profiles, get_queries, load_reports


def get_profiles(**kwargs):
    """Get profiles list"""
    logging.info('Starting getting profiles ...')
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    return LinkedinProfileProcessor(platform_name=PLATFORM_NAME, **kwargs).get_profiles(config_mock)


@set_config(state)
@set_execution_dates(dates)
def get_queries(profiles: list, **kwargs):  # **kwargs must be present for @set_execution_dates
    """Create queries list based on profiles"""
    logging.info('Starting preparing queries ...')
    return LinkedinQueryDataProcessor(profiles=profiles,
                                      dates=dates,
                                      flow_name=FLOW_NAME,
                                      api_fields=API_FIELDS,
                                      template_file='linkedin_template',
                                      sanity_period='SANITY_PULL',
                                      chunk_historical_query=False,
                                      chunk_periods=None,
                                      historical_period='TWO_YEARS_AGO',
                                      campaigns_plural=False).prepare_queries(**kwargs)


def load_reports(query_data: list, **kwargs):
    """Handle and load reports"""
    logging.info('Starting loading reports...')
    return LinkedinReportLoadProcessor(query_data=query_data,
                                       flow_name=FLOW_NAME,
                                       token_header='Authorization',
                                       retries=3,
                                       error_sleep_time=30,
                                       success_sleep_time=3,
                                       env_type=env_type,
                                       report_table_name=REPORTS_TABLE_NAME,
                                       postgres_hook=pg_hook, **kwargs).process_reports()

# Using DAG context with wrapped functions to run Airflow step by step -> t1, t2, t3, cleanup_xcoms


with DAG(FLOW_NAME,
         description='LINKEDIN OMNI DAG',
         start_date=datetime.datetime(2022, 1, 1),
         schedule_interval=None,
         params=DagConfigSetter(FLOW_NAME).get_params(),
         tags=['ingestion_flow'],
         catchup=False) as dag:

    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )
    t2 = PythonOperator(
        task_id='get_queries',
        on_failure_callback=failure_callback,
        python_callable=get_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="load_reports",
        on_failure_callback=failure_callback,
        python_callable=load_reports,
        op_kwargs={"query_data": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> cleanup_xcoms
