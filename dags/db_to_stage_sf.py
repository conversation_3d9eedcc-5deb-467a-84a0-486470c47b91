import datetime
import logging
import traceback
import json

import pandas as pd
import numpy as np
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.snowflake.hooks.snowflake import SnowflakeHook
from airflow.utils.task_group import TaskGroup
from myn_airflow_lib.commons import failure_callback, fetch_avg_durations
from myn_airflow_lib.constants import DATABASES_INFO, DatabaseInfo, TableInfo
from myn_airflow_lib.exceptions import SnowflakeFailed
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.snowflake_commons import log_table_entry, track_staging_load
from myn_airflow_lib.snowflake_constants import (
    TRANSACTIONAL_SOURCE,
    JOB_START_ENTRY,
    DATA_UPLOAD_START_ENTRY,
    PERFORMANCE_SOURCE,
    FINISH_METADATA_STATUS,
    START_METADATA_STATUS,
)
from kubernetes.client import models as k8s
from pandas import DataFrame as PandasDataFrame, DataFrame
from snowflake.connector.pandas_tools import write_pandas

env_resolver = EnvironmentResolver()

SNOWFLAKE_HOOK = SnowflakeHook(
    snowflake_conn_id="snowflake_dbt",
    warehouse=env_resolver.snowflake_write_warehouse
)

# Chunk size is tradeoff, need to test different sizes to find best-performance one
CHUNK_SIZE = 1_000_000
CUSTOM_CHUNK_SIZES = {"service_responsivesearchadmodel": 500_000}
DAG_ID = "Postgres_Dump_to_Snowflake"

# === Dynamic resource sizing based on duration buckets ===
AVG_DURS = fetch_avg_durations(DAG_ID)

dur_vals = np.array(list(AVG_DURS.values())) if AVG_DURS else np.array([0])
p50, p80 = np.percentile(dur_vals, [50, 80])
# Changing request can lead to a problem
# https://mint-ai.atlassian.net/browse/PDT-83262
MEMORY_BUCKETS = [
    (lambda d: d <= p50, {"requests": {"memory": "2Gi"}, "limits": {"memory": "10Gi"}}),
    (lambda d: d <= p80, {"requests": {"memory": "3Gi"},   "limits": {"memory": "10Gi"}}),
    (lambda d: True,     {"requests": {"memory": "6Gi"},   "limits": {"memory": "10Gi"}}),
]

def read_postgres_table_chunked(table_name: str, pg_hook: PostgresHook):
    """
    Read large PostgreSQL tables in chunks using pandas.read_sql.
    Returns an iterator over dataframes.
    """
    engine = pg_hook.get_sqlalchemy_engine()
    conn = engine.connect().execution_options(stream_results=True)
    sql = f"SELECT * FROM {table_name}"
    return pd.read_sql(sql, conn, chunksize=CUSTOM_CHUNK_SIZES.get(table_name, CHUNK_SIZE), dtype_backend="pyarrow")


def clean_dict(data):
    try:
        data = data.replace('\'', '"').replace('None', 'null')
        data = json.loads(data)
    except Exception:
        return
    if isinstance(data, dict):
        for key, value in data.items():
            if value is not None:
                data[key] = str(value)
        return None if data == {} else data


def enrich_data(df:PandasDataFrame, table_info: TableInfo, **kwargs):
    """Enriches pandas DF and adds metadata columns for Snowflake"""
    if table_info.clean_jsons:
        for col in table_info.clean_jsons:
            df[col] = df[col].apply(clean_dict)
    if table_info.force_str:
        for col in table_info.force_str:
            df[col] = df[col].apply(str)

    df["SYS_UPDATE_TIMESTAMP"] = pd.Timestamp.utcnow().timestamp()
    df["RUN_ID"] = kwargs['dag_run'].run_id
    return df


def write_to_snowflake(df: DataFrame, sf_conn, table_name: str, overwrite: bool = True):
    """Write chunk of data to Snowflake using write_pandas"""
    success, nchunks, nrows, _ = write_pandas(
        sf_conn,
        df,
        table_name.upper(),
        overwrite=overwrite,
        auto_create_table=True,
        table_type='transient',
        use_logical_type = True
    )

    if success:
        print(f"Successfully loaded {nrows} rows in {nchunks} chunks.")
    else:
        raise SnowflakeFailed("Data load to Snowflake failed!")


@log_table_entry(JOB_START_ENTRY)
def main(database_info: DatabaseInfo, table_info: TableInfo, **kwargs):
    """Read data from Postgres in chunks and dump it into Snowflake"""
    conn = SNOWFLAKE_HOOK.get_conn()
    pg_hook = PostgresHook(postgres_conn_id=database_info.postgres_conn_id)
    target_schema_name = database_info.snowflake_database_name or database_info.postgres_database_name

    try:
        with conn.cursor() as cur:
            cur.execute(f"USE DATABASE {env_resolver.staging_db_name}")
            cur.execute(f'CREATE SCHEMA IF NOT EXISTS {target_schema_name}')
            cur.execute(f"USE SCHEMA {target_schema_name}")

        chunk_iter = read_postgres_table_chunked(
            table_name=kwargs['table_name'],
            pg_hook=pg_hook
        )
        track_staging_load(DATA_UPLOAD_START_ENTRY, START_METADATA_STATUS, **kwargs)
        is_first_chunk = True
        for chunk in chunk_iter:
            enriched_chunk = enrich_data(chunk, table_info, **kwargs)
            print(f"Dataframe types: {chunk.dtypes}")
            write_to_snowflake(enriched_chunk, conn, table_info.table_name, is_first_chunk)
            is_first_chunk = False
        track_staging_load(DATA_UPLOAD_START_ENTRY, FINISH_METADATA_STATUS, **kwargs)
    except Exception:
        logging.error("Error while processing table %s", table_info.table_name)
        logging.error(traceback.format_exc())
        raise


def resources_for(task_key: str):
    if not AVG_DURS:
        return MEMORY_BUCKETS[-1][1]
    dur = AVG_DURS.get(task_key, 0)
    for cond, cfg in MEMORY_BUCKETS:
        if cond(dur):
            return cfg
    return MEMORY_BUCKETS[0][1]

# === Build per-task pod_override dynamically ===
def get_k8s_dump_node(task_key: str) -> dict:
    mem = resources_for(task_key)
    return {
        'pod_override': k8s.V1Pod(
            spec=k8s.V1PodSpec(
                node_selector={"dag": "true"},
                tolerations=[
                    k8s.V1Toleration(
                        effect="NoSchedule",
                        key="dag",
                        operator="Equal",
                        value="true"
                    )
                ],
                containers=[
                    k8s.V1Container(
                        name='base',
                        resources=k8s.V1ResourceRequirements(
                            requests={
                                'cpu': '375m',
                                'memory': mem['requests']['memory']
                            },
                            limits={
                                'cpu': '1',
                                'memory': mem['limits']['memory']
                            }
                        )
                    )
                ]
            )
        )
    }

with DAG(
    DAG_ID,
    description='Flow that dumps all DB data into Snowflake',
    # Need to set schedule interval
    schedule_interval=None,
    start_date=datetime.datetime(2022, 1, 1),
    catchup=False,
    on_failure_callback=failure_callback
) as dag:
    dag.data_source = TRANSACTIONAL_SOURCE

    for database_name in DATABASES_INFO:
        if database_name == 'REPORTS':
            dag.data_source = PERFORMANCE_SOURCE
        # Create a TaskGroup for each database
        with TaskGroup(group_id=database_name) as tg:
            for table in DATABASES_INFO[database_name].tables:
                task_id = f'dump_{table.table_name}'
                task_key = f"{database_name}.{task_id}"
                PythonOperator(
                    task_id=task_id,
                    on_failure_callback=failure_callback,
                    python_callable=main,
                    provide_context=True,
                    executor_config=get_k8s_dump_node(task_key),
                    op_kwargs={
                        'database_info': DATABASES_INFO[database_name],
                        'table_info': table,
                        'database_name': database_name,
                        'table_name': table.table_name,
                        'snowflake_hook': SNOWFLAKE_HOOK
                    }
                )
