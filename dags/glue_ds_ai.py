import datetime
from airflow import DAG
from myn_airflow_lib.constants import GLUE_DS_FLOW_NAME
from myn_airflow_lib.custom_operators import CustomGlueJobOperatorNoParallel, CustomCrawlersTriggerOperatorNoParallel
from myn_airflow_lib.resolvers import EnvironmentResolver

FLOW_NAME = GLUE_DS_FLOW_NAME
env_resolver = EnvironmentResolver()
config = dict(retries=dict(max_attempts=10))
glue_client_params = dict(region_name='eu-west-1', config=config)

with DAG(
        FLOW_NAME,
        schedule=None,
        start_date=datetime.datetime(2021, 1, 1),
        catchup=False,
        max_active_tasks=100,
) as dag:
    ds_jobs = CustomGlueJobOperatorNoParallel(
        task_id='ds_jobs',
        env=env_resolver.environment,
        layer='ds',
        glue_client_params=glue_client_params,
        job_list=None  # None means all the jobs in the layer
    )

    ai_jobs = CustomGlueJobOperatorNoParallel(
        task_id='ai_jobs',
        env=env_resolver.environment,
        layer='ai',
        glue_client_params=glue_client_params,
        job_list=None  # None means all the jobs in the layer
    )

    crawlers = CustomCrawlersTriggerOperatorNoParallel(
        task_id='crawlers_for_athena_tables',
        env=env_resolver.environment,
        glue_client_params=glue_client_params,
        crawler_ids_list=None  # None means all crawlers in the layer
    )

    ds_jobs >> ai_jobs >> crawlers
