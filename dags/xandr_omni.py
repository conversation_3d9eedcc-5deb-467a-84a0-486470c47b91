import logging
import requests
import time
import datetime
import json
import pandas as pd

from pandas import DataFrame
from copy import deepcopy
from io import String<PERSON>
from typing import Union
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import <PERSON>g<PERSON>Hook

from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.commons import (set_config, set_execution_dates, failure_callback, get_date_to_execute_on,
                                     enable_hist_log_check_based_on_conf)
from myn_airflow_lib.constants import EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE, OMNI_ETL_ENDPOINT
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.datadog import init_datadog, increment_metric
from myn_airflow_lib.exceptions import OmniFlowFailed

from omni.utils import QueryData
from omni.processors import ProfileProcessor, QueryDataProcessor, ReportLoadProcessor, ReportOrderProcessor


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True

state = {
    'dates': {}
}
dates = state['dates']

env_resolver = EnvironmentResolver()
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)

FLOW_NAME = 'Xandr'
PLATFORM_NAME = 'xandr_integrated'
API_FIELDS = ["day", "campaign_id", "insertion_order_id", "line_item_id", "creative_id", "creative_name", "pixel_name", "imps",
              "clicks", "booked_revenue_adv_curr", "video_starts", "video_25_pcts", "video_50_pcts", "video_75_pcts",
              "video_completions", "total_convs", "advertiser_currency"]

env_resolver = EnvironmentResolver()
env_type = env_resolver.environment

REPORTS_TABLE_NAME = 'reports_xandr_performance_v3'


# Init -> XandrProfileProcessor, XandrQueryDataProcessor, XandrReportOrderProcessor, XandrReportLoadProcessor

class XandrProfileProcessor(ProfileProcessor):
    def __init__(self, platform_name, **kwargs):
        super().__init__(platform_name, **kwargs)

    def filter_profiles(self) -> list:
        """Flatten and filter the profiles by platform_name"""
        new_endpoint = OMNI_ETL_ENDPOINT + f'/?platform={self.platform_name}'
        response = requests.get(new_endpoint).json()
        flatten = response['data']['items']
        filtered = list(filter(lambda x: x['platform'] == self.platform_name and x['campaigns'], flatten))
        return filtered


class XandrQueryDataProcessor(QueryDataProcessor):
    def __init__(self, profiles, dates, flow_name, api_fields, template_file, sanity_period, chunk_historical_query,
                 chunk_periods, historical_period, campaigns_plural):
        super().__init__(profiles, dates, flow_name, api_fields, template_file, sanity_period, chunk_historical_query,
                         chunk_periods, historical_period, campaigns_plural)

    def get_standard_queries(self, profile: dict, campaign_data: Union[dict, list], **kwargs) -> list:
        """Redefine this function to use tomorrow date  -> to retrieve today data:)"""
        standard_query_data = []
        tomorrow = self.dates['TOMORROW']
        tomorrow_format = tomorrow.strftime('%Y-%m-%d')
        seven_days_ago_format = get_date_to_execute_on(self.dates, '%Y-%m-%d', self.sanity_period, **kwargs)
        date_from = seven_days_ago_format
        date_to = tomorrow_format
        account_id = profile['ad_account_id']
        standard_req_data = self._create_query_body(campaign_data, date_from, date_to, account_id)
        standard_query = QueryData(standard_req_data, 'standard', profile, campaign_data, date_from, date_to)
        standard_query_data.append(standard_query)
        return standard_query_data

    def _create_query_body(self, campaign: dict, date_from: str, date_to: str, account_id: str = None) -> dict:
        """Creates filters to use in request body"""
        filters = self._read_query_template()
        filters["report"]["columns"] = self.api_fields
        filters["report"]["filters"] = [{"insertion_order_id": [int(camp) for camp in campaign]}]
        filters["report"]["start_date"] = date_from
        filters["report"]["end_date"] = date_to
        return filters

    @enable_hist_log_check_based_on_conf(state)
    def _does_hist_log_exist(self, hist_request: QueryData) -> bool:
        """Re-defined function filters historical request if it has been already processed"""
        campaign_detail = hist_request.campaign_detail
        query = f"""
                    select id from {HISTORICAL_LOG_TABLE}
                    where campaign_type = '{self.flow_name}'
                          and campaign_detail = '{campaign_detail}'
                    limit 1;
                """
        logging.info(f"Filtering historical data with query - {query}")
        df = pg_hook.get_pandas_df(query)
        return not df.empty


class XandrReportOrderProcessor(ReportOrderProcessor):
    def __init__(self, query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type, **kwargs):
        self.current_run_number = kwargs['dag_run'].conf.get('current_run_number')
        super().__init__(query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type)

    def _create_report_id(self, query_data) -> str:
        """Creates report id by calling API"""
        query = QueryData(**query_data)
        bad_statuses = [400, 401, 403, 404, 405, 415, 429, 500, 501, 503]
        good_statuses = [200, 201, 202]
        request_data = {'headers': {'Content-Type': 'application/json',
                                    'Accept': 'application/json',
                                    'Authorization': f'Bearer {query.access_token}'},
                        'url': 'https://api.appnexus.com/report',
                        'method': 'POST',
                        'json': query.query_body}
        exc = None
        for tries_made in range(self.retries):
            try:
                response = requests.request(**request_data)
                logging.info(f'RESPONSE STATUS CODE - {response.status_code}')
            except Exception as e:
                logging.error(f"Network exception during request execution. {e}. {request_data}")
                exc = e
                time.sleep(self.error_sleep_time)
                continue
            if response.status_code in bad_statuses:
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:error",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                logging.warning(f'send request with data = {json.dumps(request_data)} \n'
                                f'received {response.status_code}')
                time.sleep(self.error_sleep_time)
            elif response.status_code in good_statuses:
                time.sleep(self.success_sleep_time)
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:success",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                return response.json()['response']['report_id']
            else:
                data_to_log = deepcopy(request_data)
                data_to_log.pop('headers')
                msg = f'FLOW - {self.flow_name}. Cant process the request (tries_made={tries_made + 1}) {json.dumps(data_to_log)}. Exception `{exc}`'
                raise OmniFlowFailed(msg)

    def _check_report_status(self, query_data: dict, report_id: str) -> str:
        """Checks report status by calling API"""
        query = QueryData(**query_data)
        bad_statuses = [400, 401, 403, 404, 405, 415, 429, 500, 501, 503]
        good_statuses = [200, 201, 202]
        request_data = {'headers': {'Content-Type': 'application/json',
                                    'Accept': 'application/json',
                                    'Authorization': f'Bearer {query.access_token}'},
                        'url': f'https://api.appnexus.com/report?id={report_id}',
                        'method': 'GET'}
        exc = None
        for tries_made in range(self.retries):
            try:
                response = requests.request(**request_data)
                logging.info(f'RESPONSE STATUS CODE - {response.status_code}')
            except Exception as e:
                logging.error(f"Network exception during request execution. {e}. {request_data}")
                exc = e
                time.sleep(self.error_sleep_time)
                continue
            if response.status_code in bad_statuses:
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:error",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                logging.warning(f'send request with data = {json.dumps(request_data)} \n'
                                f'received {response.status_code}')
                time.sleep(self.error_sleep_time)
            elif response.status_code in good_statuses:
                time.sleep(self.success_sleep_time)
                increment_metric('airflow_request_counter.increment', self.env_type, self.flow_name,
                                 additional_tags=[f"request_type:{query.query_type}", "request_status:success",
                                                  f"client:{query.company_id}", f'run_number:{self.current_run_number}'])
                resp_json = response.json()
                execution_status = resp_json.get('response', {}).get('execution_status')
                if execution_status not in ['ready']:
                    logging.info(f'Status - {execution_status}. Getting link is still in progress for report id = {report_id}')
                    time.sleep(self.error_sleep_time)
                    continue
                elif execution_status == 'ready':
                    report_link = resp_json['response']['report']['url']
                    logging.info(f"Report link received successfully - {report_link}")
                    return report_link
            else:
                data_to_log = deepcopy(request_data)
                data_to_log.pop('headers')
                msg = f'FLOW - {self.flow_name}. Cant process the request (tries_made={tries_made + 1}) {json.dumps(data_to_log)}. Exception `{exc}`'
                raise OmniFlowFailed(msg)

    def order_report(self, query_data: dict) -> str:
        """Order single report by processing single query and returns report link."""
        logging.info('Creating report ID')
        report_id = self._create_report_id(query_data)
        if report_id is None:
            report_id_error_msg = f"Received NONE report_id for data - {query_data}"
            logging.error(report_id_error_msg)
            raise OmniFlowFailed(report_id_error_msg)
        logging.info('Checking report status')
        report_link = self._check_report_status(query_data, report_id)
        if report_link is None:
            report_link_error_msg = f"Received NONE report_link for data - {query_data}"
            logging.error(report_link_error_msg)
            raise OmniFlowFailed(report_link_error_msg)
        return report_link


class XandrReportLoadProcessor(ReportLoadProcessor):
    def __init__(self, query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type,
                 report_table_name, postgres_hook, **kwargs):
        self.current_run_number = kwargs['dag_run'].conf.get('current_run_number')
        super().__init__(query_data, flow_name, token_header, retries, error_sleep_time, success_sleep_time, env_type,
                         report_table_name, postgres_hook)

    def _request_report(self, query: QueryData) -> list:
        """Re-defined function requests single report"""
        report_link = query.report_link
        response = self._send_request(query=query,
                                      url=f'https://api.appnexus.com/{report_link}',
                                      method='GET')
        report_data = response.text
        if bool(report_data):
            return report_data
        else:
            logging.info(
                f'report is empty with type {query.query_type} for account_id {query.account_id}')
            return []

    def _parse_report_data(self, report_data: str, query: QueryData = None) -> DataFrame:
        """Re-defined function handles data before pushing to DB"""
        # parse data if it exists
        if report_data:
            report = pd.read_csv(StringIO(report_data), sep=",")

            col_mapping = {"day": "date",
                           "campaign_id": "campaign_id",
                           "advertiser_currency": "currency",
                           "insertion_order_id": "insertion_order_id",
                           "line_item_id": "line_item_id",
                           "creative_id": "creative_id",
                           "creative_name": "creative_name",
                           "pixel_name": "conversion_name",
                           "imps": 'impressions',
                           "clicks": "clicks",
                           "booked_revenue_adv_curr": "spend",
                           "video_starts": "video_start",
                           "video_25_pcts": "completed_views_first",
                           "video_50_pcts": "completed_views_mid",
                           "video_75_pcts": "completed_views_third",
                           "video_completions": "completed_views_full",
                           "total_convs": "platform_conversions"}
            report_renamed = report.rename(columns=col_mapping)

            final_report = report_renamed[['date', 'campaign_id', 'currency', 'insertion_order_id',
                                           'line_item_id', 'creative_id', 'creative_name', 'conversion_name', 'impressions',
                                           'clicks', 'spend', 'video_start', 'completed_views_first',
                                           'completed_views_mid', 'completed_views_third', 'completed_views_full',
                                           'platform_conversions']]

            final_report = final_report.drop_duplicates()
            return final_report
        # returns empty df if data is absent
        else:
            return pd.DataFrame([])

    def _create_report_db_query(self, columns: str, values_str: str) -> str:
        """Re-defined function creates upsert sql statement"""
        query = f"""
              INSERT INTO {self.report_table_name} ({columns})
              VALUES {values_str} ON CONFLICT (date,campaign_id,insertion_order_id,line_item_id,creative_id,conversion_name)
              DO UPDATE SET completed_views_full=Excluded.completed_views_full, 
              completed_views_first=Excluded.completed_views_first,
              completed_views_mid=Excluded.completed_views_mid, 
              completed_views_third=Excluded.completed_views_third,
              spend=Excluded.spend, 
              creative_name = Excluded.creative_name,
              video_start=Excluded.video_start,
              clicks=Excluded.clicks,
              platform_conversions=Excluded.platform_conversions,
              impressions=Excluded.impressions,
              currency=Excluded.currency; """
        return query


# Wrapping initialised Xandr classes into functions -> get_profiles, get_queries, order_reports, load_reports


def get_profiles(**kwargs):
    """Get profiles list"""
    logging.info('Starting getting profiles ...')
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    profiles = XandrProfileProcessor(platform_name=PLATFORM_NAME, **kwargs).get_profiles(config_mock)
    filtered = list(filter(lambda x: x['platform'] == PLATFORM_NAME and x['campaigns'], profiles))
    return filtered


@set_config(state)
@set_execution_dates(dates)
def get_queries(profiles: list, **kwargs):  # **kwargs must be present for @set_execution_dates
    """Create queries list based on profiles"""
    logging.info('Starting preparing queries ...')
    return XandrQueryDataProcessor(profiles=profiles,
                                   dates=dates,
                                   flow_name=FLOW_NAME,
                                   api_fields=API_FIELDS,
                                   template_file='xandr_template',
                                   sanity_period='SANITY_PULL',  # 45 days default
                                   chunk_historical_query=True,
                                   chunk_periods=90,  # 3 months split
                                   historical_period='TWO_YEARS_AGO',
                                   campaigns_plural=True).prepare_queries(**kwargs)


def order_reports(query_data: list, **kwargs):
    """Order reports based on queries list"""
    logging.info('Starting ordering reports...')
    return XandrReportOrderProcessor(query_data=query_data,
                                     flow_name=FLOW_NAME,
                                     token_header='Authorization',
                                     retries=7,
                                     error_sleep_time=45,
                                     success_sleep_time=8,
                                     env_type=env_type, **kwargs).order_reports()


def load_reports(query_data: list, **kwargs):
    """Handle and load reports"""
    logging.info('Starting loading reports...')
    return XandrReportLoadProcessor(query_data=query_data,
                                    flow_name=FLOW_NAME,
                                    token_header='Authorization',
                                    retries=3,
                                    error_sleep_time=45,
                                    success_sleep_time=8,
                                    env_type=env_type,
                                    report_table_name=REPORTS_TABLE_NAME,
                                    postgres_hook=pg_hook, **kwargs).process_reports()

# Using DAG context with wrapped functions to run Airflow step by step -> t1, t2, t3, t4 cleanup_xcoms


with DAG(FLOW_NAME,
         description='Xandr OMNI DAG',
         start_date=datetime.datetime(2022, 1, 1),
         schedule_interval=None,
         tags=['ingestion_flow'],
         params=DagConfigSetter(FLOW_NAME).get_params(),
         catchup=False) as dag:

    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )
    t2 = PythonOperator(
        task_id='get_queries',
        on_failure_callback=failure_callback,
        python_callable=get_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="order_reports",
        on_failure_callback=failure_callback,
        python_callable=order_reports,
        op_kwargs={"query_data": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t4 = PythonOperator(
        task_id="load_reports",
        on_failure_callback=failure_callback,
        python_callable=load_reports,
        op_kwargs={"query_data": XComArg(t3)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> t4 >> cleanup_xcoms
