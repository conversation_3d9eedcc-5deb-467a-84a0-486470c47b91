import datetime
import logging
import time
import base64
import pandas as pd
import json
from airflow_common.dag_config_setter import DagConfigSetter
import numpy as np

from copy import deepcopy
from pandas import DataFrame
from io import StringIO
from requests import post, get
from typing import NoReturn, Union, Optional
from dataclasses import dataclass
from requests import Response

from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook

from myn_airflow_lib.commons import (EtlEndpointProcessorWithParams, failure_callback, filter_out_standard_queries,
                                     set_execution_dates, set_config, _get_db_value, iter_batch,
                                     enable_hist_log_check_based_on_conf, load_static_template, get_date_to_execute_on)
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.constants import (ZEMANTA_API_ENDPOINT, ZEMANTA_ETL_ENDPOINT, ZEMANTA_WAIT_SLEEP,
                                       ZEMANTA_RETRY_API_ATTEMPTS, EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE)
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog


FLOW_NAME = 'ZEMANTA'
CAMPAIGN_TYPE = 'zemanta'
FILTERS = load_static_template('static/zemanta_filters.json')
REPORTS_TABLE_NAME = 'reports_zemanta_performance_v2'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
pg_hook = PostgresHook(postgres_conn_id='uri_reports')
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)

state = {
    'dates': {}
}
dates = state['dates']


@dataclass
class QueryData:
    """Class creates special data structure
    containing all necessary data for other handlers"""
    data: dict
    type: str
    profile: dict
    campaign: dict

    @property
    def company_id(self):
        return self.profile['company_id']

    @property
    def campaign_detail(self):
        return json.dumps({"account_id": self.profile['zemanta_account_id'],
                           "campaign_id": self.campaign['external_id']})

    def to_dict(self):
        # removes campaigns data from profile otherwise XCOM grows 1000x in size
        if 'campaigns' in self.profile:
            del self.profile['campaigns']
        return {'data': self.data,
                'type': self.type,
                'profile': self.profile,
                'campaign': self.campaign}


class QueryDataProcessor:
    """Class creates full list of historical
     and standard queries"""
    def __init__(self, profiles, dates):
        self.historical_queries = []
        self.standard_queries = []
        self.profiles = profiles
        self.dates = dates

    @staticmethod
    def _create_query_body(campaign: dict, date_from: str, date_to: str) -> dict:
        """Creates filters to use in request body"""
        filters = deepcopy(FILTERS)
        filters["filters"][0]["from"] = date_from
        filters["filters"][0]["to"] = date_to
        filters["filters"][1]["value"] = str(campaign['external_id'])
        return filters

    @staticmethod
    @enable_hist_log_check_based_on_conf(state)
    def _does_hist_log_exist(hist_request: QueryData) -> bool:
        """Filters historical request if it has been already processed"""
        campaign_detail = hist_request.campaign_detail
        query = f"""
            select id from {HISTORICAL_LOG_TABLE}
            where 
                campaign_type = '{CAMPAIGN_TYPE}'
                and campaign_detail = '{campaign_detail}'
            limit 1;
        """
        logging.info(f"Filtering historical data with query - {query}")
        df = pg_hook.get_pandas_df(query)
        return not df.empty

    def _get_historical_query(self, profile: dict, campaign: dict, historical_range_mock: Optional[int]) -> Union[QueryData, None]:
        """Collects historical query"""
        campaign_id = campaign['external_id']
        settings_date_from = self.dates['TWO_YEARS_AGO']
        settings_date_to = self.dates['TODAY']
        if historical_range_mock:
            logging.info('historical_range_mock is set. Using it')
            settings_date_from = self.dates['TODAY'] - datetime.timedelta(historical_range_mock)
        if campaign_id:
            date_from = settings_date_from.strftime('%Y-%m-%d')
            date_to = settings_date_to.strftime('%Y-%m-%d')
            hist_req_data = self._create_query_body(campaign, date_from, date_to)
            hist_req = QueryData(hist_req_data, 'historical', profile, campaign)
            if not self._does_hist_log_exist(hist_req):
                return hist_req
            else:
                return

    def _get_standard_query(self, profile: dict, campaign: dict, **kwargs) -> QueryData:
        """Collects standard query"""
        yesterday = self.dates['YESTERDAY']
        yesterday_format = yesterday.strftime('%Y-%m-%d')
        seven_days_ago_format = get_date_to_execute_on(dates, '%Y-%m-%d', **kwargs)
        date_from = seven_days_ago_format
        date_to = yesterday_format
        standard_req_data = self._create_query_body(campaign, date_from, date_to)
        standard_query = QueryData(standard_req_data, 'standard', profile, campaign)
        return standard_query

    def prepare_queries(self, **kwargs) -> list:
        """Collects historical and standard queries"""
        historical_range_mock = kwargs['dag_run'].conf.get('historical_range_in_days')
        for profile in self.profiles:
            campaigns = profile['campaigns']
            for campaign in campaigns:
                campaign_hist_query = self._get_historical_query(profile, campaign, historical_range_mock)
                campaign_stand_query = self._get_standard_query(profile, campaign, **kwargs)
                self.standard_queries.append(campaign_stand_query)
                if campaign_hist_query is not None:
                    self.historical_queries.append(campaign_hist_query)
        all_queries = self.historical_queries + self.standard_queries
        logging.info(f"Total queries = {len(all_queries)}")
        return filter_out_standard_queries([query.to_dict() for query in all_queries], **kwargs)


class BadStatusProcessor:
    """Class provides context of handling bad statuses codes"""
    @staticmethod
    def retry_bad_status(url: str, retry: dict, response: Response) -> None:
        """Retries request in case of bad statuses codes"""
        retry['retry_attempts'] -= 1
        logging.warning(
            f"Retries on url {url} with  status code {response.status_code} = {retry['retry_attempts']} retries left")

    @staticmethod
    def log_for_bad_status(response: Response) -> NoReturn:
        """Logs and raises API fail in case of no retries left"""
        logging.error(
            f"API doesn't respond. The request was stopped by {response.status_code} with all attempts of retrying")


@dataclass
class AuthProcessor:
    """
    Class creates auth token that lives 10 hours
    """
    query_profile: QueryData

    def __post_init__(self):
        self.auth_url = "https://oneapi.zemanta.com/o/token/"
        self.body = {'grant_type': 'client_credentials'}

    def get_token(self) -> str:
        """Provides Bearer token"""
        auth = "Basic " + base64.b64encode(bytes(self.query_profile.profile.get("client_id") + ":" +
                                                 self.query_profile.profile.get("client_secret"),
                                                 "utf-8")).decode('ascii')

        token_response = post(url=self.auth_url,
                              data=self.body,
                              headers={"Content-Type": "application/x-www-form-urlencoded",
                                       "Authorization": auth})
        token = json.loads(token_response.content)
        bearer_token = "Bearer " + token["access_token"]
        logging.info(f"Token has been created - {bearer_token}")
        return bearer_token


class ProfileProcessor(BadStatusProcessor):
    """Gets all profiles from ZEMANTA_ETL_ENDPOINT"""
    def __init__(self, **kwargs):
        self.url = ZEMANTA_ETL_ENDPOINT
        self.kwargs = kwargs

    @staticmethod
    def _filter_profiles(profile: dict) -> dict:
        """Quick filter of profiles"""
        return profile['zemanta_account_id'] is not None and profile['campaigns']

    def get_profiles(self, config_mock: list) -> list:
        """Collects all profiles from Zemanta ETL endpoint"""
        logging.info("Getting data from internal Zemanta API")
        if config_mock:
            return config_mock
        else:
            raw_profiles = EtlEndpointProcessorWithParams(self.url, **self.kwargs).apply_all()
            profiles = list(filter(self._filter_profiles, raw_profiles))
            return profiles


class ApiProcessor(BadStatusProcessor):
    """Class calls API endpoints"""
    def __init__(self, query_profile: QueryData = None, **kwargs):
        self.query_profile = query_profile
        self.headers = {'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': AuthProcessor(self.query_profile).get_token()} if self.query_profile else None
        self.current_run_number = kwargs['dag_run'].conf.get('current_run_number')

    def create_endpoint_job(self) -> Union[str, NoReturn]:
        """Job endpoint creation"""
        retry = dict(retry_attempts=ZEMANTA_RETRY_API_ATTEMPTS)
        url = ZEMANTA_API_ENDPOINT
        company_id = self.query_profile.profile.get('company_id')
        data = json.dumps(self.query_profile.data)

        while retry['retry_attempts']:
            response = post(url=url,
                            data=data,
                            headers=self.headers)
            if response.status_code in [200, 201]:
                increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                                 additional_tags=[f"request_type:{self.query_profile.type}", "request_status:success",
                                                  f"client:{company_id}", f'run_number:{self.current_run_number}'])
                return response.json().get('data').get('id')
            elif response.status_code in [400, 403, 429, 500, 502, 503, 504]:
                increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                                 additional_tags=[f"request_type:{self.query_profile.type}", "request_status:error",
                                                  f"client:{company_id}", f'run_number:{self.current_run_number}'])
                time.sleep(ZEMANTA_WAIT_SLEEP)
                self.retry_bad_status(url, retry, response)
                if retry['retry_attempts'] > 0:
                    continue
                else:
                    self.log_for_bad_status(response)
            else:
                self.log_for_bad_status(response)
                break

    def get_report_link(self, report_id: str) -> Union[str, NoReturn]:
        """Gets link"""
        company_id = self.query_profile.profile.get('company_id')
        retry = dict(retry_attempts=ZEMANTA_RETRY_API_ATTEMPTS)
        url = f'{ZEMANTA_API_ENDPOINT}{report_id}'
        while retry['retry_attempts']:
            response = get(url=url,
                           headers=self.headers)
            if response.status_code in [200, 201]:
                increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                                 additional_tags=[f"request_type:{self.query_profile.type}", "request_status:success",
                                                  f"client:{company_id}", f'run_number:{self.current_run_number}'])
                resp_data = response.json().get('data')
                if resp_data.get('status') == 'DONE':
                    result = resp_data.get('result')
                    logging.info(f'Link {result} received for report id = {report_id}')
                    return result
                elif resp_data.get('status') == 'IN_PROGRESS':
                    logging.info(f'Getting link is still in progress for report id = {report_id}')
                    time.sleep(ZEMANTA_WAIT_SLEEP)
                    self.retry_bad_status(url, retry, response)
                    continue
            elif response.status_code in [400, 403, 429, 500, 502, 503, 504]:
                increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                                 additional_tags=[f"request_type:{self.query_profile.type}", "request_status:error",
                                                  f"client:{company_id}", f'run_number:{self.current_run_number}'])
                time.sleep(ZEMANTA_WAIT_SLEEP)
                self.retry_bad_status(url, retry, response)
                if retry['retry_attempts'] > 0:
                    continue
                else:
                    self.log_for_bad_status(response)
            else:
                self.log_for_bad_status(response)
                break

    def get_report_data(self, url: str) -> Union[str, NoReturn]:
        """Gets content from link"""
        retry = dict(retry_attempts=ZEMANTA_RETRY_API_ATTEMPTS)
        while retry['retry_attempts']:
            response = get(url=url)
            if response.status_code in [200, 201]:
                return response.content
            elif response.status_code in [400, 403, 429, 500, 502, 503, 504]:
                time.sleep(ZEMANTA_WAIT_SLEEP)
                self.retry_bad_status(url, retry, response)
                if retry['retry_attempts'] > 0:
                    continue
                else:
                    self.log_for_bad_status(response)
            else:
                self.log_for_bad_status(response)
                break

    def process_endpoint(self) -> str:
        """Starts job and get link"""
        report_id = self.create_endpoint_job()
        report_link = self.get_report_link(report_id)
        return report_link


class DataProcessor:
    """Class handles the report dataframe"""
    def __init__(self, data):
        self.data = data

    def process_report(self) -> DataFrame:
        """Cleanses and forms report"""
        report = pd.read_csv(StringIO(self.data.decode('utf-8')))
        col_mapping = {'Day': 'date', 'Account': 'account_name', 'Account Id': 'account_id',
                       'Campaign': 'campaign_name', 'Campaign Id': 'campaign_id', 'Ad Group': 'ad_group_name',
                       'Ad Group Id': 'ad_group_id', 'Content Ad': 'content_ad', 'Content Ad Id': 'content_ad_id',
                       'Total Spend': 'total_spend', 'Impressions': 'impressions', 'Clicks': 'clicks',
                       'Video First Quartile': 'video_first_quartile', 'Video Midpoint': 'video_midpoint',
                       'Video Third Quartile': 'video_third_quartile', 'Video Complete': 'video_complete',
                       'Video Start': 'video_start', 'Currency': 'currency'}

        report = report.rename(columns=col_mapping)
        report = report.replace({np.nan: None})
        final_report = report[['date', 'account_name', 'account_id', 'campaign_name', 'campaign_id', 'ad_group_name',
                               'ad_group_id', 'content_ad', 'content_ad_id', 'total_spend', 'impressions', 'clicks',
                               'video_first_quartile', 'video_midpoint', 'video_third_quartile', 'video_complete',
                               'video_start', 'currency']]
        return final_report


class ReportPrepareProcessor:
    """Class calls API to get report link"""
    def __init__(self, queries_data: list, **kwargs):
        self.queries_data = queries_data
        self.kwargs = kwargs

    def process_reports(self) -> list:
        """Calls API to process endpoints"""
        result = []
        errors = {"historical": 0, "standard": 0}
        logging.info(f"Processing len(queries_data)={len(self.queries_data)};")
        for query_dict in self.queries_data:
            query = QueryData(**query_dict)
            api_processor = ApiProcessor(query, **self.kwargs)
            report_link = api_processor.process_endpoint()
            if report_link:
                result.append([report_link, query_dict, query.type])
            else:
                errors.setdefault(query_dict['type'], 0)
                errors[query_dict['type']] += 1
                msg = f'API failed for {FLOW_NAME} when process {query_dict}'
                logging.error(msg)

        for error_type, error_count in errors.items():
            all_requests_count = len(list(filter(lambda x: x["type"] == error_type, self.queries_data)))
            logging.info(f'{error_type} errors {error_count}/{all_requests_count}')
        return result


class ReportLoadProcessor:
    """Class loads reports to Zemanta DB"""
    def __init__(self, reports_meta, **kwargs):
        self.reports_meta = reports_meta
        self.current_run_number = kwargs['dag_run'].conf.get('current_run_number')
        self.kwargs = kwargs

    @staticmethod
    def _put_reports_to_db(report: DataFrame) -> None:
        """Pushes data to DB reports_zemanta_performance_v2"""
        columns = ",".join(['"{0}"'.format(col) for col in report.columns])
        rows = list(report.itertuples(index=False, name=None))
        fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
        batch_size = 5000
        for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
            logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
            values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
            query = f"""
                  INSERT INTO {REPORTS_TABLE_NAME} ({columns})
                  VALUES {values_str} ON CONFLICT (date,account_id,campaign_id,ad_group_id,content_ad_id) 
                  DO UPDATE SET account_name=Excluded.account_name, campaign_name=Excluded.campaign_name,
                  ad_group_name=Excluded.ad_group_name, content_ad=Excluded.content_ad,
                  total_spend=Excluded.total_spend, impressions=Excluded.impressions,
                  clicks=Excluded.clicks, video_first_quartile=Excluded.video_first_quartile,
                  video_midpoint=Excluded.video_midpoint,video_third_quartile=Excluded.video_third_quartile,
                  video_complete=Excluded.video_complete, video_start=Excluded.video_start,
                  currency=Excluded.currency; """
            pg_hook.run(query, True)

    @staticmethod
    def _log_hist_req(query: QueryData) -> None:
        """Logs historical data to be sure that it has been already processed"""
        campaign_detail = query.campaign_detail
        company_id = query.company_id
        query = f"""
            INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
            Values (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
            """
        data = {'company_id': company_id,
                'campaign_type': CAMPAIGN_TYPE,
                'campaign_detail': campaign_detail}
        pg_hook.run(query, True, data)

    def load_reports(self) -> None:
        """Pushes reports to DB, logs historical ingestion"""
        for report_data in self.reports_meta:
            report_link, query_data, report_type = report_data[0], report_data[1], report_data[2]
            query = QueryData(**query_data)
            logging.info(f'{FLOW_NAME}. Starting downloading report for {report_link}')
            content = ApiProcessor(**self.kwargs).get_report_data(report_link)
            report = DataProcessor(content).process_report()

            if report.empty:
                message = f'{FLOW_NAME}. Report is empty for {report_link}'
                logging.warning(message)
            else:
                self._put_reports_to_db(report)
                company_id = query.profile.get('company_id')
                increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                                 [f"table:{REPORTS_TABLE_NAME}", f"client:{company_id}", f'run_number:{self.current_run_number}'])
                logging.info(f'{FLOW_NAME}. Report is pushed to DB for {report_link}')
            if report_type == 'historical':
                self._log_hist_req(query)
                logging.info(f'{FLOW_NAME}. Historical log is pushed for {report_link}')


def get_profiles(**kwargs):
    """Get profiles list"""
    logging.info('Starting getting profiles ...')
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    return ProfileProcessor(**kwargs).get_profiles(config_mock)


@set_config(state)
@set_execution_dates(dates)
def get_zemanta_queries(profiles: list, **kwargs):  # **kwargs must be present for @set_execution_dates
    """Create queries list based on profiles"""
    logging.info('Starting preparing queries ...')
    return QueryDataProcessor(profiles, dates).prepare_queries(**kwargs)


def run_reports(queries_data: list, **kwargs):
    """Run reports based on query"""
    logging.info('Starting preparing reports ...')
    report_meta = ReportPrepareProcessor(queries_data, **kwargs).process_reports()
    return report_meta


def load_reports(reports_meta: list, **kwargs):
    """Load reports based on urls"""
    logging.info('Starting loading reports ...')
    ReportLoadProcessor(reports_meta, **kwargs).load_reports()


with DAG(FLOW_NAME,
         description='Zemanta flow dag',
         params=DagConfigSetter(FLOW_NAME).get_params(),
         start_date=datetime.datetime(2022, 1, 1),
         schedule_interval=None,
         tags=['ingestion_flow'],
         catchup=False) as dag:

    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )
    t2 = PythonOperator(
        task_id='get_zemanta_queries',
        on_failure_callback=failure_callback,
        python_callable=get_zemanta_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="run_reports",
        on_failure_callback=failure_callback,
        python_callable=run_reports,
        op_kwargs={"queries_data": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t4 = PythonOperator(
        task_id="load_reports",
        on_failure_callback=failure_callback,
        python_callable=load_reports,
        op_kwargs={"reports_meta": XComArg(t3)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> t4 >> cleanup_xcoms
