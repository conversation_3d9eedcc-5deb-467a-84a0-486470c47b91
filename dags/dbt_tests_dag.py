from datetime import datetime
import os

from airflow import DAG
from cosmos import ProjectConfig, ProfileConfig, ExecutionConfig, DbtTaskGroup, RenderConfig

# Import the operator and relevant profiles from Cosmos
from cosmos.operators import DbtTestOperator
from cosmos.profiles import SnowflakePrivateKeyPemProfileMapping

# If you have a custom environment resolver
from myn_airflow_lib.resolvers import EnvironmentResolver

env_resolver = EnvironmentResolver()

with DAG(
    dag_id="dbt_snowflake_test_dag",
    schedule_interval=None,
    start_date=datetime(2025, 1, 1),
    catchup=False,
    default_args={"retries": 0}
) as dag:
    # Path to your dbt executable (virtualenv, etc.)
    dbt_executable_path = f"{os.environ['AIRFLOW_HOME']}/dbt_venv/bin/dbt"

    # Example environment usage
    env_name = env_resolver.environment

    SF_WAREHOUSE_NAME = env_resolver.snowflake_write_warehouse
    STAGE_DB_NAME = f"{env_name}_STAGE"
    TRANSFORM_DB_NAME = f"{env_name}_TRANSFORM"
    CORE_DB_NAME = f"{env_name}_CORE"
    DATAMART_DB_NAME = f"{env_name}_DATAMART"

    # Optionally set environment variables if your dbt_project.yml uses them
    os.environ["SF_WAREHOUSE_NAME"] = SF_WAREHOUSE_NAME
    os.environ["STAGE_DB_NAME"] = STAGE_DB_NAME
    os.environ["TRANSFORM_DB_NAME"] = TRANSFORM_DB_NAME
    os.environ["CORE_DB_NAME"] = CORE_DB_NAME
    os.environ["DATAMART_DB_NAME"] = DATAMART_DB_NAME

    test_all = DbtTestOperator(
        task_id="test_all",
        retries=0,
        project_dir=f"{os.environ['AIRFLOW_HOME']}/dags/dbt/snowflake_dbt",
        dbt_executable_path=dbt_executable_path,
        profile_config=ProfileConfig(
            profile_name="default",
            target_name="dev",
            profile_mapping=SnowflakePrivateKeyPemProfileMapping(
                conn_id="snowflake_dbt",
                profile_args={
                    "snowflake_warehouse": SF_WAREHOUSE_NAME,
                    "database": "",
                    "schema": "",
                }
            ),
        ),
    )
