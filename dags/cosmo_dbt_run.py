from datetime import datetime
import os

from cosmos import <PERSON>btDag, ProjectConfig, ProfileConfig, ExecutionConfig, RenderConfig
from cosmos.profiles import SnowflakePrivateKeyPemProfileMapping

from myn_airflow_lib.constants import K8S_DBT_NODE
from myn_airflow_lib.resolvers import EnvironmentResolver

DAG_ID = "dbt_snowflake_dag"

TEST_TYPE_DATA = "test_type:data"

resolver = EnvironmentResolver()
env = resolver.environment
default_args = {
    "executor_config": K8S_DBT_NODE
}

# Export your Snowflake/env variables so dbt picks them up
os.environ.update({
    'STAGE_DB_NAME':     f'{env}_STAGE',
    'TRANSFORM_DB_NAME': f'{env}_TRANSFORM',
    'CORE_DB_NAME':      f'{env}_CORE',
    'DATAMART_DB_NAME':  f'{env}_DATAMART',
})

# Base dbt/Cosmos configs
project_cfg = ProjectConfig(f"{os.environ['AIRFLOW_HOME']}/dags/dbt/snowflake_dbt")
profile_cfg = ProfileConfig(
    profile_name="default",
    target_name="dev",
    profile_mapping=SnowflakePrivateKeyPemProfileMapping(
        conn_id='snowflake_dbt',
        profile_args={
            "database": "",
            "schema": "",
            "warehouse": resolver.snowflake_write_warehouse
        }
    ),
)
exec_cfg = ExecutionConfig(
    dbt_executable_path=f"{os.environ['AIRFLOW_HOME']}/dbt_venv/bin/dbt"
)

# Define the DAG itself
dag = DbtDag(
    dag_id=DAG_ID,
    schedule_interval=None,
    start_date=datetime(2025, 1, 1),
    catchup=False,
    default_args=default_args,

    project_config=project_cfg,
    profile_config=profile_cfg,
    execution_config=exec_cfg,
    render_config=RenderConfig(
        select=["path:models"],
        exclude=["config.materialized:ephemeral", "test", TEST_TYPE_DATA],
        dbt_deps=False,

    ),
    operator_args={"install_deps": False},

)
