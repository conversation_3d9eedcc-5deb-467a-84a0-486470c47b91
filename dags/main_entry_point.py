# Airflow dag to trigger entire data flow of mint
# will trigger Airflow flows, glue datalake scripts, and imply ingestion
# For the DOC reference see
# https://mint-ai.atlassian.net/wiki/spaces/TEAMJ/pages/3918659677/Airflow+Main+DAG+documentation


from airflow.models import DagRun, TaskInstance
from airflow.utils.db import provide_session
from airflow.utils import timezone
from airflow.utils.state import State
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow import DAG
import datetime
import os
from typing import List
import logging
from airflow_common.dag_config_setter import DagConfigSetter
from airflow import DAG
from airflow.utils.state import State
from raw_s3_snowflake_copy import FLOW_NAME as RAW_DATALAKE_COPY_DAG_ID
from myn_airflow_lib.commons import failure_callback, get_main_dags, is_first_saturday
from myn_airflow_lib.constants import GLUE_IMPLY_ONLY_APPROX_TIMES, TIMEZON_INGESTION_MAP, GLUE_FLOW_NAME, IMPLY_FLOW_NAME, MAIN_DAG_FLOW_NAME, GLUE_DS_FLOW_NAME
from myn_airflow_lib.custom_operators import CustomTriggerDagRunOperator
from myn_airflow_lib.datadog import init_datadog, increment_metric
from myn_airflow_lib.resolvers import EnvironmentResolver
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.models.baseoperator import BaseOperator
from airflow.models.dagrun import DagRun
from airflow.utils.task_group import TaskGroup
import pytz
from myn_airflow_lib.constants import TRIGGER_DAG_NODES, BRANCH_PODS, CUSTOM_TRIGGER_PODS

env_resolver = EnvironmentResolver()
FLOW_NAME = MAIN_DAG_FLOW_NAME
ingestion_flow_tag = 'main_flow'  # "ingestion_flow"
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
    

def get_current_runs_ttk():
    """
    Get current runs ttk and sets it as a global variable
    """
    # timezone and multiplication of the time to kill
    ONE_HOUR_DELTA = datetime.timedelta(0.042)
    TTK = ONE_HOUR_DELTA * 1.6 # default TTK
    TTK_MAP = {
        1 : 1.6,
        2 : 1
    }
    timezon_ingestion_map = TIMEZON_INGESTION_MAP
    # get the run
    execution_date = datetime.datetime.now().strftime("%H:%M")
    execution_date = datetime.datetime.strptime(execution_date, "%H:%M")
    for schedule_time in timezon_ingestion_map.keys():
        offset = abs(int((schedule_time - execution_date).total_seconds()) / 60)
        if offset < 40:
            logging.info(f'Setting time to kill to  {ONE_HOUR_DELTA * TTK_MAP[timezon_ingestion_map[schedule_time]]}')
            TTK = ONE_HOUR_DELTA * TTK_MAP[timezon_ingestion_map[schedule_time]]
    if is_first_saturday():
        logging.info("Sanity pull detected, setting time to kill to 3 hours")
        TTK = ONE_HOUR_DELTA * 3
    
    logging.info(f"TTK was set to: {TTK}")
    return TTK


def mark_all_tasks_success(dag_run_id, dag_id, execution_date, session=None):
    """
    Set all tasks in the current DAG run to 'success'.
    """
    # Query the DAG run
    dag_run = session.query(DagRun).filter(
        DagRun.dag_id == dag_id,
        DagRun.run_id == dag_run_id,
        DagRun.execution_date == execution_date
    ).one_or_none()

    if not dag_run:
        raise ValueError(f"DAG run not found for dag_id={dag_id}, run_id={dag_run_id}, execution_date={execution_date}")
    
    # Fetch all task instances for the DAG run
    task_instances = dag_run.get_task_instances(session=session)

    for task_instance in task_instances:
        task_instance.set_state(State.SUCCESS, session=session)



@provide_session
def get_current_runtype(session=None, **kwargs):
    """
    get current run type and push it into dag context
    """
    if not kwargs['dag_run'].conf.get('historical_only'):
        logging.info('not a historical run setting current run number')
        def is_same_day_as_now(run):
            if run.start_date:
                input_date = run.start_date
            else:
                input_date = run.execution_date
            current_date = datetime.datetime.now().date()
            return input_date.date() == current_date
        dag_id = FLOW_NAME
        dag_runs = DagRun.find(dag_id=dag_id)
        # get number of finished scheduled runs today
        if  not kwargs['dag_run'].external_trigger:
            logging.info('Scheduled run detected setting run number...')
            runs = [run for run in dag_runs if run.external_trigger==False
                    and run.state in ['success', 'failed'] and is_same_day_as_now(run)
                    ]
            current_run_number = len(runs)+1
            kwargs['dag_run'].conf['current_run_number'] = current_run_number
        else:
            logging.info('Manual run detected setting run_number as NONE!!')
            kwargs['dag_run'].conf['current_run_number'] = None
    else:
        logging.info('historical run detected')

    # check the weekday and do we need to skip the execution
    execution_date = kwargs['execution_date']
    dag_run = kwargs['dag_run']
    if execution_date.weekday() in [6,5] and current_run_number > 2:  #(Python: 0=Monday, 6=Sunday)
        logging.info('The dag run will be skipped since its weekend and an extra run')
        mark_all_tasks_success(
        dag_run_id=dag_run.run_id,
        dag_id=dag_run.dag_id,
        execution_date=execution_date,
        session=session
    )



def success_callback(context):
    """
    This is just a dummy callback
    it's implemented in order to wait for completion
    """
    task_id = context['ti'].task_id
    logging.info(f'dag {task_id} finished!!')


def get_dag(dag_id, allowed_states=None, **kwargs):
    """
    triggers dag inside python context by dag_id
    """
    if not allowed_states:
        allowed_states = [State.SUCCESS, State.FAILED]

    return CustomTriggerDagRunOperator(
        task_id=dag_id,
        trigger_dag_id=dag_id,
        allowed_states=allowed_states,
        wait_for_completion=True,
        trigger_rule='all_done',
        env_resolver=env_resolver,
        on_success_callback=success_callback,
        conf=kwargs['task_instance'].dag_run.conf
    )


class TriggerImplyOperator(BaseOperator):
    """
    This is needed to inherit context to the imply operator
    Basically operator that triggers imply should have the same dag config as parental dag
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def execute(self, context):
        operator = get_dag(IMPLY_FLOW_NAME, [State.SUCCESS], **context)
        return operator.execute(context)


def skip_running_dag_if_not_in_config(dag_id, dag_run_task_id, skipped_task_id):
    def _skip_running_dag_if_not_in_config(**kwargs):
        tasks_to_execute = kwargs['dag_run'].conf.get('airflow_flows')
        if tasks_to_execute is None:
            return dag_run_task_id
        else:
            return dag_run_task_id if dag_id in tasks_to_execute else skipped_task_id
    return _skip_running_dag_if_not_in_config


def list_ingestion_dags_to_run(all_ingestion_dags, dag_config):
    logging.info(f'All ingestion DAGs: {all_ingestion_dags}')
    ingestion_dags_to_run = dag_config.get("airflow_flows")
    logging.info(f'Ingestion DAGs to run: {ingestion_dags_to_run}')
    if ingestion_dags_to_run is None:
        return [f'running_airflow_dags.trigger_{ingestion_dag_id}' for ingestion_dag_id in all_ingestion_dags]
    else:
        return [f'running_airflow_dags.trigger_{ingestion_dag_id}' for ingestion_dag_id in ingestion_dags_to_run]


def set_dag_conf_based_on_schedule_time(**kwargs_approx_times):
    """
    this function will be used in branching ops
    it will handle changing config based on the schedule time

    so that additional runs will only schedule glue + imply

    checks for the dag start time to be near the additional run time schedule
    modifies the tasks to execute if needed
    """
    tasks_to_set = ['glue', 'imply', 'extra']
    maximum_offset_minutes = 120 # minutes


    def is_scheduled_run(**kwargs):
        return not kwargs['dag_run'].external_trigger
    
    def _set_dag_conf_based_on_schedule_time(**kwargs):
        execution_date = datetime.datetime.now().strftime("%H:%M")
        execution_date = datetime.datetime.strptime(execution_date, "%H:%M")
        return tasks_to_set if any(
            [abs(int((schedule_time - execution_date).total_seconds() / 60)) < maximum_offset_minutes for schedule_time
             in GLUE_IMPLY_ONLY_APPROX_TIMES]
            ) and is_scheduled_run(**kwargs) else kwargs['dag_run'].conf.get('tasks')

    return _set_dag_conf_based_on_schedule_time(**kwargs_approx_times)


def skip_running_task_if_not_in_config(task_id, run_task_id, skipped_task_id):
    def _skip_running_task_if_not_in_config(**kwargs):
        tasks_to_execute = set_dag_conf_based_on_schedule_time(**kwargs)
        if tasks_to_execute == kwargs['dag_run'].conf.get('tasks'):
            logging.info('No modification to the tasks list')
        else:
            logging.info('Tasks list has been modified due to scheduling time')
            logging.info(f'list is {tasks_to_execute}')
        if tasks_to_execute is None:
            return run_task_id
        else:
            return run_task_id if task_id in tasks_to_execute else skipped_task_id
    return _skip_running_task_if_not_in_config


def check_if_need_to_skip_k8s(**kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    os.environ["TIMEZONE_GROUP"] = str(current_run_number)
    skip_task = 'skip_k8s'
    run_task = 'k8s_ds_jobs'
    logging.info(f'run_number = {current_run_number}')
    if current_run_number is None:
        logging.info('Manual run detected, skipping k8s...')
        return skip_task
    if current_run_number > 2:
        logging.info('skipping k8s')
        return skip_task
    return run_task

def check_if_need_to_skip_k8s_aire4(**kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    os.environ["TIMEZONE_GROUP"] = str(current_run_number)
    skip_task = 'skip_k8s_aire4'
    run_task = 'k8s_aire4_jobs'
    logging.info(f'run_number = {current_run_number}')
    if current_run_number is None:
        logging.info('Manual run detected, skipping k8s_aire4_jobs...')
        return skip_task
    if current_run_number > 2:
        logging.info('skipping k8s_aire4_jobs')
        return skip_task
    return run_task


def post_dag_metadata_push(pre_airflow_task, post_airflow_task,
        calculate_glue=False, pre_glue_task='check_if_need_to_skip_glue', post_glue_task='glue_done',
        pre_glue_extra_task='check_if_need_to_skip_extra_tasks', post_glue_extra_task='finish',
         **kwargs):
    """
    task pushes the datadog metrics after the whole run of the dag
    """
    execution_date = kwargs['dag_run'].start_date
    current_time = datetime.datetime.now(pytz.utc).astimezone(execution_date.tzinfo)
    delta_seconds = (current_time - execution_date).total_seconds()
    env_resolver = EnvironmentResolver()
    init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
    logging.info(f'dag runtime: {delta_seconds}')
    flow_id = kwargs['dag'].dag_id
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    increment_metric('airflow_execution_time.increment', env_resolver.environment, f'flow_{flow_id}', delta_seconds,
                additional_tags=[f'run_number:{current_run_number}']
                )
    # now we should push also exec time of the airflow flows
    pre_airflow_task_start_date = kwargs['dag_run'].get_task_instance(pre_airflow_task).start_date
    post_airflow_task_start_date = kwargs['dag_run'].get_task_instance(post_airflow_task).start_date
    delta_seconds = (post_airflow_task_start_date - pre_airflow_task_start_date).total_seconds()
    increment_metric('airflow_execution_time.increment', env_resolver.environment, flow_id + 'task_airflow_flows', delta_seconds,
                additional_tags=[f'run_number:{current_run_number}']
                )
    logging.info(f'airflow took {delta_seconds}')
    if calculate_glue:
        # now we should push also exec time of the all glue jobs
        pre_glue_task_start_date = kwargs['dag_run'].get_task_instance(pre_glue_task).start_date
        post_glue_task_start_date = kwargs['dag_run'].get_task_instance(post_glue_task).start_date
        delta_seconds = (post_glue_task_start_date - pre_glue_task_start_date).total_seconds()
        increment_metric('airflow_execution_time.increment', env_resolver.environment, flow_id + 'task_glue', delta_seconds,
                    additional_tags=[f'run_number:{current_run_number}']
                    )
        logging.info(f'glue took {delta_seconds}')    

        # now we should push also exec time of the all glue jobs in the Extra task
        pre_glue_extra_task_start_date = kwargs['dag_run'].get_task_instance(pre_glue_extra_task).start_date
        post_glue_extra_task_start_date = kwargs['dag_run'].get_task_instance(post_glue_extra_task).start_date
        delta_seconds = (post_glue_extra_task_start_date - pre_glue_extra_task_start_date).total_seconds()
        increment_metric('airflow_execution_time.increment', env_resolver.environment, flow_id + 'task_glue_extra', delta_seconds,
                    additional_tags=[f'run_number:{current_run_number}']
                    )
        logging.info(f'glue extra took {delta_seconds}') 


with DAG(
        dag_id=FLOW_NAME,
        schedule_interval="0 4,8,11,13,17,19 * * *",
        description="main dag to trigger base flows",
        start_date=datetime.datetime(2022, 1, 1),
        on_failure_callback=failure_callback,
        params=DagConfigSetter(FLOW_NAME).get_params(),
        max_active_runs=1,
        render_template_as_native_obj=True
) as dag:

    push_metadata = PythonOperator(
        task_id='push_metadata',
        dag=dag,
        python_callable=get_current_runtype
    )

    check_if_need_to_skip_airflow = BranchPythonOperator(
        task_id='check_if_need_to_skip_airflow',
        python_callable=skip_running_task_if_not_in_config('airflow', 'running_airflow', 'skip_airflow'),
        dag=dag,
        executor_config=BRANCH_PODS,
        trigger_rule='all_success',
    )

    all_main_dags = get_main_dags()
    running_airflow = DummyOperator(task_id='running_airflow', dag=dag)

    with TaskGroup(group_id="running_airflow_dags") as running_airflow_dags:

        airflow_branching = BranchPythonOperator(
            task_id='airflow_branching',
            python_callable=list_ingestion_dags_to_run,
            op_args=[all_main_dags, '{{ dag_run.conf }}'],
            executor_config=BRANCH_PODS,
        )

        for dag_id in all_main_dags:
            trigger_task = TriggerDagRunOperator(
                task_id=f'trigger_{dag_id}',
                trigger_dag_id=dag_id,
                dag=dag,
                retries=0,
                wait_for_completion=True,
                conf='{{ dag_run.conf }}',
                executor_config=TRIGGER_DAG_NODES,
                execution_timeout=get_current_runs_ttk(),
            )
            airflow_branching >> trigger_task

    running_airflow >> running_airflow_dags

    skip_airflow = DummyOperator(task_id='skip_airflow', dag=dag)

    push_metadata >> check_if_need_to_skip_airflow >> [running_airflow, skip_airflow]

    check_if_need_to_skip_glue = BranchPythonOperator(
        task_id='check_if_need_to_skip_glue',
        python_callable=skip_running_task_if_not_in_config('glue', GLUE_FLOW_NAME, 'skip_glue'),
        dag=dag,
        trigger_rule='all_done',
        executor_config=BRANCH_PODS
    )

    skip_glue = DummyOperator(task_id='skip_glue', dag=dag)

    running_airflow_dags >> check_if_need_to_skip_glue
    skip_airflow >> check_if_need_to_skip_glue

    glue_dag = TriggerDagRunOperator(
        task_id=GLUE_FLOW_NAME,
        trigger_dag_id=GLUE_FLOW_NAME,
        allowed_states=[State.SUCCESS, State.FAILED],
        wait_for_completion=True,
        trigger_rule='none_failed',
        retries=0,
        conf='{{ dag_run.conf }}',
        on_success_callback=success_callback,
        executor_config=CUSTOM_TRIGGER_PODS
    )

    check_if_need_to_skip_imply = BranchPythonOperator(
        task_id='check_if_need_to_skip_imply',
        python_callable=skip_running_task_if_not_in_config('imply', IMPLY_FLOW_NAME, 'skip_imply'),
        dag=dag,
        trigger_rule='none_failed',
        executor_config=BRANCH_PODS
    )

    glue_done = DummyOperator(task_id='glue_done', dag=dag, trigger_rule='none_failed')

    check_if_need_to_skip_glue >> [glue_dag, skip_glue]
    glue_dag >> glue_done
    skip_glue >> glue_done

    imply_dag = TriggerDagRunOperator(
        task_id=IMPLY_FLOW_NAME,
        trigger_dag_id=IMPLY_FLOW_NAME,
        allowed_states=[State.SUCCESS, State.FAILED],
        wait_for_completion=True,
        trigger_rule='none_failed',
        retries=0,
        conf='{{ dag_run.conf }}',
        on_success_callback=success_callback,
        executor_config=CUSTOM_TRIGGER_PODS
    )
    skip_imply = DummyOperator(task_id='skip_imply', dag=dag)

    post_extra_dag = TriggerDagRunOperator(
        task_id=GLUE_DS_FLOW_NAME,
        trigger_dag_id=GLUE_DS_FLOW_NAME,
        allowed_states=[State.SUCCESS, State.FAILED],
        wait_for_completion=True,
        trigger_rule='none_failed',
        retries=0,
        conf='{{ dag_run.conf }}',
        on_success_callback=success_callback,
        executor_config=CUSTOM_TRIGGER_PODS
    )



    datalake_snowlfake_copy_dag = TriggerDagRunOperator(
        task_id=RAW_DATALAKE_COPY_DAG_ID,
        trigger_dag_id=RAW_DATALAKE_COPY_DAG_ID,
        allowed_states=[State.SUCCESS, State.FAILED],
        wait_for_completion=True,
        trigger_rule='none_failed',
        retries=0,
        conf='{{ dag_run.conf }}',
        on_success_callback=success_callback,
        executor_config=CUSTOM_TRIGGER_PODS
    )



    check_if_need_to_skip_extra_tasks = BranchPythonOperator(
        task_id='check_if_need_to_skip_extra_tasks',
        python_callable=skip_running_task_if_not_in_config('extra', GLUE_DS_FLOW_NAME, 'skip_extra'),
        dag=dag,
        trigger_rule='none_failed',
        executor_config=BRANCH_PODS
    )

    k8s_dag = TriggerDagRunOperator(
        task_id='k8s_ds_jobs',
        trigger_dag_id='k8s_ds_jobs',
        allowed_states=[State.SUCCESS, State.FAILED],
        wait_for_completion=True,
        trigger_rule='none_failed',
        retries=0,
        conf='{{ dag_run.conf }}',
        on_success_callback=success_callback,
        executor_config=CUSTOM_TRIGGER_PODS
    )
    skip_k8s = DummyOperator(task_id='skip_k8s', dag=dag)

    check_if_need_to_skip_k8s_jobs = BranchPythonOperator(
        task_id='check_if_need_to_skip_k8s_jobs',
        python_callable=check_if_need_to_skip_k8s,
        dag=dag,
        trigger_rule='none_failed',
        executor_config=BRANCH_PODS
    )

    k8s_aire4_dag = TriggerDagRunOperator(
        task_id='k8s_aire4_jobs',
        trigger_dag_id='k8s_aire4_jobs',
        allowed_states=[State.SUCCESS, State.FAILED],
        wait_for_completion=True,
        trigger_rule='none_failed',
        retries=0,
        conf='{{ dag_run.conf }}',
        on_success_callback=success_callback,
        executor_config=CUSTOM_TRIGGER_PODS
    )

    skip_k8s_aire4 = DummyOperator(task_id='skip_k8s_aire4', dag=dag)

    check_if_need_to_skip_k8s_aire4_jobs = BranchPythonOperator(
        task_id='check_if_need_to_skip_k8s_aire4',
        python_callable=check_if_need_to_skip_k8s_aire4,
        dag=dag,
        trigger_rule='none_failed',
        executor_config=BRANCH_PODS
    )

    skip_extra = DummyOperator(task_id='skip_extra', dag=dag)

    finish = PythonOperator(task_id='finish',python_callable=post_dag_metadata_push,
             dag=dag, trigger_rule='none_failed', op_args=['running_airflow', 'check_if_need_to_skip_glue', True])

    glue_done >> [check_if_need_to_skip_imply, check_if_need_to_skip_extra_tasks]
    check_if_need_to_skip_imply >> [imply_dag, skip_imply]
    imply_dag >> check_if_need_to_skip_k8s_jobs
    skip_imply >> check_if_need_to_skip_k8s_jobs

    check_if_need_to_skip_extra_tasks >> [post_extra_dag, skip_extra]
    post_extra_dag >> datalake_snowlfake_copy_dag >> check_if_need_to_skip_k8s_jobs >> [k8s_dag, skip_k8s]
    skip_extra >> datalake_snowlfake_copy_dag >> check_if_need_to_skip_k8s_jobs
    [k8s_dag, skip_k8s] >> check_if_need_to_skip_k8s_aire4_jobs

    check_if_need_to_skip_k8s_aire4_jobs >> [k8s_aire4_dag, skip_k8s_aire4] >> finish
