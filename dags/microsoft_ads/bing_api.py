import logging
import time
import re
import requests
import zipfile
import io
import pandas as pd
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.constants import MICROSOFT_ADS_REPORTING_URL
from myn_airflow_lib.datadog import increment_metric
from myn_airflow_lib.exceptions import OmniFlowFailed


FLOW_NAME = 'MICROSOFT_ADS'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment


def send_request_status_to_datadog(request_type, company_id, success=True, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    status = 'success' if success else 'error'
    increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                     additional_tags=[f"request_type:{request_type}", f"request_status:{status}",
                                      f"client:{company_id}", f'run_number:{current_run_number}'])


class MicrosoftAdsApi:

    def __init__(self, developer_token, access_token, request_retries_on_error=3, query_type=None, company_id=None):
        self.developer_token = developer_token
        self.access_token = access_token
        self.request_retries_on_error = request_retries_on_error
        self.query_type = query_type
        self.company_id = company_id

    def _get_xml_auth_header(self, action, account_id, customer_id):
        return f'''
            <s:Header xmlns="https://bingads.microsoft.com/Reporting/v13">
                <Action mustUnderstand="1">{action}</Action>
                <AuthenticationToken i:nil="false">{self.access_token}</AuthenticationToken>
                <CustomerAccountId i:nil="false">{account_id}</CustomerAccountId>
                <CustomerId i:nil="false">{customer_id}</CustomerId>
                <DeveloperToken i:nil="false">{self.developer_token}</DeveloperToken>
            </s:Header>
        '''

    def _get_xml_time_filter(self, date_from, date_to):
        if isinstance(date_from, str):
            date_from = pd.to_datetime(date_from)
        if isinstance(date_to, str):
            date_to = pd.to_datetime(date_to)
        return f'''
            <Time i:nil="false">
                <CustomDateRangeEnd i:nil="false">
                    <Day>{date_to.day}</Day>
                    <Month>{date_to.month}</Month>
                    <Year>{date_to.year}</Year>
                </CustomDateRangeEnd>
                <CustomDateRangeStart i:nil="false">
                    <Day>{date_from.day}</Day>
                    <Month>{date_from.month}</Month>
                    <Year>{date_from.year}</Year>
                </CustomDateRangeStart>
            </Time>
        '''

    def submit_report(self, level, campaign_id, customer_id, account_id, date_from, date_to,
                      columns, **kwargs):
        if level.lower() == 'ad':
            level = "Ad"
        elif level.lower() == 'campaign':
            level = "Campaign"
        else:
            raise ValueError("level must be in ['Ad', 'Campaign']")
        if type(campaign_id) == list:
            campaign = ''
            for c in campaign_id:
                campaign += f"""<CampaignReportScope><AccountId>{account_id}</AccountId>
                            <CampaignId>{c}</CampaignId></CampaignReportScope>"""
        else:
            campaign = f"""<CampaignReportScope><AccountId>{account_id}</AccountId>
                                    <CampaignId>{campaign_id}</CampaignId></CampaignReportScope>"""
        report_name = f'report_{int(time.time() * 1000)}'
        xml_columns = "\n".join(f"<{level}PerformanceReportColumn>{c}</{level}PerformanceReportColumn>"
                                for c in columns)
        url = MICROSOFT_ADS_REPORTING_URL
        get_data = lambda: f'''
            <s:Envelope xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
            {self._get_xml_auth_header("SubmitGenerateReport", account_id, customer_id)}
            <s:Body>
                <SubmitGenerateReportRequest xmlns="https://bingads.microsoft.com/Reporting/v13">
                <ReportRequest i:nil="false" i:type="{level}PerformanceReportRequest">
                    <ExcludeReportFooter i:nil="false">true</ExcludeReportFooter>
                    <ExcludeReportHeader i:nil="false">true</ExcludeReportHeader>
                    <ReportName i:nil="false">{report_name}</ReportName>
                    <ReturnOnlyCompleteData i:nil="false">false</ReturnOnlyCompleteData>
                    <Aggregation>Daily</Aggregation>
                    <Columns i:nil="false">
                        {xml_columns}
                    </Columns>
                    <Filter i:nil="false"></Filter>
                    <Scope i:nil="false">
                        <Campaigns i:nil="false">
                                {campaign}
                        </Campaigns>
                    </Scope>
                    {self._get_xml_time_filter(date_from, date_to)}
                </ReportRequest>
                </SubmitGenerateReportRequest>
            </s:Body>
            </s:Envelope>
        '''
        response = requests.post(url, data=get_data(), headers={
            'SOAPAction': 'SubmitGenerateReport',
            'Content-Type': 'text/xml',
        })
        if 'AuthenticationTokenExpired' in response.text:
            send_request_status_to_datadog(self.query_type, self.company_id, success=False, **kwargs)
            msg = 'Cant process the request - AuthenticationTokenExpired'
            logging.info(msg)
            raise OmniFlowFailed(msg)
        report_ids = re.findall(r'<ReportRequestId>([^<]+)</ReportRequestId>', response.text)
        if not report_ids:
            logging.error(f"Report id is not obtained when trying to submit report generation."
                          f" Full resp text `{response.text}`")
            send_request_status_to_datadog(self.query_type, self.company_id, success=False, **kwargs)
        else:
            send_request_status_to_datadog(self.query_type, self.company_id, success=True, **kwargs)
        return report_ids[0]

    def _process_download_url(self, url, **kwargs):
        r = requests.get(url)
        send_request_status_to_datadog(self.query_type, self.company_id, success=r.status_code == 200, **kwargs)
        zf = zipfile.ZipFile(io.BytesIO(r.content))
        for filename in zf.namelist():
            if not filename.endswith('.csv'):
                continue
            with zf.open(filename) as f:
                return f.read().decode().strip()[1:]

    def get_report_content(self, customer_id, account_id, report_id, **kwargs):
        url = MICROSOFT_ADS_REPORTING_URL
        get_data = lambda: f'''
            <s:Envelope xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
            {self._get_xml_auth_header("SubmitGenerateReport", account_id, customer_id)}
            <s:Body>
                <PollGenerateReportRequest xmlns="https://bingads.microsoft.com/Reporting/v13">
                    <ReportRequestId i:nil="false">{report_id}</ReportRequestId>
                </PollGenerateReportRequest>
            </s:Body>
            </s:Envelope>
        '''
        response = requests.post(url, data=get_data(), headers={
            'SOAPAction': 'PollGenerateReport',
            'Content-Type': 'text/xml',
        })
        if 'AuthenticationTokenExpired' in response.text:
            send_request_status_to_datadog(self.query_type, self.company_id, success=False, **kwargs)
            msg = 'Cant process the request - AuthenticationTokenExpired'
            logging.info(msg)
            raise OmniFlowFailed(msg)
        statuses = re.findall(r'<Status>([^<]+)</Status>', response.text)
        if not statuses:
            send_request_status_to_datadog(self.query_type, self.company_id, success=False, **kwargs)
            raise OmniFlowFailed(f"Status not found in PollStatus response. Resp `{response.text}`")
        send_request_status_to_datadog(self.query_type, self.company_id, success=True, **kwargs)
        status = statuses[0]
        if status == "Error":
            raise OmniFlowFailed(f"Status of PollStatus response is 'Error'. Resp `{response.text}`")
        elif status == "Pending":
            logging.info(f"Status of PollStatus response is 'Pending'")
            return False
        elif status == "Success":
            download_urls = re.findall(r'<ReportDownloadUrl>([^<]+)</ReportDownloadUrl>', response.text)
            if not download_urls:
                # report is empty
                return None
            download_url = download_urls[0].replace('&amp;', '&')
            return self._process_download_url(download_url, **kwargs)
        else:
            raise OmniFlowFailed(f"Status of PollStatus response is '{status}'. Resp `{response.text}`")


def squash_queries(queries):
    """
    this function merges requests into one
    """
    ad_level = [q.campaign['external_id'] for q in queries if q.level.lower() == 'ad']
    campaign_level = [q.campaign['external_id'] for q in queries if q.level.lower() == 'campaign']
    
    return [
        {
            'query_type': 'standard',
            'level': 'Ad',
            'profile': queries[0].profile,
            'campaign': ad_level,
            'start_date': queries[0].start_date,
            'end_date': queries[0].end_date,
        },
        {
            'query_type': 'standard',
            'level': 'Campaign',
            'profile': queries[0].profile,
            'campaign': campaign_level,
            'start_date': queries[0].start_date,
            'end_date': queries[0].end_date,
        },
        ]
