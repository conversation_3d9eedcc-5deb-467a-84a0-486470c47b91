import datetime
import json
import logging
import time
import io
import traceback
import pandas as pd

from pandas import DataFrame
from dataclasses import dataclass
from typing import List
from collections import deque

from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import <PERSON>g<PERSON>Hook
from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.constants import OMNI_ETL_ENDPOINT, EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.commons import (EtlEndpointProcessorWithParams, failure_callback, filter_out_standard_queries,
                                     set_execution_dates, _get_db_value, iter_batch, set_config,
                                     enable_hist_log_check_based_on_conf, safe_cast_to_float, get_date_to_execute_on)
from microsoft_ads.bing_api import MicrosoftAdsApi, FLOW_NAME, squash_queries


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True
CAMPAIGN_TYPE = 'microsoft_ads'  # in historical log table
MAX_WAIT_FOR_REPORT_SECONDS = 60 * 15
WAIT_SLEEP = 100
WAIT_SLEEP_AFTER_GETTING_NEW_TOKEN = 1
PLATFORM_NAME = 'bing_integrated'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
state = {
    'dates': {}
}
dates = state['dates']
historical_log_cache = None


@dataclass
class QueryData:
    query_type: str  # 'standard' or 'historical'
    level: str  # 'campaign' or 'ad'
    profile: dict
    campaign: dict
    start_date: str
    end_date: str
    report_id: str = None
    report_submitted_at: float = None

    @property
    def campaign_detail(self):
        return json.dumps({
            'level': self.level,
            'campaign_id': self.campaign['external_id'],
            'account_id': self.profile['ad_account_id'],
            'customer_id': self.profile['bing_customer_id'],
        }, sort_keys=True)

    @property
    def company_id(self):
        return self.profile['company_id']

    @property
    def customer_id(self):
        return self.profile['bing_customer_id']

    @property
    def account_id(self):
        return self.profile['ad_account_id']

    @property
    def access_token(self):
        return self.profile['access_token']

    def to_dict(self):
        if 'campaigns' in self.profile:
            # don't include campaigns data. otherwise XCOM grows 1000x in size
            del self.profile['campaigns']
        return {
            'query_type': self.query_type,
            'level': self.level,
            'profile': self.profile,
            'campaign': self.campaign,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'report_id': self.report_id,
            'report_submitted_at': self.report_submitted_at,
        }


def filter_profiles(profile):
    return (
            profile.get('ad_account_id') is not None
            and profile.get('campaigns')
    )


def get_profiles(**kwargs):
    new_endpoint = OMNI_ETL_ENDPOINT + f'/?platform={PLATFORM_NAME}'
    raw_profiles = EtlEndpointProcessorWithParams(new_endpoint, **kwargs).apply_all()
    # mock data
    mocked_resp = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    if mocked_resp:
        logging.info('Mocked endpoint was found - using that...')
        raw_profiles = mocked_resp
    return [
        profile for profile in raw_profiles
        if filter_profiles(profile)
    ]


def get_standard_queries(profile, campaign, **kwargs):
    start_date = get_date_to_execute_on(dates, '%Y-%m-%d', **kwargs)
    end_date = dates['YESTERDAY'].strftime('%Y-%m-%d')
    standard_queries = [
        QueryData('standard', 'Ad', profile, campaign, start_date, end_date),
        QueryData('standard', 'Campaign', profile, campaign, start_date, end_date),
    ]
    return standard_queries


def get_historical_queries(profile, campaign, historical_range_mock):
    min_start_date = (dates['TODAY'] - datetime.timedelta(365 * 2))
    max_end_date = dates['YESTERDAY']
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        min_start_date = dates['TODAY'] - datetime.timedelta(historical_range_mock)
    start_date = min_start_date.strftime('%Y-%m-%d')
    end_date = max_end_date.strftime('%Y-%m-%d')
    historical_queries = [
        QueryData('historical', 'Ad', profile, campaign, start_date, end_date),
        QueryData('historical', 'Campaign', profile, campaign, start_date, end_date),
    ]
    return [
        query for query in historical_queries
        if not does_hist_log_exist(query)
    ]


def filter_out_existing_queries(new_batch, queries):
    get_query_key = lambda q: (
        q.campaign_detail, q.query_type, q.access_token
    )
    existing_keys = {
        get_query_key(q) for q in queries
    }
    filtered_queries = []
    for query in new_batch:
        key = get_query_key(query)
        if key not in existing_keys:
            filtered_queries.append(query)
            existing_keys.add(key)
    return filtered_queries


@set_config(state)
@set_execution_dates(dates)
def generate_queries(profiles, **kwargs):
    logging.info(f"generate_queries from {len(profiles)} profiles")
    historical_range_mock = state['dag_run'].conf.get('historical_range_in_days')
    standard_queries_count, historical_queries_count = 0, 0
    queries = []
    only_standard = []
    only_hist = []
    for profile in profiles:
        logging.info(f"Processing profile {profile}")
        campaigns = profile['campaigns']
        standard_batch = []

        for campaign in campaigns:
            if not campaign:
                continue
            # standard queries
            standard_queries = get_standard_queries(profile, campaign, **kwargs)
            standard_queries = filter_out_existing_queries(standard_queries, only_standard)
            standard_batch.extend(standard_queries)
            only_standard.extend(standard_queries)

            # historical queries
            historical_queries = get_historical_queries(profile, campaign, historical_range_mock)
            historical_queries = filter_out_existing_queries(historical_queries, only_hist)
            only_hist.extend(historical_queries)
            queries.extend(historical_queries)
            historical_queries_count += len(historical_queries)
        standard_batch = squash_queries(standard_batch)
        standard_batch = [QueryData(**batch) for batch in standard_batch]
        queries.extend(standard_batch)
        standard_queries_count += len(standard_batch)
    logging.info(f"Generated {historical_queries_count+standard_queries_count} queries total."
                 f" {standard_queries_count} standard and {historical_queries_count} historical")
    return filter_out_standard_queries([
        query.to_dict() for query in queries
    ], **kwargs)


def submit_report(query: QueryData, **kwargs):
    api = MicrosoftAdsApi(developer_token=env_resolver.microsoft_ads_developer_token,
                          access_token=query.access_token,
                          request_retries_on_error=2,
                          query_type=query.query_type,
                          company_id=query.company_id)
    if query.level.lower() == 'ad':
        columns = ["TimePeriod", "CampaignId", "AdGroupId", "AdId", 
                   "AdType", "CurrencyCode", "Impressions", "Clicks", "Spend",
                   "ConversionsQualified"]
    elif query.level.lower() == 'campaign':
        # API requires to always include "Impressions"
        columns = ["TimePeriod", "CampaignId",
                   "ImpressionLostToBudgetPercent", "ImpressionLostToRankAggPercent",
                   "ImpressionSharePercent","CampaignType", "CurrencyCode", "Impressions", "Clicks", "Spend",
                   "ConversionsQualified"]
    if type(query.campaign) == list:
        camp = query.campaign
    else:
        camp = query.campaign['external_id']
    report_id = api.submit_report(query.level, camp, query.customer_id, query.account_id,
                                  query.start_date, query.end_date, columns, **kwargs)
    return report_id


def _cast_percent_column_to_float(v):
    if isinstance(v, str):
        v = v.strip()
        if v.endswith('%'):
            return float(v[:-1]) / 100
    return safe_cast_to_float(v)


def parse_report_content(report_content: str):
    df = pd.read_csv(io.StringIO(report_content))
    logging.info(f"Len df = {len(df)}")
    df.rename(
        inplace=True,
        columns={
            'TimePeriod': 'date',
            'CampaignId': 'campaign_id',
            'AdGroupId': 'ad_group_id',
            'AdId': 'ad_id',
            'AdType': 'ad_type',
            'CampaignType': 'ad_type',
            'CurrencyCode': 'currency_code',
            'Impressions': 'impressions',
            'Clicks': 'clicks',
            'Spend': 'spend',
            'ConversionsQualified': 'conversions_qualified',
            'ImpressionLostToBudgetPercent': 'impression_lost_to_budget_percent',
            'ImpressionLostToRankAggPercent': 'impression_lost_to_rank_agg_percent',
            'ImpressionSharePercent': 'impression_share_percent',
        }
    )
    for percent_col in df.columns:
        if percent_col.endswith('_percent'):
            df[percent_col] = df[percent_col].apply(_cast_percent_column_to_float)
    return df


def poll_and_process_report(query: QueryData, **kwargs):
    company_id = query.company_id
    api = MicrosoftAdsApi(developer_token=env_resolver.microsoft_ads_developer_token,
                          access_token=query.access_token,
                          request_retries_on_error=2,
                          query_type=query.query_type,
                          company_id=company_id)
    report_content = api.get_report_content(query.customer_id,
                                            query.account_id,
                                            query.report_id,
                                            **kwargs)
    if report_content is False:
        logging.info(f"Report is not ready")
        return False
    elif report_content is None:
        logging.warning(f"Report is empty for query {query}")
        if query.query_type == 'historical':
            log_hist_request(query)
        return True
    logging.info(f"Received report content for query {query}")

    df = parse_report_content(report_content)
    if query.level.lower() == 'ad':
        put_report_to_db_ad(df, company_id)
    elif query.level.lower() == 'campaign':
        put_report_to_db_campaign(df, company_id)

    if query.query_type == 'historical':
        # log historical to historical_log
        logging.info("Writing historical query to historical_log")
        log_hist_request(query)
    return True
    

@set_execution_dates(dates)
def process_queries(generated_batch: List[dict], **kwargs):
    logging.info(f"process_queries {len(generated_batch)} {generated_batch}")
    errors_count = 0
    historical_errors_count = 0
    standard_errors_count = 0
    errors_occurred_with_this_profiles = set()
    # submit async reports
    query_queue = deque()
    for i, query_data in enumerate(generated_batch):
        logging.info(f"Submitting query_data [{i} / {len(generated_batch)}] {query_data}")
        try:
            query = QueryData(**query_data)
            report_id = submit_report(query, **kwargs)
            logging.info(f"Submitted Query. report_id={report_id}")
            query.report_id = report_id
            query.report_submitted_at = time.time()
            query_queue.append(query)
            time.sleep(1)
        except Exception as e:
            logging.error(f"Exception while submitting query {query_data} ; {e} ; {traceback.format_exc()}")
            errors_count += 1
            if query.query_type == 'historical':
                historical_errors_count += 1
            else:
                standard_errors_count += 1
            errors_occurred_with_this_profiles.add(
                json.dumps(query.profile, indent=4, sort_keys=True)
            )
    # poll and download report if ready
    while len(query_queue):
        query = query_queue.popleft()
        try:
            processed = poll_and_process_report(query, **kwargs)
            if not processed and time.time() - query.report_submitted_at < MAX_WAIT_FOR_REPORT_SECONDS:
                query_queue.append(query)
                time.sleep(5)
        except Exception as e:
            logging.error(f"Exception while processing report for query {query} ; {e}; {traceback.format_exc()}")
            errors_count += 1
            if query.query_type == 'historical':
                historical_errors_count += 1
            else:
                standard_errors_count += 1
            errors_occurred_with_this_profiles.add(
                json.dumps(query.profile, indent=4, sort_keys=True)
            )
    
    logging.info(f"processed {len(generated_batch)} queries. There were {errors_count} errors")
    logging.info(f"historical_errors_count={historical_errors_count};")
    logging.info(f"standard_errors_count={standard_errors_count};")
    logging.info(f"errors_occurred_with_this_profiles={errors_occurred_with_this_profiles};")
    return [{"total": len(generated_batch), "errors": errors_count,
             "historical_errors_count": historical_errors_count,
             "standard_errors_count": standard_errors_count,
             "errors_occurred_with_this_profiles": list(errors_occurred_with_this_profiles)}]


def print_processing_summary(results):
    total, errors = 0, 0
    historical_errors_count, standard_errors_count = 0, 0
    errors_occurred_with_this_profiles = set()
    for row in results:
        total += row['total']
        errors += row['errors']
        historical_errors_count += row['historical_errors_count']
        standard_errors_count += row['standard_errors_count']
        errors_occurred_with_this_profiles.update(row['errors_occurred_with_this_profiles'])
    errors_occurred_with_this_profiles = [
        json.loads(p) for p in errors_occurred_with_this_profiles
    ]
    logging.info(f"Processed {total} queries. Of which there were {errors} errors"
                 f" ({historical_errors_count} historical errors & {standard_errors_count} standard errors)")
    logging.info(f"errors_occurred_with_this_profiles (count={len(errors_occurred_with_this_profiles)})"
                 f" {json.dumps(errors_occurred_with_this_profiles, indent=4)}")


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(query: QueryData):
    global historical_log_cache
    if historical_log_cache is None:
        sql = f"""
            select campaign_detail
            from {HISTORICAL_LOG_TABLE}
            where campaign_type = '{CAMPAIGN_TYPE}'
        """
        df = pg_hook.get_pandas_df(sql)
        if df.empty:
            historical_log_cache = set()
        else:
            df['campaign_detail'] = df['campaign_detail'].apply(
                lambda cd: json.dumps(cd, sort_keys=True))
            historical_log_cache = set(df['campaign_detail'])
    return query.campaign_detail in historical_log_cache


def log_hist_request(query: QueryData):
    campaign_detail = query.campaign_detail
    company_id = query.company_id
    sql = f"""
        INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
        VALUES (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
    """
    data = {'company_id': company_id,
            'campaign_type': CAMPAIGN_TYPE,
            'campaign_detail': campaign_detail}
    pg_hook.run(sql, True, data)


def _get_sql_columns_and_values(report, columns_order):
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    return columns_str, values_str


def put_report_to_db_ad(report: DataFrame, company_id: int):
    columns_order = ['date', 'campaign_id', 'ad_group_id',
                     'ad_id', 'ad_type', 'currency_code', 'impressions', 'clicks', 'spend',
                     'conversions_qualified']
    table = "microsoft_advertising_performance_ad_level"
    for i, batch_report in enumerate(iter_batch(report, n=5000)):
        logging.info(f"Inserting batch #{i} size={len(batch_report)}")
        columns_str, values_str = _get_sql_columns_and_values(batch_report, columns_order)
        sql = f"""
            INSERT INTO {table} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (date, campaign_id, ad_group_id, ad_id)
            DO UPDATE
                SET ad_type = Excluded.ad_type,
                    currency_code = Excluded.currency_code,
                    impressions = Excluded.impressions,
                    clicks = Excluded.clicks,
                    spend = Excluded.spend,
                    conversions_qualified = Excluded.conversions_qualified
            ;
        """
        pg_hook.run(sql, True)
    increment_metric('airflow_data_pushed.increment', env_type,
                     FLOW_NAME, report.shape[0], [f"table:{table}",  f"client:{company_id}"])


def put_report_to_db_campaign(report: DataFrame, company_id: int):
    columns_order = ['date', 'campaign_id',
                     'impressions', 'impression_lost_to_budget_percent',
                     'impression_lost_to_rank_agg_percent', 'impression_share_percent', 'currency_code',
                     'clicks', 'ad_type', 'spend', 'conversions_qualified']
    table = "microsoft_advertising_estimated_campaign_level_v2"
    for i, batch_report in enumerate(iter_batch(report, n=5000)):
        logging.info(f"Inserting batch #{i} size={len(batch_report)}")
        columns_str, values_str = _get_sql_columns_and_values(batch_report, columns_order)
        sql = f"""
            INSERT INTO {table} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (date, campaign_id)
            DO UPDATE
                SET impressions = Excluded.impressions,
                    impression_lost_to_budget_percent = Excluded.impression_lost_to_budget_percent,
                    impression_lost_to_rank_agg_percent = Excluded.impression_lost_to_rank_agg_percent,
                    impression_share_percent = Excluded.impression_share_percent,
                    currency_code = Excluded.currency_code,
                    clicks = Excluded.clicks,
                    ad_type = Excluded.ad_type,
                    spend = Excluded.spend,
                    conversions_qualified = Excluded.conversions_qualified
            ;
        """
        pg_hook.run(sql, True)
    increment_metric('airflow_data_pushed.increment', env_type,
                     FLOW_NAME, report.shape[0], [f"table:{table}",  f"client:{company_id}"])


with DAG(FLOW_NAME,
         description='microsoft ads (bing) dag',
         schedule_interval=None,
         tags=['ingestion_flow'],
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False,
         params=DagConfigSetter(FLOW_NAME).get_params(),
         on_failure_callback=failure_callback) as dag:
    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id='generate_queries',
        on_failure_callback=failure_callback,
        python_callable=generate_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="process_queries",
        on_failure_callback=failure_callback,
        python_callable=process_queries,
        op_kwargs={"generated_batch": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    print_processing_summary = PythonOperator(
        task_id='print_processing_summary',
        on_failure_callback=failure_callback,
        python_callable=print_processing_summary,
        op_kwargs={"results": XComArg(t3)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> print_processing_summary >> cleanup_xcoms
