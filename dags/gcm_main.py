import traceback
import datetime
import io
import json
import logging
import time
from concurrent.futures import  Thr<PERSON><PERSON><PERSON>Executor, as_completed
import threading
from copy import deepcopy
from dataclasses import dataclass
from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
import pandas as pd
import requests
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pandas import DataFrame
from myn_airflow_lib.commons import (
    EtlEndpointProcessorWithParams, _get_db_value, failure_callback, filter_out_standard_queries, set_execution_dates,
    set_config, enable_hist_log_check_based_on_conf, get_date_to_execute_on, iter_batch, split_date_range_period)
from myn_airflow_lib.constants import (
    GOOGLE_REPORTS_URL_FORMAT,
    GOOGLE_FILE_URL_FORMAT,
    CM360_ETL_ENDPOINT,
    GOO<PERSON>LE_RUN_QUERY_URL_FORMAT,
    EXECUTOR_STABLE_NODES,
    K8S_SPECS,
    HISTORICAL_LOG_TABLE
)
from myn_airflow_lib.exceptions import MintFlowFailed, NotFloodLightAccount, NotPaidSearchAccount
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog

pg_hook = PostgresHook(postgres_conn_id="uri_reports")
pg_hook.log.disabled = True
FLOW_NAME = "CM360"
env_resolver = EnvironmentResolver()
state = {
    'dates': {}
}
dates = state['dates']
CAMPAIGN_TYPES = {
   'gcm_paid_search': 'gcm_paid_search_external',
   'gcm_floodlight': 'gcm_floodlight',
   'gcm_dv360': 'gcm_dv360',
   'gcm_classic': 'gcm_v3'
}

REPORT_TABLE_NAMES = {
    'gcm_classic': 'campaign_manager_base_report',
    'gcm_paid_search': 'campaign_manager_paid_search_external_report',
    'gcm_floodlight': 'campaign_manager_floodlight_base_report',
    'gcm_dv360': 'campaign_manager_dv360_report',
}
env_type = env_resolver.environment
REPORTS_TABLE_NAME = 'campaign_manager_dv360_report'
REPORT_CLASSES = ['gcm_dv360', 'gcm_floodlight', 'gcm_paid_search', 'gcm_classic']

init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
access_tokens = {}


@dataclass
class QueryData:
    data: dict
    type: str
    profile: dict
    setting: dict
    report_class: str = None
    report_id: int = None

    @property
    def campaign_detail(self):
        advertisers = self.profile['advertisers']
        advertiser_ids = [adv['advertiser_id'] for adv in advertisers]
        campaigns = []
        for adv in advertisers:
            campaigns.extend(adv['campaigns'])
        campaigns = list(set(campaigns))

        campaign_detail = {
            "advertiser_ids": advertiser_ids,
            "campaigns": campaigns if self.report_class != 'gcm_dv360' else [],  # gcm_dv360 pulls full advertiser data
        }
        return json.dumps(campaign_detail, sort_keys=True)

    @property
    def client_id(self):
        return self.profile['client_id']

    @property
    def client_secret(self):
        return self.profile['client_secret']

    @property
    def refresh_token(self):
        return self.profile['refresh_token']

    @property
    def company_id(self):
        return self.profile['company_id']

    def to_dict(self):
        return {'data': self.data,
                'type': self.type,
                'profile': self.profile,
                'setting': self.setting,
                'report_id': self.report_id,
                'report_class': self.report_class}


def check_report_eligibility(profile, report_type):
    """
    Checks profile for report_eligibility
    """
    mapping = {
        'gcm_dv360': 'dv360_linkage',
        'gcm_floodlight': 'is_floodlight_report_eligible',
        'gcm_paid_search': 'is_paid_search_account'
    }
    if report_type in list(mapping.keys()):
        return bool(profile[mapping[report_type]])
    else:
        return True


def filter_profiles(profile):
    return all([profile['advertisers'],
                profile['profile_id'],
                profile['refresh_token'],
                profile['company_id']])


def get_profiles(**kwargs):
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    processor = EtlEndpointProcessorWithParams(CM360_ETL_ENDPOINT, **kwargs)
    profiles = processor.apply_all()
    # use this mock for testing on stage
    if config_mock:
        profiles = config_mock
    profiles = list(filter(filter_profiles, profiles))
    return profiles


def get_advertiser_and_fl_conf(dim_filters):
    result = {}
    for filtr in dim_filters:
        if filtr['dimensionName'] in ['advertiserId', 'floodlightConfigId']:
            dim_name = filtr['dimensionName']
            result[dim_name] = filtr['value']
    return result


def get_campaign_filter(dim_filters):
    result = []
    for filt in dim_filters:
        if filt['dimensionName'] == 'campaign':
            result.append(filt)
    return result


def split_campaigns(campaigns: list):
    chunk_size = 500
    return [campaigns[i:i + chunk_size] for i in range(0, len(campaigns), chunk_size)]


def optimize_hist_requests(hist_requests):
    """
    this function takes all the historical queries and process them in a way
    to produce the least amount of needed requests to cover all of them
    it uses dimension filters
    finds unique combinations of adv_id and fl_configs
    and joins campaigns across all the same adv_id and fl_configs
    """
    # filter only floodlights
    other_requests = [req for req in hist_requests if req['report_class'] != 'gcm_floodlight']
    hist_requests = [req for req in hist_requests if req['report_class'] == 'gcm_floodlight']

    optimized_list = []
    found_dims = []
    for req in hist_requests:
        dims = get_advertiser_and_fl_conf(
            req['data']['floodlightCriteria']['dimensionFilters'])
        if dims in found_dims:
            for request in optimized_list:
                if get_advertiser_and_fl_conf(
                request['data']['floodlightCriteria']['dimensionFilters']) == dims:
                    # get campaigns to add to the optimized request
                    campaigns_to_add = get_campaign_filter(req['data']['floodlightCriteria']['dimensionFilters'])
                    request['data']['floodlightCriteria']['dimensionFilters'] += campaigns_to_add
        else:
            found_dims.append(dims)
            optimized_list.append(req)

    # we need to split requests if we exceed 1000 campaigns in the same request
    splited_and_optimized = []
    for query in optimized_list:
        if len(query['data']['floodlightCriteria']['dimensionFilters']) > 1000:
            print(len(query['data']['floodlightCriteria']['dimensionFilters']))
            # get adv id and fl_conf
            main_dims = []
            campaigns = []
            for filtr in query['data']['floodlightCriteria']['dimensionFilters']:
                    if filtr['dimensionName'] in ['advertiserId', 'floodlightConfigId']:
                        main_dims.append(filtr)
                    else:
                        campaigns.append(filtr)
            campaigns = split_campaigns(campaigns)
            for campaign_collection in campaigns:
                temp_query = query
                temp_query['data']['floodlightCriteria']['dimensionFilters'] = main_dims+campaign_collection
                splited_and_optimized.append(temp_query)
        else:
            splited_and_optimized.append(query)

    return splited_and_optimized + other_requests


def get_report_template(report_type: str):
    """
    grab report template based on report type
    """
    report_path_map = {
        'gcm_dv360': "static/report_template_gcm_dv360.json",
        'gcm_floodlight': 'static/gcm_floodlight.json',
        'gcm_paid_search': 'static/report_template_gcm_paid_search_external.json',
        'gcm_classic': 'static/report_template.json'
    }
    with open(report_path_map[report_type]) as file:
        report_template = json.load(file)
    return report_template


def generate_google_access_token(client_id, client_secret, refresh_token, force_update=False):
    if refresh_token in access_tokens and not force_update:
        return access_tokens[refresh_token]
    response = requests.post('https://accounts.google.com/o/oauth2/token', json={'client_id': client_id,
                                                                                 'client_secret': client_secret,
                                                                                 'refresh_token': refresh_token,
                                                                                 'grant_type': 'refresh_token'})
    logging.info('Access token resp received')
    if 'access_token' not in response.json():
        logging.error(f"Access token requested but not received. Response `{response.text}`")
        return None
    access_tokens[refresh_token] = response.json()['access_token']
    return access_tokens[refresh_token]


def send_google_request(url: str, method: str, token_creds: dict, report_class: str, json_data: dict = None, headers: dict = None,
                        report_type: str = None, company_id: int = None, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    bad_statuses = [502, 504, 503, 429, 500, 403, 401]
    access_token = generate_google_access_token(**token_creds)
    request_data = {'headers': {'Authorization': f"Bearer {access_token}"},
                    'url': url,
                    'method': method}
    if json_data:
        request_data['json'] = json_data
    if headers:
        request_data['headers'].update(headers)
    exc = None
    for tries_made in range(5):
        try:
            response = requests.request(**request_data)
        except Exception as e:
            logging.error(f"Network exception during request execution. {e}. {request_data}")
            exc = e
            time.sleep(env_resolver.gcm2_wait_sleep)
            continue
        if response.status_code in bad_statuses:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:error",
                                              f"client:{company_id}", f'run_number:{current_run_number}'])
            logging.warning(f'send request with data = {json.dumps(request_data)} \n'
                            f'received code {response.status_code} and response was: {response.text}')
            if response.status_code == 401:
                logging.info('need new access token')
                access_token_retry = generate_google_access_token(**token_creds, force_update=True)
                request_data['headers']['Authorization'] = f"Bearer {access_token_retry}"
            elif response.status_code == 403 and report_class == 'gcm_paid_search':
                logging.warning("Skipped. Not paid search account")
                raise NotPaidSearchAccount(FLOW_NAME)
            elif response.status_code == 403 and report_class == 'gcm_floodlight':
                logging.warning("Skipped. Not floodlight account")
                raise NotFloodLightAccount(FLOW_NAME)
            time.sleep(env_resolver.gcm2_wait_sleep)
        else:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:success",
                                              f"client:{company_id}", f'run_number:{current_run_number}'])
            return response
    data_to_log = deepcopy(request_data)
    data_to_log.pop('headers')
    msg = f'Cant process the request (tries_made={tries_made + 1}) {json.dumps(data_to_log)}. Exception `{exc}`'
    raise MintFlowFailed(msg)


def get_setting_dimensions(setting):
    dimensions = []
    advertiser_id = setting['advertiser_id']
    dimension = {'dimensionName': 'advertiserId',
                 'kind': 'dfareporting#dimensionValue',
                 'value': int(advertiser_id)}
    dimensions.append(dimension)

    for c in setting['campaigns']:
        if c:
            dimension = {'dimensionName': 'campaign',
                         'kind': "dfareporting#dimensionValue",
                         'id': c}
            dimensions.append(dimension)
    return dimensions


def get_setting_dimensions_floodlight(setting, floodlight_config_id):
    dimensions = []
    dimension = {
            "kind": "dfareporting#dimensionValue",
            "dimensionName": "floodlightConfigId",
            "value": floodlight_config_id
            }
    dimensions.append(dimension)
    for c in setting['campaigns']:
        if c:
            dimension = {'dimensionName': 'campaign',
                         'kind': "dfareporting#dimensionValue",
                         'id': c}
            dimensions.append(dimension)
    return dimensions


def get_setting_dimensions_dv360(setting):
    # gcm_dv360 pulls all advertiser's data so campaigns are not needed to be specified
    dimensions = []
    advertiser_id = setting['advertiser_id']
    dimension = {'dimensionName': 'advertiserId',
                 'kind': 'dfareporting#dimensionValue',
                 'value': int(advertiser_id)}
    dimensions.append(dimension)
    return dimensions


def get_hist_requests_body(date_from, date_to, dimensions, report_class) -> dict:
    report_template = get_report_template(report_class)
    criteria_name = 'floodlightCriteria' if report_class == 'gcm_floodlight' else 'criteria'
    report_template['name'] = f"Custom_dates"
    report_template[criteria_name]['dateRange'] = {
        "kind": "dfareporting#dateRange",
        "startDate": date_from,
        "endDate": date_to
    }
    report_template[criteria_name]['dimensionFilters'] = dimensions
    return report_template


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(hist_request: QueryData):
    # if there are no campaigns and report_class is not gcm_dv360 we skip the check
    if len(hist_request.setting['campaigns']) == 0 and hist_request.report_class != 'gcm_dv360':
        return False
    campaign_detail = hist_request.campaign_detail
    query = f"""
    select id from {HISTORICAL_LOG_TABLE}
    where campaign_type = '{CAMPAIGN_TYPES[hist_request.report_class]}'
      and campaign_detail = '{campaign_detail}' limit 1;
    """
    df = pg_hook.get_pandas_df(query)
    return not df.empty


def log_hist_request(query: QueryData):
    campaign_detail = query.campaign_detail
    company_id = query.company_id
    logging.info(f'Logging hist Request: report_class={query.report_class}')
    campaign_type = CAMPAIGN_TYPES[query.report_class]
    query = f"""
            INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
            Values (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
            """
    data = {
        'company_id': company_id,
        "campaign_type": campaign_type,
        "campaign_detail": campaign_detail,
    }
    pg_hook.run(query, True, data)


def get_standard_requests_body(dimensions, report_class=None, **kwargs):
    criteria_name = 'floodlightCriteria' if report_class == 'gcm_floodlight' else 'criteria'
    report_template = get_report_template(report_class)
    report_template['name'] = f"Yesterday_A"
    today = dates['TODAY'].strftime('%Y-%m-%d')
    week_ago = get_date_to_execute_on(dates, '%Y-%m-%d', **kwargs)
    report_template[criteria_name]['dateRange'] = {
        "kind": "dfareporting#dateRange",
        "startDate": week_ago,
        "endDate": today
    }
    report_template[criteria_name]['dimensionFilters'] = dimensions
    return report_template


def get_standard_query(profile, settings, **kwargs) -> list:
    queries = []  # list will hold all created queries

    def deduplicate_queries(queries_list):
        for key, value in queries_list.items():
            seen = set()
            unique_data = []
            for item in value:
                identifier = (item.get('id'), item.get('value'))
                if identifier not in seen:
                    seen.add(identifier)
                    unique_data.append(item)
            queries_list[key] = unique_data

    def add_dimensions(key, new_dimensions):
        if key in queries_list:
            queries_list[key].extend(new_dimensions)
        else:
            queries_list[key] = new_dimensions

    for report_class in REPORT_CLASSES:
        if not check_report_eligibility(profile, report_class):
            continue
        queries_list = {}
        for setting in settings:
            # first we handle gcm_dv360 then skip null campaigns then gcm_floodlight then the rest
            if report_class == 'gcm_dv360':
                dimensions = get_setting_dimensions_dv360(setting)
                add_dimensions("standard", dimensions)
            elif len(setting['campaigns']) == 0:  # skips for classic, floodlight, paid_search
                continue
            elif report_class == 'gcm_floodlight':
                floodlight_config_id = setting.get('floodlight_activity_config')
                if floodlight_config_id:
                    dimensions = get_setting_dimensions_floodlight(setting, floodlight_config_id)
                    add_dimensions(floodlight_config_id, dimensions)
            else:
                dimensions = get_setting_dimensions(setting)
                add_dimensions("standard", dimensions)

        deduplicate_queries(queries_list)
        for dl in queries_list.values():
            standard_req_data = get_standard_requests_body(dl, report_class, **kwargs)
            standard_query = QueryData(standard_req_data, 'standard', profile, settings, report_class)
            queries.append(standard_query)
    return queries


def get_historical_query(profile, setting, historical_range_mock):
    hist_requests = []
    for report_class in REPORT_CLASSES:
        if not check_report_eligibility(profile, report_class):
            continue
        if report_class == 'gcm_floodlight':
            if 'floodlight_activity_config' in setting.keys() and setting['floodlight_activity_config'] is not None:
                floodlight_config_id = setting['floodlight_activity_config']
                dimensions = get_setting_dimensions_floodlight(setting, floodlight_config_id)
                two_month_ago = dates['TODAY'] - datetime.timedelta(1 * 59)

                hist_req_data = get_hist_requests_body(two_month_ago.strftime('%Y-%m-%d'),
                                                       dates['TODAY'].strftime('%Y-%m-%d'),
                                                       dimensions, report_class)
                hist_request = QueryData(hist_req_data, 'historical', profile, setting, report_class)

                if not does_hist_log_exist(hist_request) and len(hist_request.setting['campaigns']) != 0:
                    hist_requests.append(hist_request)
        elif report_class == 'gcm_dv360':
            dimensions = get_setting_dimensions_dv360(setting)
            two_years_ago = dates['TWO_YEARS_AGO']
            if historical_range_mock:
                logging.info('historical_range_mock is set. Using it')
                two_years_ago = dates['TODAY'] - datetime.timedelta(historical_range_mock)
            # chunk historical queries: 2 years on 4 parts
            historical_periods = split_date_range_period(date_from=two_years_ago.strftime('%Y-%m-%d'),
                                                         date_to=dates['TODAY'].strftime('%Y-%m-%d'),
                                                         period_days=185)
            for period in historical_periods:
                hist_req_data = get_hist_requests_body(period[0], period[1], dimensions, report_class)
                hist_req = QueryData(hist_req_data, 'historical', profile, setting, report_class)
                # not checking campaigns len because gcm_dv360 pulls all advertiser's data
                if not does_hist_log_exist(hist_req):
                    hist_requests.append(hist_req)
        else:
            dimensions = get_setting_dimensions(setting)
            two_years_ago = dates['TWO_YEARS_AGO']
            if historical_range_mock:
                logging.info('historical_range_mock is set. Using it')
                two_years_ago = dates['TODAY'] - datetime.timedelta(historical_range_mock)
            # chunk historical queries: 2 years on 4 parts
            historical_periods = split_date_range_period(date_from=two_years_ago.strftime('%Y-%m-%d'),
                                                         date_to=dates['TODAY'].strftime('%Y-%m-%d'),
                                                         period_days=185)
            for period in historical_periods:
                hist_req_data = get_hist_requests_body(period[0], period[1], dimensions, report_class)
                hist_req = QueryData(hist_req_data, 'historical', profile, setting, report_class)
                if len(hist_req.setting['campaigns']) != 0 and not does_hist_log_exist(hist_req):
                    hist_requests.append(hist_req)
    return hist_requests


@set_config(state)
@set_execution_dates(dates)
def get_google_queries(profiles, **kwargs):
    queries = []
    hist_queries = []
    historical_range_mock = state['dag_run'].conf.get('historical_range_in_days')
    for profile in profiles:
        settings = profile['advertisers']
        standard_queries = get_standard_query(profile, settings, **kwargs)
        if standard_queries:
            queries += standard_queries
        if kwargs['dag_run'].conf.get('historical_only') or kwargs['dag_run'].conf.get('force_historical_repull'):
            for setting in settings:
                historical_queries = get_historical_query(profile, setting, historical_range_mock)
                if historical_queries is not None:
                    hist_queries += historical_queries
    logging.info(f"The numer of Standard queries {len(queries)}")
    hist_queries = optimize_hist_requests(list(map(lambda x: x.to_dict(), hist_queries)))
    logging.info(f"The numer of historical queries {len(hist_queries)}")
    dict_queries = list(map(lambda x: x.to_dict(), queries)) + hist_queries
    dict_queries = filter_out_standard_queries(dict_queries, **kwargs)
    logging.info(f'Going to process {len(dict_queries)} queries')
    return dict_queries


def create_report(url, data, report_type, token_creds, company_id, report_class, **kwargs):
    response = send_google_request(url, 'POST', token_creds, report_class, data,
                                   report_type=report_type, company_id=company_id, **kwargs)
    if response.status_code != 200:
        logging.info(response.text)
    report_id = response.json()['id']
    return report_id


def create_file(url, report_type, token_creds, company_id, report_class, **kwargs):
    response = send_google_request(url, 'POST', token_creds, report_class,
                                   report_type=report_type, company_id=company_id, **kwargs)
    file_id = response.json()['id']
    return file_id


def run_query(query, **kwargs):
    logging.debug('start load historical query')
    report_url = GOOGLE_REPORTS_URL_FORMAT.format(user_id=query.profile['profile_id'])
    token_creds = {'client_id': query.client_id,
                   'client_secret': query.client_secret,
                   'refresh_token': query.refresh_token}
    report_id = create_report(report_url, query.data, query.type, token_creds, query.profile.get('company_id'),
                              query.report_class, **kwargs)
    logging.debug(f'report_id is {report_id}')
    file_id = create_file(GOOGLE_RUN_QUERY_URL_FORMAT.format(user_id=query.profile['profile_id'],
                                                             report_id=report_id,),
                          query.type,
                          token_creds,
                          query.profile.get('company_id'),
                          query.report_class, **kwargs)
    logging.debug(f'file_id is {file_id}')
    return file_id, report_id


def run_single_report(query_data, **kwargs):
    query = QueryData(**query_data)
    try:
        file_id, report_id = run_query(query, **kwargs)
    except Exception:
        logging.critical(f'Error processing query {query}')
        raise
    return [file_id, report_id, query_data, query.type]


@set_execution_dates(dates)
def run_reports(queries, **kwargs):
    result = []
    errors = {"historical": 0,
              "standard": 0}

    for query_data in queries:
        try:
            query_result = run_single_report(query_data, **kwargs)
            result.append(query_result)
        except Exception as e:
            errors.setdefault(query_data['type'], 0)
            errors[query_data['type']] += 1
            error_message = f'had an error {e} on {json.dumps(query_data)} item \n' + traceback.format_exc()
            logging.error(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x["type"] == error_type, queries)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')
    return result


def get_file_download_url(file_url, report_type, token_creds, company_id, report_class, **kwargs):
    for _ in range(100):
        response = send_google_request(file_url, 'GET', token_creds, report_type=report_type, company_id=company_id, report_class=report_class, **kwargs)
        json_response = response.json()
        status = json_response['status']
        if status == 'REPORT_AVAILABLE':
            return json_response['urls']['apiUrl']
        if status == 'FAILED':
            break
        time.sleep(env_resolver.gcm2_wait_sleep * (5 if report_type == 'historical' else 3))
    message = (f'Can\'t load file for file_url={file_url}, '
               f'\nreport failed to get in REPORT_AVAILABLE or FAILED statuses, '
               f'it remained in {status} status  for too long')
    logging.error(message)
    raise Exception(message)


def download_report_classic(url, report_type, token_creds, company_id, report_class, **kwargs):
    
    def fix_report(df: DataFrame) -> DataFrame:
        metric_cols = ['total_conversions', 'impressions', 'clicks', 'rich_media_video_completions',
                       'active_view_viewable_impressions', 'click_through_conversions',
                       'view_through_conversions', 'rich_media_video_plays',
                       'rich_media_video_first_quartile_completes', 'rich_media_video_midpoints',
                       'rich_media_video_third_quartile_completes', 'active_view_measurable_impressions']
        fillna_columns = ['advertiser_id', 'floodlight_config_id', 'activity_id', 'campaign_id', 'site_id',
                          'total_conversions', 'impressions', 'clicks', 'rich_media_video_completions',
                          'active_view_viewable_impressions', 'rich_media_video_plays',
                          'rich_media_video_first_quartile_completes', 'rich_media_video_midpoints',
                          'rich_media_video_third_quartile_completes', 'active_view_measurable_impressions']

        df.columns = ['activity_id', 'campaign_id', 'site_id', 'date', 'advertiser_id', 'floodlight_config_id',
                      'activity_name', 'campaign_name', 'site_name', 'placement_id'] + metric_cols

        df[metric_cols] = df[metric_cols].replace(-1, 0)
        df[fillna_columns] = df[fillna_columns].fillna(0)
        df['activity_id'] = df['activity_id'].replace('(not set)', 0)
        return df
    
    report_response = send_google_request(url, 'GET', token_creds, report_type=report_type,
                                          company_id=company_id, report_class=report_class, **kwargs)
    # Translations from Italian is only for profile_id=6164896 (as of Nov 2022)
    text = report_response.text.split('Report Fields')[1:][0].split('Grand Total')[0].strip() \
        .replace('Activity ID', 'activity_id', 1) \
        .replace('Campaign ID', 'campaign_id', 1) \
        .replace('Site ID \\(DCM\\)', 'site_id', 1) \
        .replace('Date', 'date', 1) \
        .replace('Placement ID', 'placement_id', 1) \
        .replace('Advertiser ID', 'advertiser_id', 1) \
        .replace('Floodlight Configuration', 'floodlight_config_id', 1) \
        .replace('Activity', 'activity_name', 1) \
        .replace('Campaign', 'campaign_name', 1) \
        .replace('Site \\(DCM\\)', 'site_name', 1) \
        .replace('Total Conversions', 'total_conversions', 1) \
        .replace('Clicks', 'clicks', 1) \
        .replace('ID attività', 'activity_id', 1) \
        .replace('ID campagna', 'campaign_id', 1) \
        .replace('ID sito \\(DCM\\)', 'site_id', 1) \
        .replace('Data', 'date', 1) \
        .replace('ID inserzionista', 'advertiser_id', 1) \
        .replace('Configurazione Floodlight', 'floodlight_config_id', 1) \
        .replace('Attività', 'activity_name', 1) \
        .replace('Campagna', 'campaign_name', 1) \
        .replace('Sito \\(DCM\\)', 'site_name', 1) \
        .replace('Conversioni totali', 'total_conversions', 1) \
        .replace('Impressioni', 'impressions', 1) \
        .replace('Clic,', 'clicks,', 1) \
        .replace('ID posizionamento', 'placement_id', 1) \
        .replace('Site ID \\(CM360\\)', 'site_id', 1) \
        .replace('Site \\(CM360\\)', 'site_name', 1) \
        .replace('ID sito \\(CM360\\)', 'site_id', 1) \
        .replace('Visualizzazioni complete di un video', 'rich_media_video_completions', 1) \
        .replace('Visualizzazione attiva: impressioni visibili', 'active_view_viewable_impressions', 1) \
        .replace('Video Completions', 'rich_media_video_completions', 1) \
        .replace('Active View: Viewable Impressions', 'active_view_viewable_impressions', 1) \
        .replace('Sito \\(CM360\\)', 'site_name', 1) \
        .replace("'", ' ', 1) \
        .replace("Click-through Conversions", 'click_through_conversions', 1) \
        .replace("View-through Conversions", 'view_through_conversions', 1) \
        .replace("Conversioni clickthrough", 'click_through_conversions', 1) \
        .replace("Conversioni view-through", 'view_through_conversions', 1) \
        .replace("Video Plays", 'rich_media_video_plays', 1) \
        .replace("Video First Quartile Completions", 'rich_media_video_first_quartile_completes', 1) \
        .replace("Video Midpoints", 'rich_media_video_midpoints', 1) \
        .replace("Video Third Quartile Completions", 'rich_media_video_third_quartile_completes', 1) \
        .replace('Active View: Measurable Impressions', 'active_view_measurable_impressions', 1)

    df = pd.read_csv(io.StringIO(text), sep=",")
    df = fix_report(df)
    return df


def download_report_floodlight(url, report_type, token_creds, company_id, report_class, **kwargs):
    report_response = send_google_request(url, 'GET', token_creds, report_type=report_type,
                                          company_id=company_id, report_class=report_class, **kwargs)
    text = report_response.text.split('Report Fields')[1:][0]
    if len(text) > 1:
        text = text.split('Grand Total')[0].strip()
    else:
        text = report_response.text.split('Campi del rapporto')[1:][0].split('Totale complessivo')[0].strip()

    text = text.replace('Date', 'date', 1) \
        .replace('Data', 'date', 1) \
        .replace('Activity ID', 'activity_id', 1) \
        .replace('Campaign ID', 'campaign_id', 1) \
        .replace('ID attività', 'activity_id', 1) \
        .replace('ID campagna', 'campaign_id', 1) \
        .replace('Site ID (DCM)', 'site_id', 1) \
        .replace('Site (DCM)', 'site_name', 1) \
        .replace('ID sito (DCM)', 'site_id', 1) \
        .replace('Sito (DCM)', 'site_name', 1) \
        .replace('Site ID (CM360)', 'site_id', 1) \
        .replace('Site (CM360)', 'site_name', 1) \
        .replace('ID sito (CM360)', 'site_id', 1) \
        .replace('Sito (CM360)', 'site_name', 1) \
        .replace('Site ID \\(DCM\\)', 'site_id', 1) \
        .replace('Site \\(DCM\\)', 'site_name', 1) \
        .replace('ID sito \\(DCM\\)', 'site_id', 1) \
        .replace('Sito \\(DCM\\)', 'site_name', 1) \
        .replace('Site ID \\(CM360\\)', 'site_id', 1) \
        .replace('Site \\(CM360\\)', 'site_name', 1) \
        .replace('ID sito \\(CM360\\)', 'site_id', 1) \
        .replace('Sito \\(CM360\\)', 'site_name', 1) \
        .replace('Placement ID', 'placement_id', 1) \
        .replace('ID posizionamento', 'placement_id', 1) \
        .replace('Advertiser ID', 'advertiser_id', 1) \
        .replace('Floodlight Configuration', 'floodlight_config_id', 1) \
        .replace('Activity', 'activity_name', 1) \
        .replace('ID inserzionista', 'advertiser_id', 1) \
        .replace('Configurazione Floodlight', 'floodlight_config_id', 1) \
        .replace('Attività', 'activity_name', 1) \
        .replace('Campagna', 'campaign_name', 1) \
        .replace('Campaign', 'campaign_name', 1) \
        .replace('Total Conversions', 'total_conversions', 1) \
        .replace('Conversioni totali', 'total_conversions', 1) \
        .replace('Entrate totali', 'total_conversions_revenue', 1) \
        .replace('Total Revenue', 'total_conversions_revenue', 1) \
        .replace('Conteggio transazioni', 'transaction_count', 1) \
        .replace('Transaction Count', 'transaction_count', 1) \
        .replace("'", ' ', 1)
    df = pd.read_csv(io.StringIO(text), sep=",")
    df = df.replace('(not set)', None)

    return df


def download_report_paid_search(url, report_type, token_creds, company_id, report_class, **kwargs):
    report_response = send_google_request(url, 'GET', token_creds, report_type=report_type,
                                          company_id=company_id, report_class=report_class, **kwargs)
    text = report_response.text \
        .replace('Campi del rapporto', 'Report Fields') \
        .replace('Totale complessivo', 'Grand Total') \
        .split('Report Fields')[1].split('Grand Total')[0].strip()
    df = pd.read_csv(io.StringIO(text), sep=",")
    df = df.replace('(not set)', None)
    df.columns = ['date', 'activity_id', 'paid_search_external_campaign_id',
                  'paid_search_external_ad_group_id', 'paid_search_external_ad_id',
                  'placement_id', 'paid_search_actions',
                  'paid_search_transactions', 'paid_search_revenue']
    return df


def download_report_dv360(url, report_type, token_creds, company_id, report_class, **kwargs):
    report_response = send_google_request(url, "GET", token_creds, report_type=report_type,
                                          company_id=company_id, report_class=report_class, **kwargs)
    try:
        text = (
            report_response.text.replace('Campi del rapporto', 'Report Fields')
            .replace('Totale complessivo', 'Grand Total')
            .split("Report Fields")[1:][0]
            .split("Grand Total")[0]
            .strip()
            .replace("Activity ID", "activity_id")
            .replace("DV360 Advertiser ID", "dv360_advertiser_id")
            .replace("DV360 Campaign ID", "dv360_campaign_id")
            .replace("Date", "date")
            .replace("Placement ID", "placement_id")
            .replace("Advertiser ID", "advertiser_id")
            .replace("DV360 Insertion Order ID", "dv360_insertion_order_id")
            .replace("DV360 Line Item ID", "dv360_line_item_id")
            .replace("Total Conversions", "total_conversions")
            .replace("Total Revenue", "total_revenue")
            .replace("ID attività", "activity_id")
            .replace("Data", "date")
            .replace("ID inserzionista", "advertiser_id")
            .replace("Conversioni totali", "total_conversions")
            .replace("ID posizionamento", "placement_id")
            .replace("Visualizzazioni complete di un video", "rich_media_video_completions")
            .replace(
                "Visualizzazione attiva: impressioni visibili",
                "active_view_viewable_impressions",
            )
            .replace("Video Completions", "rich_media_video_completions")
            .replace(
                "Active View: Viewable Impressions", "active_view_viewable_impressions"
            )
            .replace("'", " ")
            .replace("Click-through Conversions", "click_through_conversions")
            .replace("View-through Conversions", "view_through_conversions")
            .replace("Video Plays", 'rich_media_video_plays', 1)
            .replace("Video First Quartile Completions", 'rich_media_video_first_quartile_completes', 1)
            .replace("Video Midpoints", 'rich_media_video_midpoints', 1)
            .replace("Video Third Quartile Completions", 'rich_media_video_third_quartile_completes', 1)
            .replace('Active View: Measurable Impressions', 'active_view_measurable_impressions', 1)
        )
        df = pd.read_csv(io.StringIO(text), sep=",")
        df = df.replace("(not set)", None)
        df.columns = [
            "activity_id",
            "advertiser_id",
            "date",
            "placement_id",
            "dv360_advertiser_id",
            "dv360_campaign_id",
            "dv360_insertion_order_id",
            "dv360_line_item_id",
            "total_conversions",
            "active_view_viewable_impressions",
            "rich_media_video_completions",
            "total_revenue",
            "click_through_conversions",
            "view_through_conversions",
            "rich_media_video_plays",
            "rich_media_video_first_quartile_completes",
            "rich_media_video_midpoints",
            "rich_media_video_third_quartile_completes",
            'active_view_measurable_impressions'
        ]
        df['activity_id'] = df['activity_id'].fillna(0)
        return df
    except IndexError as e:
        logging.error('encounted unexpected error while processing report!!!')
        logging.error(f'got unexpected response {report_response.text}')
        raise IndexError


def put_report_to_db_classic(report: DataFrame):
    columns_order = ['date', 'advertiser_id', 'placement_id', 'floodlight_config_id', 'activity_id',
                     'activity_name',
                     'campaign_id', 'campaign_name', 'site_id', 'site_name', 'total_conversions',
                     'impressions', 'clicks',
                     'rich_media_video_completions', 'active_view_viewable_impressions',
                     'click_through_conversions', 'view_through_conversions', 'rich_media_video_plays',
                     'rich_media_video_first_quartile_completes', 'rich_media_video_midpoints',
                     'rich_media_video_third_quartile_completes', 'active_view_measurable_impressions']
    report = report[columns_order]
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    for i, batch_rows in enumerate(iter_batch(fixed_rows, 5000)):
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        query = f"""INSERT INTO {REPORT_TABLE_NAMES['gcm_classic']} ({columns})
                    VALUES
                    {values_str} ON CONFLICT (date, activity_id, placement_id, campaign_id,
                                              site_id, advertiser_id, floodlight_config_id)
                    DO UPDATE
                    SET activity_name = Excluded.activity_name, campaign_name=Excluded.campaign_name,
                        site_name=Excluded.site_name, total_conversions=Excluded.total_conversions,
                        impressions=Excluded.impressions, clicks=Excluded.clicks,
                        rich_media_video_completions=Excluded.rich_media_video_completions,
                        active_view_viewable_impressions=Excluded.active_view_viewable_impressions,
                        click_through_conversions=Excluded.click_through_conversions,
                        view_through_conversions=Excluded.view_through_conversions,
                        rich_media_video_plays=Excluded.rich_media_video_plays,
                        rich_media_video_first_quartile_completes=Excluded.rich_media_video_first_quartile_completes,
                        rich_media_video_midpoints=Excluded.rich_media_video_midpoints,
                        rich_media_video_third_quartile_completes=Excluded.rich_media_video_third_quartile_completes,
                        active_view_measurable_impressions=Excluded.active_view_measurable_impressions;
              """
        pg_hook.run(query, True)


def put_report_to_db_paid_search(report: DataFrame):
    report = report[report['paid_search_external_campaign_id'].notnull()]
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    for i, batch_row in enumerate(iter_batch(fixed_rows, 5000)):
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_row))
        query = f"""INSERT INTO {REPORT_TABLE_NAMES['gcm_paid_search']} ({columns})
                    VALUES
                    {values_str} ON CONFLICT (date,activity_id,paid_search_external_campaign_id,
                                              paid_search_external_ad_group_id,paid_search_external_ad_id,
                                              placement_id)
                    DO UPDATE 
                    SET paid_search_actions=Excluded.paid_search_actions,
                        paid_search_revenue=Excluded.paid_search_revenue,
                        paid_search_transactions=Excluded.paid_search_transactions;
              """
        pg_hook.run(query, True)


def put_report_to_db_floodlight(report: DataFrame):
    columns_order = ['date', 'floodlight_config_id', 'advertiser_id', 'placement_id', 'activity_id',
                     'activity_name', 'campaign_id', 'campaign_name', 'site_id', 'site_name',
                     'total_conversions_revenue', 'transaction_count', 'total_conversions']
    report = report[columns_order]
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    for i, batch_row in enumerate(iter_batch(fixed_rows, 5000)):
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_row))
        query = f"""
        INSERT INTO {REPORT_TABLE_NAMES['gcm_floodlight']}({columns}) VALUES {values_str} 
        ON CONFLICT(date, activity_id, campaign_id, site_id, placement_id, advertiser_id, floodlight_config_id) 
        DO UPDATE SET activity_name = Excluded.activity_name, campaign_name = Excluded.campaign_name, 
        site_name = Excluded.site_name, total_conversions = Excluded.total_conversions,
         total_conversions_revenue = Excluded.total_conversions_revenue, transaction_count = Excluded.transaction_count; """

        pg_hook.run(query, True)


def put_report_to_db_dv360(report: DataFrame):
    columns = ", ".join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    for i, batch_row in enumerate(iter_batch(fixed_rows, 5000)):
        values_str = ", ".join(map(lambda x: f'({", ".join(x)})', batch_row))
        query = f"""
                    INSERT INTO {REPORT_TABLE_NAMES['gcm_dv360']} ({columns}) 
                    VALUES {values_str}
                    ON CONFLICT (date, activity_id, placement_id, dv360_advertiser_id,
                    dv360_campaign_id, advertiser_id, dv360_insertion_order_id,
                    dv360_line_item_id) 
                    DO UPDATE SET  total_conversions=Excluded.total_conversions,total_revenue=Excluded.total_revenue,
                    rich_media_video_completions=Excluded.rich_media_video_completions,
                    active_view_viewable_impressions=Excluded.active_view_viewable_impressions,
                    click_through_conversions=Excluded.click_through_conversions,
                    view_through_conversions=Excluded.view_through_conversions,
                    rich_media_video_plays=Excluded.rich_media_video_plays,
                    rich_media_video_first_quartile_completes=Excluded.rich_media_video_first_quartile_completes,
                    rich_media_video_midpoints=Excluded.rich_media_video_midpoints,
                    rich_media_video_third_quartile_completes=Excluded.rich_media_video_third_quartile_completes,
                    active_view_measurable_impressions=Excluded.active_view_measurable_impressions;
              """
        pg_hook.run(query, True)


def load_single_report(file_id, report_id, query_data, report_type, **kwargs):
    logging.info(f"load_reports {file_id} {report_id} {report_type} {query_data}")
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    query = QueryData(**query_data)
    file_url_endpoint = GOOGLE_FILE_URL_FORMAT.format(user_id=query.profile['profile_id'],
                                                      report_id=report_id,
                                                      file_id=file_id)
    logging.debug('url ' + file_url_endpoint)
    company_id = query.profile.get('company_id')
    token_creds = {'client_id': query.client_id,
                   'client_secret': query.client_secret,
                   'refresh_token': query.refresh_token}
    url = get_file_download_url(file_url_endpoint, report_type, token_creds, company_id, query.report_class, **kwargs)
    logging.debug('found file url')
    download_func_map = {
        'gcm_classic': download_report_classic,
        'gcm_floodlight': download_report_floodlight,
        'gcm_paid_search': download_report_paid_search,
        'gcm_dv360': download_report_dv360 
    }
    put_report_func_map = {
        'gcm_classic': put_report_to_db_classic,
        'gcm_floodlight': put_report_to_db_floodlight,
        'gcm_paid_search': put_report_to_db_paid_search,
        'gcm_dv360': put_report_to_db_dv360 
    }
    logging.info(f"report class is = {query.report_class} using {download_func_map[query.report_class].__str__()} and {put_report_func_map[query.report_class].__str__()}")
    report = download_func_map[query.report_class](url, report_type, token_creds, company_id, query.report_class, **kwargs)
    if not report.empty:
        put_report_func_map[query.report_class](report)
        increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                         [f"table:{REPORTS_TABLE_NAME}", f"client:{company_id}", f'run_number:{current_run_number}'])
        logging.debug('report is pushed')
    else:
        message = f'report {url} is empty with type {report_type}'
        logging.warning(message)
    if report_type == 'historical':
        log_hist_request(query)
        logging.debug('log is pushed')


def process_report(item, errors, **kwargs):
    file_id = item[0]
    report_id = item[1]
    start_time = time.time()
    thread_id = threading.get_ident()
    logging.info(f"Thread {thread_id} started for file_id {file_id} and report id {report_id} at {start_time}")
    try:
        load_single_report(*item, **kwargs)
    except Exception:
        errors.setdefault(item[3], 0)
        errors[item[3]] += 1
        error_message = f'had an error on {item} item \n' + traceback.format_exc()
        logging.error(error_message)
    end_time = time.time()
    logging.info(
        f"Thread {thread_id} completed for file_id {file_id} and report id {report_id} at {end_time}, duration: {end_time - start_time} seconds")


@set_execution_dates(dates)
def load_reports(batch, **kwargs):
    errors = {"historical": 0,
              "standard": 0}
    workers = kwargs['dag_run'].conf.get('parallelism')
    threads = 5
    if workers:
        threads = workers
    logging.info(f"The number of Threads are {threads}")
    with ThreadPoolExecutor(max_workers=threads) as executor:
        futures = {executor.submit(process_report, item, errors, **kwargs): item for item in batch}

        for future in as_completed(futures):
            item = futures[future]
            try:
                future.result()
            except Exception as e:
                error_message = f'Unexpected error on {item} item \n' + traceback.format_exc()
                logging.error(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x[3] == error_type, batch)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')


with DAG(
    FLOW_NAME,
    description="gcm main dag",
    schedule_interval=None,
    tags=['ingestion_flow'],
    params=DagConfigSetter(FLOW_NAME).get_params(),
    start_date=datetime.datetime(2022, 1, 1),
    catchup=False,
) as dag:
    t1 = PythonOperator(
        task_id="get_profiles",
        python_callable=get_profiles,
        on_failure_callback=failure_callback,
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id="get_google_queries",
        on_failure_callback=failure_callback,
        python_callable=get_google_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="run_reports",
        python_callable=run_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"queries": XComArg(t2)},
        executor_config=K8S_SPECS
    )

    t4 = PythonOperator(
        task_id="load_reports",
        python_callable=load_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"batch": XComArg(t3)},
        executor_config=K8S_SPECS
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> t4 >> cleanup_xcoms
