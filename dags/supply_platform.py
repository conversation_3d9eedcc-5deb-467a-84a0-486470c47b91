import datetime

import boto3
from airflow import DAG
from botocore.config import Config

from myn_airflow_lib.custom_operators import CustomGlueJobOperatorNoParallel
from myn_airflow_lib.resolvers import EnvironmentResolver

FLOW_NAME = 'SUPPLY_PLATFORM'
env_resolver = EnvironmentResolver()
config = dict(retries=dict(max_attempts=3))
glue_client_params = dict(region_name='eu-west-1', config=config)

level_1 = [
    "supply_platform"
]

level_2 = [
    "deliver_supply_platform_to_gcs"
]


with DAG(
        FLOW_NAME,
        schedule_interval='0 0 5 * *',
        start_date=datetime.datetime(2021, 1, 1),
        catchup=False,
        max_active_tasks=100
) as dag:
    level_operator_1 = CustomGlueJobOperatorNoParallel(
        task_id='supply_platform',
        env=env_resolver.environment,
        layer="extra",
        glue_client_params=glue_client_params,
        job_list=level_1,
    )

    level_operator_2 = CustomGlueJobOperatorNoParallel(
        task_id='deliver_supply_platform_to_gcs',
        env=env_resolver.environment,
        layer="extra",
        glue_client_params=glue_client_params,
        job_list=level_2,
    )
    level_operator_1 >> level_operator_2
