from airflow import DAG
import logging
from airflow.decorators import task
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import (
    KubernetesPodOperator,
)
import json
import os
from kubernetes.client import models as k8s

from myn_airflow_lib.resolvers import EnvironmentResolver
from airflow.models.param import Param

from lib.myn_airflow_lib.commons import failure_callback

resolver = EnvironmentResolver()

DEFAULT_TIMEZONE_GROUP = str(os.environ.get("TIMEZONE_GROUP", "1"))  # fallback default

APP_CONFIGS = {
    "aire4-core": {
        "namespace": os.getenv("AIRE4_CORE_NAMESPACE", "ds"),
        "service_account": os.getenv("AIRE4_CORE_SA", "ds"),
        "cpu_request": os.getenv("AIRE4_CORE_CPU_REQUEST", "2000m"),
        "cpu_limit": os.getenv("AIRE4_CORE_CPU_LIMIT", "2000m"),
        "memory_request_limit": os.getenv("AIRE4_CORE_MEMORY", "4Gi"),
        "configmap_app": os.getenv("AIRE4_CORE_CONFIGMAP", "aire4-core"),
        "node_selector": os.getenv("AIRE4_CORE_NODE_SELECTOR", "dag"),
    },
    "aire4-applicators": {
        "namespace": os.getenv("AIRE4_APPLICATORS", "ds"),
        "service_account": os.getenv("AIRE4_APPLICATORS_SA", "ds"),
        "cpu_request": os.getenv("AIRE4_APPLICATORS_CPU_REQUEST", "2000m"),
        "cpu_limit": os.getenv("AIRE4_APPLICATORS_CPU_LIMIT", "2000m"),
        "memory_request_limit": os.getenv("AIRE4_APPLICATORS_MEMORY", "4Gi"),
        "configmap_app": os.getenv("AIRE4_APPLICATORS_CONFIGMAP", "aire4-applicators"),
        "node_selector": os.getenv("AIRE4_APPLICATORS_NODE_SELECTOR", "dag"),
    },
}


def create_core_image() -> str:
    aire4_core_hash = str(os.environ.get("AIRE4_CORE_VER"))
    image = f"************.dkr.ecr.eu-west-1.amazonaws.com/aire4-core:{aire4_core_hash}"

    logging.info(f"Pulling the following images for the repos \n {str(image)}")
    return image


def create_applicators_image() -> str:
    aire4_applicators_hash = str(os.environ.get("AIRE4_APPLICATORS_VER"))
    image = f"************.dkr.ecr.eu-west-1.amazonaws.com/aire4-applicators:{aire4_applicators_hash}"

    logging.info(f"Pulling the following images for the repos \n {str(image)}")
    return image


with DAG(
    "k8s_aire4_jobs",
    schedule_interval=None,
    description="Dag to generate aire4 recommendation",
    catchup=False,
    on_failure_callback=failure_callback,
    tags=[
        "aire4-core",
        "aire4-applicators",
        "recommendations",
        "kafka-triggered",
    ],
    params={
        "TIMEZONE_OVERRIDE": Param(DEFAULT_TIMEZONE_GROUP, type="string"),
        "aire4_core_enabled": Param(True, type="boolean"),
        "aire4_applicators_enabled": Param(True, type="boolean"),
    },
) as dag:
    core_image = create_core_image()
    applicators_image = create_applicators_image()
    config_core = APP_CONFIGS["aire4-core"]
    config_applicators = APP_CONFIGS["aire4-applicators"]

    @task(task_id="get_timezone")
    def get_timezone_value(**context) -> str:
        """
        Retrieves the timezone from the DagRun conf.
        """
        dag_run = context["dag_run"]
        timezone = dag_run.conf.get("TIMEZONE_OVERRIDE")
        if timezone is None:
            timezone = dag_run.conf.get("current_run_number", DEFAULT_TIMEZONE_GROUP)

        return str(timezone)


    @task
    def get_payload(**context) -> dict:
        """
        Retrieves the payload from kafka.
        """
        dag_run = context.get("dag_run")
        if not dag_run:
            return {}

        # Look for 'kafka_payload' in dag_run.conf:
        raw_payload_str = dag_run.conf.get("kafka_payload", "{}")
        try:
            parsed = json.loads(raw_payload_str)
            parsed =parsed.get("conf")

        except Exception:
            parsed = {"raw_payload": raw_payload_str}

        return parsed

    @task
    def parse_recommendations_uri(**context) -> str:
        """
        Retrieves the s3 bucket recommendations URI from the core pod's XCom.
        """
        ti = context["ti"]
        recommendations_uri = ti.xcom_pull(task_ids="aire4_core_pod_task")

        return recommendations_uri

    @task
    def prepare_applicators_cmds(recommendations_list) -> list[list[str]]:
        """
        Prepare a list of commands for the aire4_applicators_pod tasks to execute.
        """
        return [
            [
                "sh", "-c", f"echo '{json.dumps(rec)}' > external_input/recommendation_uri.json && "
             "python3.12 main.py"
             ]
            for rec in recommendations_list
        ]


    @task(trigger_rule="all_done")
    def monitor_aire4_applicators_pod_status(**context) -> None:
        """
        Monitors the status of aire4_applicators_pod tasks and logs success and failure counts.
        """
        ti = context["ti"]
        dag_run = context["dag_run"]

        applicators_tasks = dag_run.get_task_instances()
        applicators_tasks = [
            t
            for t in applicators_tasks
            if t.task_id.startswith("aire4_applicators_pod_task")
        ]

        success_count = sum(1 for t in applicators_tasks if t.state == "success")
        failed_count = sum(1 for t in applicators_tasks if t.state == "failed")

        ti.log.info(
            f"Recommendation processing complete. Success: {success_count}, Failed: {failed_count}"
        )

        if failed_count > 0:
            ti.log.info(f"Warning: {failed_count} recommendations failed processing")

    timezone_task = get_timezone_value()

    core_pod_task = KubernetesPodOperator(
        task_id="aire4_core_pod_task",
        namespace=config_core["namespace"],
        name="aire4-core-pod",
        startup_timeout_seconds=300,
        image=core_image,
        labels={"app": "aire4-core"},
        service_account_name=config_core["service_account"],
        is_delete_operator_pod=True,
        in_cluster=True,
        image_pull_policy="Always",
        get_logs=True,
        do_xcom_push=True,
        retries=2,
        env_vars={
            "TIMEZONE_GROUP": "{{ task_instance.xcom_pull(task_ids='get_timezone') }}",
            "KAFKA_SERVERS": str(os.environ.get("KAFKA_SERVERS")),
            "ENVIRONMENT": str(resolver.environment),
        },
        env_from=[
            k8s.V1EnvFromSource(
                config_map_ref=k8s.V1ConfigMapEnvSource(name="infra")
            ),
            k8s.V1EnvFromSource(
                config_map_ref=k8s.V1ConfigMapEnvSource(
                    name=config_core["configmap_app"]
                )
            ),
        ],
        container_resources=k8s.V1ResourceRequirements(
            requests={
                "cpu": config_core["cpu_request"],
                "memory": config_core["memory_request_limit"],
            },
            limits={
                "cpu": config_core["cpu_limit"],
                "memory": config_core["memory_request_limit"],
            },
        ),
        node_selector={config_core["node_selector"]: "true"},
        tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key=config_core["node_selector"],
                operator="Equal",
                value="true",
            )
        ],
        cmds=[
            "sh",
            "-c",
            (
                "echo '{{ task_instance.xcom_pull(task_ids=\"get_payload\", key=\"return_value\") | tojson }}' > external_input/taxonomy.json && "
                "mkdir -p /airflow/xcom/ && python3.12 main.py > /airflow/xcom/return.json"
            ),
        ],
    )

    payload_task = get_payload()
    recommendations_task = parse_recommendations_uri()
    applicators_cmds_task = prepare_applicators_cmds(recommendations_task)

    applicators_pod_task = KubernetesPodOperator.partial(
        task_id="aire4_applicators_pod_task",
        name="aire4-applicators-pod",
        namespace=config_applicators["namespace"],
        startup_timeout_seconds=300,
        image=applicators_image,
        labels={"app": "aire4-applicators"},
        service_account_name=config_applicators["service_account"],
        is_delete_operator_pod=True,
        in_cluster=True,
        image_pull_policy="Always",
        get_logs=True,
        do_xcom_push=True,
        retries=2,
        env_vars={
            "KAFKA_SERVERS": str(os.environ.get("KAFKA_SERVERS")),
            "ENVIRONMENT": str(resolver.environment),
        },
        env_from=[
            k8s.V1EnvFromSource(
                config_map_ref=k8s.V1ConfigMapEnvSource(name="infra")
            ),
            k8s.V1EnvFromSource(
                config_map_ref=k8s.V1ConfigMapEnvSource(
                    name=config_applicators["configmap_app"]
                )
            ),
        ],
        container_resources=k8s.V1ResourceRequirements(
            requests={
                "cpu": config_applicators["cpu_request"],
                "memory": config_applicators["memory_request_limit"],
            },
            limits={
                "cpu": config_applicators["cpu_limit"],
                "memory": config_applicators["memory_request_limit"],
            },
        ),
        node_selector={config_applicators["node_selector"]: "true"},
        tolerations=[
            k8s.V1Toleration(
                effect="NoSchedule",
                key=config_applicators["node_selector"],
                operator="Equal",
                value="true",
            )
        ],
    ).expand(cmds=applicators_cmds_task)

    monitor_task = monitor_aire4_applicators_pod_status()

    timezone_task >> core_pod_task
    payload_task >> core_pod_task
    core_pod_task >> recommendations_task >> applicators_pod_task >> monitor_task
