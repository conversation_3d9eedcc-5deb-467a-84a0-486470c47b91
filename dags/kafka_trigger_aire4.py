from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from consumer.custom_kafka_sensor import CustomKafkaSensor
from airflow.operators.python import PythonOperator, ShortCircuitOperator
from airflow.operators.dummy import DummyOperator


TASK_ID = "wait_for_kafka_aire4_trigger_dag"
TOPIC = "aire4-dag-trigger"
KAFKA_CONN_ID = "kafka_default"
TRIGGER_DAG_ID = "k8s_aire4_jobs"


# Function to check if the Kafka sensor successfully received a message
def _should_continue(**context) -> bool:
    ti = context['ti']
    kafka_result = ti.xcom_pull(task_ids=TASK_ID, key="message")
    return kafka_result is not None and "value" in kafka_result


with DAG(
    dag_id="kafka_trigger_aire4",
    description="Trigger aire4 jobs DAG",
    start_date=datetime(2025, 6, 1, 0, 0, 0),
    schedule_interval=timedelta(seconds=30),
    catchup=False,
) as dag:

    # Poll Kafka once per DAG run, up to 1 record
    wait_for_kafka = CustomKafkaSensor(
        task_id=TASK_ID,
        kafka_conn_id=KAFKA_CONN_ID,
        topic=TOPIC,
        group_id="airflow-aire4-listener",
        timeout=27,
        soft_fail=True,
    )

    # This operator checks the result and decides whether to continue
    should_continue = ShortCircuitOperator(
        task_id="check_kafka_result",
        python_callable=_should_continue,
        provide_context=True,
    )

    # Decode raw bytes to string so we can pass just the payload
    def _decode_payload(**context) -> str:
        ti = context["ti"]
        record = ti.xcom_pull(task_ids=TASK_ID, key="message")

        if not record or "value" not in record:
            return ""

        raw_bytes = record["value"]

        try:
            return raw_bytes.decode("utf-8")
        except Exception:
            return str(raw_bytes)

    decode_kafka_payload = PythonOperator(
        task_id="decode_kafka_payload",
        python_callable=_decode_payload,
        provide_context=True,
    )

    trigger_k8s_aire4_jobs = TriggerDagRunOperator(
        task_id="trigger_k8s_aire4_jobs",
        trigger_dag_id=TRIGGER_DAG_ID,
        conf={
            "kafka_payload": "{{ ti.xcom_pull(task_ids='decode_kafka_payload') }}"
        }
    )

    # Add a dummy success task to ensure the DAG completes successfully
    success_task = DummyOperator(
        task_id="success",
        trigger_rule="none_failed_or_skipped"
    )

    wait_for_kafka >> should_continue >> decode_kafka_payload >> trigger_k8s_aire4_jobs >> success_task
    wait_for_kafka >> success_task
