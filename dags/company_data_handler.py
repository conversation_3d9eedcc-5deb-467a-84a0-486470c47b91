# DOCS - https://mint-ai.atlassian.net/wiki/spaces/TEAMJ/pages/**********/PRE+IMPLEMENTATION+DOC+COMPANY+ID+FOR+DATA+DELETION
# TASK - https://mint-ai.atlassian.net/browse/PDT-44287
import datetime
import pandas as pd
import logging
from pandas import DataFrame
from typing import NoReturn
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook

from myn_airflow_lib.commons import _get_db_value, add_mapp_columns
from myn_airflow_lib.commons import failure_callback

FLOW_NAME = 'COMPANY_DATA_MAPPING_FLOW'
WAIT_SLEEP = 100
REPORTS_TABLE_NAME = 'company_data_mapping'


pg_hook_reports = PostgresHook(postgres_conn_id='uri_reports')
pg_hook_bing = PostgresHook(postgres_conn_id='uri_bing')
pg_hook_adform = PostgresHook(postgres_conn_id='uri_adform')
pg_hook_adwords = PostgresHook(postgres_conn_id='uri_adwords')
pg_hook_appsflyer = PostgresHook(postgres_conn_id='uri_appsflyer')
pg_hook_dv360 = PostgresHook(postgres_conn_id='uri_dv360')
pg_hook_facebook_instagram = PostgresHook(postgres_conn_id='uri_facebook_instagram')
pg_hook_cm360 = PostgresHook(postgres_conn_id='uri_cm360')
pg_hook_zemanta = PostgresHook(postgres_conn_id='uri_zemanta')
pg_hook_ga4 = PostgresHook(postgres_conn_id='uri_ga4')
pg_hook_omni = PostgresHook(postgres_conn_id='uri_omni-connector')


data_mapping = dict(zemanta_mapping=dict(flow_name='ZEMANTA', entity_name='account_id', reports_table_name='reports_zemanta_performance_v2'),
                    gcm_mapping=dict(flow_name='CM360', entity_name='campaign_id', reports_table_name='campaign_manager_base_report'),
                    gcm_dv360_mapping=dict(flow_name='DV360', entity_name='advertiser_id', reports_table_name='campaign_manager_dv360_report'),
                    gcm_paid_search_mapping=dict(flow_name='CM360', entity_name='placement_id', reports_table_name='campaign_manager_paid_search_external_report'),
                    gcm_floodlight_mapping=dict(flow_name='CM360', entity_name='campaign_id', reports_table_name='campaign_manager_floodlight_base_report'),
                    fb_mapping=dict(flow_name='FACEBOOK_INSTAGRAM', entity_name='account_id', reports_table_name='social_campaigns_reports_v3'),
                    fb_conv_mapping=dict(flow_name='FACEBOOK_INSTAGRAM', entity_name='account_id', reports_table_name='meta_conversions_report'),
                    dv360_mapping=dict(flow_name='DV360', entity_name='advertiser_id', reports_table_name='dv360_base_reports'),
                    dv360_youtube_mapping=dict(flow_name='DV360', entity_name='advertiser_id', reports_table_name='dv360_base_reports_youtube'),
                    appsflyer_mapping=dict(flow_name='APPSFLYER', entity_name='app_id', reports_table_name='reports_appsflyer_performance_v2'),
                    adwords_ad_mapping=dict(flow_name='ADWORDS', entity_name='campaign_id', reports_table_name='reports_adwords_ad_performance'),
                    adwords_mob_mapping=dict(flow_name='ADWORDS', entity_name='campaign_id', reports_table_name='search_campaign_performance_mobile'),
                    adwords_perf_mapping=dict(flow_name='ADWORDS', entity_name='campaign_id', reports_table_name='search_campaign_performance'),
                    adwords_perf_conv_mapping=dict(flow_name='ADWORDS', entity_name='campaign_id', reports_table_name='search_campaign_conversion_performance'),
                    adwords_ad_conv_mapping=dict(flow_name='ADWORDS', entity_name='campaign_id', reports_table_name='reports_adwords_ad_conversion_performance'),
                    adform_mapping=dict(flow_name='ADFORM', entity_name='campaign_id', reports_table_name='reports_adformconversion_v2'),
                    bing_ad_mapping=dict(flow_name='BING', entity_name='campaign_id', reports_table_name='microsoft_advertising_performance_ad_level'),
                    bing_camp_mapping=dict(flow_name='BING', entity_name='campaign_id', reports_table_name='microsoft_advertising_estimated_campaign_level_v2'),
                    ga4_base_mapping=dict(flow_name='GOOGLE_ANALYTICS_v4', entity_name='property_id', reports_table_name='reports_ga4_base_performance_v2'),
                    ga4_autotag_mapping=dict(flow_name='GOOGLE_ANALYTICS_v4', entity_name='property_id', reports_table_name='reports_ga4_autotag_performance_v2'),
                    ga4_base_sessions_mapping=dict(flow_name='GOOGLE_ANALYTICS_v4', entity_name='property_id', reports_table_name='reports_ga4_base_sessions_performance_v2'),
                    ga4_autotag_sessions_mapping=dict(flow_name='GOOGLE_ANALYTICS_v4', entity_name='property_id', reports_table_name='reports_ga4_autotag_sessions_performance_v2'),
                    tiktok_mapping=dict(flow_name='TIKTOK', entity_name='campaign_id', reports_table_name='reports_tiktok_performance'),
                    linkedin_mapping=dict(flow_name='LINKEDIN', entity_name='campaign_id', reports_table_name='reports_linkedin_performance'),
                    amazon_mapping=dict(flow_name='AMAZON', entity_name='campaign_id', reports_table_name='reports_amazon_performance'),
                    xandr_mapping=dict(flow_name='XANDR', entity_name='insertion_order_id', reports_table_name='reports_xandr_performance_v3'))


def prep_zemanta_df():
    """Zemanta endpoint"""
    logging.info("Starting handling Zemanta endpoint")
    query = """SELECT zemanta_account_id AS entity_id, company_id
               FROM integrations
               GROUP BY zemanta_account_id, company_id"""
    df = pg_hook_zemanta.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('zemanta_mapping'))
    return result


def prep_gcm_df():
    """Google Campaign Manager endpoint: Base"""
    logging.info("Starting handling GCM endpoint")
    query = """SELECT cm.campaign AS entity_id, it.company_id
               FROM campaign_manager_campaignmanagercampaignmapping as cm
               JOIN campaign_manager_campaignmanagersettings as st ON cm.settings_id = st.id
               JOIN campaign_manager_campaignmanagerintegration as it ON st.integration_id = it.id
               GROUP BY cm.campaign, it.company_id"""
    df = pg_hook_cm360.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('gcm_mapping'))
    return result


def prep_gcm_paid_search_df():
    """Google Campaign Manager endpoint: GCM Paid Search"""
    logging.info("Starting handling GCM Paid Search endpoint")
    query = """SELECT pm.external_id AS entity_id, it.company_id
               FROM campaign_manager_placement as pm
               JOIN site_mapping_assignment as csa ON pm.site_assignment_id = csa.id
               JOIN site_mapping as sm ON csa.site_mapping_id = sm.id
               JOIN campaign_manager_campaignmanagercampaignmapping as cm ON csa.campaign_mapping_id = cm.id
               JOIN campaign_manager_campaignmanagersettings as st ON cm.settings_id = st.id
               JOIN campaign_manager_campaignmanagerintegration as it ON st.integration_id = it.id
               WHERE pm.external_id IS NOT NULL
               GROUP BY pm.external_id, it.company_id"""
    df = pg_hook_cm360.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('gcm_paid_search_mapping'))
    return result


def prep_gcm_floodlight_df():
    """Google Campaign Manager endpoint: Floodlight"""
    logging.info("Starting handling GCM Floodlight endpoint")
    query = """SELECT cm.campaign AS entity_id, it.company_id
               FROM campaign_manager_campaignmanagercampaignmapping as cm
               JOIN campaign_manager_campaignmanagersettings as st ON cm.settings_id = st.id
               JOIN campaign_manager_campaignmanagerintegration as it ON st.integration_id = it.id
               GROUP BY cm.campaign, it.company_id"""
    df = pg_hook_cm360.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('gcm_floodlight_mapping'))
    return result


def prep_dv360_df():
    """DV360 + DV360_YOUTUBE + GCM_DV360 endpoint"""
    logging.info("Starting handling DV360 and DV360_YOUTUBE and GCM_DV360 endpoints")
    query = """SELECT advertiser_id AS entity_id, company_id
               FROM dv360_dv360integration
               GROUP BY advertiser_id, company_id"""
    df = pg_hook_dv360.get_pandas_df(query)
    df_first = add_mapp_columns(df, data_mapping.get('gcm_dv360_mapping'))
    df_second = add_mapp_columns(df, data_mapping.get('dv360_mapping'))
    df_third = add_mapp_columns(df, data_mapping.get('dv360_youtube_mapping'))
    result = pd.concat([df_first, df_second, df_third], ignore_index=True)
    return result


def prep_appsflyer_df():
    """APPSFLYER endpoint"""
    logging.info("Starting handling APPSFLYER endpoint")
    query = """SELECT ios_id AS entity_id, company_id
               FROM integrations
               GROUP BY ios_id, company_id
               UNION
               SELECT android_id AS app_id, company_id
               FROM integrations
               GROUP BY android_id, company_id"""
    df = pg_hook_appsflyer.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('appsflyer_mapping'))
    return result


def prep_adwords_df():
    """Adwords endpoint"""
    logging.info("Starting handling Adwords endpoint")
    query = """SELECT cm.external_id AS entity_id, it.company_id
               FROM campaign_adwordssearchrelatedcampaign as cm
               JOIN campaign_adwordssearchcampaign as ps ON cm.parent_campaign_id = ps.id
               JOIN external_services_adwordsexternaldata as it ON ps.credentials_id = it.id
               GROUP BY cm.external_id, it.company_id"""
    df = pg_hook_adwords.get_pandas_df(query)
    df_first = add_mapp_columns(df, data_mapping.get('adwords_ad_mapping'))
    df_second = add_mapp_columns(df, data_mapping.get('adwords_mob_mapping'))
    df_third = add_mapp_columns(df, data_mapping.get('adwords_perf_mapping'))
    df_fourth = add_mapp_columns(df, data_mapping.get('adwords_perf_conv_mapping'))
    df_fifth = add_mapp_columns(df, data_mapping.get('adwords_ad_conv_mapping'))
    result = pd.concat([df_first, df_second, df_third, df_fourth, df_fifth], ignore_index=True)
    return result


def prep_adform_df():
    """Adform endpoint"""
    logging.info("Starting handling Adform endpoint")
    query = """SELECT cm.campaign AS entity_id, it.company_id
               FROM adform_adformcampaignmapping AS cm
               JOIN adform_adformsettings AS st ON cm.settings_id = st.id
               JOIN adform_adformintegration AS it ON st.integration_id = it.id
               GROUP BY cm.campaign, it.company_id"""
    df = pg_hook_adform.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('adform_mapping'))
    return result


def prep_bing_df():
    """Bing endpoint"""
    logging.info("Starting handling Bing endpoint")
    query = """SELECT cm.external_id AS entity_id, it.company_id
               FROM campaigns as cm
               JOIN platform_settings as ps ON cm.platform_settings_id = ps.tile_id
               JOIN integrations as it ON ps.integration_id = it.id
               GROUP BY cm.external_id, it.company_id"""
    df = pg_hook_bing.get_pandas_df(query)
    df_first = add_mapp_columns(df, data_mapping.get('bing_ad_mapping'))
    df_second = add_mapp_columns(df, data_mapping.get('bing_camp_mapping'))
    result = pd.concat([df_first, df_second], ignore_index=True)
    return result


def prep_fb_df():
    """Facebook endpoint"""
    logging.info("Starting handling Facebook endpoint")
    query = """SELECT REPLACE(account_id, 'act_', '') AS entity_id, company_id
               FROM account_facebookexternaldata
               GROUP BY account_id, company_id"""
    df = pg_hook_facebook_instagram.get_pandas_df(query)
    general_df = add_mapp_columns(df, data_mapping.get('fb_mapping'))
    convs_df = add_mapp_columns(df, data_mapping.get('fb_conv_mapping'))
    result = pd.concat([general_df, convs_df], ignore_index=True)
    return result


def prep_ga4_df():
    """Google Analytics v4 endpoint"""
    logging.info("Starting handling Google Analytics v4 endpoint")
    query = """SELECT property_id AS entity_id, company_id
               FROM integrations
               GROUP BY property_id, company_id"""
    df = pg_hook_ga4.get_pandas_df(query)
    df_first = add_mapp_columns(df, data_mapping.get('ga4_base_mapping'))
    df_second = add_mapp_columns(df, data_mapping.get('ga4_autotag_mapping'))
    df_third = add_mapp_columns(df, data_mapping.get('ga4_base_sessions_mapping'))
    df_fourth = add_mapp_columns(df, data_mapping.get('ga4_autotag_sessions_mapping'))
    result = pd.concat([df_first, df_second, df_third, df_fourth], ignore_index=True)
    return result


def prep_linkedin_df():
    """LinkedIn endpoint"""
    logging.info("Starting handling LinkedIn endpoint")
    query = """SELECT campaign.external_id AS entity_id, intgr.company_id
               FROM tile_settings ts
                LEFT JOIN integrations intgr ON intgr.id = ts.integration_id
                LEFT JOIN campaigns campaign ON campaign.tile_settings_id = ts.id
               WHERE intgr.platform = 'linkedin_integrated'
               GROUP BY campaign.external_id, intgr.company_id"""
    df = pg_hook_omni.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('linkedin_mapping'))
    return result


def prep_tiktok_df():
    """TikTok endpoint"""
    logging.info("Starting handling TikTok endpoint")
    query = """SELECT campaign.external_id AS entity_id, intgr.company_id
               FROM tile_settings ts
                LEFT JOIN integrations intgr ON intgr.id = ts.integration_id
                LEFT JOIN campaigns campaign ON campaign.tile_settings_id = ts.id
               WHERE intgr.platform = 'tiktok_integrated'
               GROUP BY campaign.external_id, intgr.company_id"""
    df = pg_hook_omni.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('tiktok_mapping'))
    return result


def prep_amazon_df():
    """Amazon endpoint"""
    logging.info("Starting handling Amazon endpoint")
    query = """SELECT campaign.external_id AS entity_id, intgr.company_id
               FROM tile_settings ts
                LEFT JOIN integrations intgr ON intgr.id = ts.integration_id
                LEFT JOIN campaigns campaign ON campaign.tile_settings_id = ts.id
               WHERE intgr.platform = 'amazon_integrated'
               GROUP BY campaign.external_id, intgr.company_id"""
    df = pg_hook_omni.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('amazon_mapping'))
    return result


def prep_xandr_df():
    """Xandr endpoint"""
    logging.info("Starting handling Xandr endpoint")
    query = """SELECT campaign.external_id AS entity_id, intgr.company_id
               FROM tile_settings ts
                LEFT JOIN integrations intgr ON intgr.id = ts.integration_id
                LEFT JOIN campaigns campaign ON campaign.tile_settings_id = ts.id
               WHERE intgr.platform = 'xandr_integrated'
               GROUP BY campaign.external_id, intgr.company_id"""
    df = pg_hook_omni.get_pandas_df(query)
    result = add_mapp_columns(df, data_mapping.get('xandr_mapping'))
    return result


def _execute_sql_query(report: DataFrame) -> NoReturn:
    """Prepare and runs sql INSERT query"""
    logging.info("Starting pushing to DB")

    columns = ', '.join(report.columns)

    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    query = f"""INSERT INTO {REPORTS_TABLE_NAME} ({columns})
                      VALUES {values_str} ON CONFLICT (company_id,flow_name,entity_id,entity_name,reports_table_name) 
                      DO NOTHING; """
    pg_hook_reports.run(query, True)


def process_data():
    """Preparing dataset and pushing to DB"""
    # prepare data
    function_list = [prep_zemanta_df, prep_gcm_df, prep_gcm_paid_search_df, prep_gcm_floodlight_df, prep_dv360_df,
                     prep_appsflyer_df, prep_adwords_df, prep_adform_df, prep_bing_df, prep_fb_df,
                     prep_ga4_df, prep_linkedin_df, prep_tiktok_df, prep_amazon_df, prep_xandr_df]
    dfs = [func() for func in function_list]
    dfs_filtered = [df for df in dfs if df is not None]
    final_df = pd.concat(dfs_filtered, ignore_index=True)
    final_df = final_df.drop_duplicates()
    final_df['entity_id'] = final_df['entity_id'].astype("string")

    # push data to DB
    _execute_sql_query(final_df)


with DAG(FLOW_NAME,
         description='company data mapping flow dag',
         schedule_interval="0 10 * * *",  # daily at 10:00 UTC
         start_date=datetime.datetime(2022, 1, 1),
         tags=['ingestion_flow'],
         catchup=False) as dag:
    t1 = PythonOperator(
        task_id='process_data',
        python_callable=process_data,
        on_failure_callback=failure_callback
    )
