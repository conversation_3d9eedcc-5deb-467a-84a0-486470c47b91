import datetime
import json
import logging
import threading
import time
from base64 import b64encode
from typing import List, Optional, <PERSON>ple
import urllib3
from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.snowflake.datasource_copy import ControllerRequest, SnowflakeSourceProcessor
from pydantic import parse_obj_as
import pytz
import requests
from airflow import DAG, AirflowException, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pydantic import BaseModel
from myn_airflow_lib.commons import failure_callback
from myn_airflow_lib.constants import IMPLY_EXTRAGLUE_PODS, IMPLY_FLOW_NAME, TRIGGER_DAG_NODES
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog
from concurrent.futures import ThreadPoolExecutor, as_completed
from kafka import KafkaProducer


urllib3.disable_warnings()
env_resolver = EnvironmentResolver()
env = env_resolver.environment
FLOW_NAME = IMPLY_FLOW_NAME
main_datasource_name = "main"
CONTENT_TYPE = "application/json"
druid_user = env_resolver.druid_user
druid_password = env_resolver.druid_password
druid_token = env_resolver.druid_token
druid_host = env_resolver.druid_host
druid_port = env_resolver.druid_port
druid_flow_host = env_resolver.druid_flow_host.strip("/")
druid_datasource_ingestion_url = "druid/indexer/v1/task"
druid_datasource_task_status_url = "druid/indexer/v1/task"
enterprise_customer_list_url = "process/enterprise-customers/configuration"
AWS_S3_BUCKET = env_resolver.s3_datalake_bucket
pg_hook = PostgresHook(postgres_conn_id='uri_druid_flow')
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
API_CALLS_RETRIES = 3
WAIT_BETWEEN_CALLS = 10

def push_metrics_to_datadog(**kwargs):
    init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
    execution_date = kwargs['dag_run'].start_date
    current_time = datetime.datetime.now(pytz.utc).astimezone(execution_date.tzinfo)
    delta_seconds = (current_time - execution_date).total_seconds()
    run_number = kwargs['dag_run'].conf.get('current_run_number')
    increment_metric('airflow_execution_time.increment', env_resolver.environment, f'flow_{FLOW_NAME}', delta_seconds,
            additional_tags=[f'run_number:{run_number}']
            )
    logging.info(f'run time is : {delta_seconds}')


class Column(BaseModel):
    id: int
    fieldtype: str
    name: str

class DataSource(BaseModel):
    columns: List[Column]
    name: str
    s3_buckets: List[str]

class PostWithBasicAuthenticationRequest(BaseModel):
    uri: str
    user: str
    password: str
    entity: str

class DruidFlowGetRequest(BaseModel):
    uri: str

class Dimension(BaseModel):
    name: str
    title: str
    formula: str
    dimension_type: str

class Measure(BaseModel):
    name: str
    title: str
    formula: str
    measure_type: str
    leading_value: str
    min_decimals: str
    max_decimals: str
    abbreviation_space: str
    missing_value_fill: str
    transform: str

class Customer(BaseModel):
    inner_name: str
    real_name: str
    client_id: str
    controller_id: Optional[str] = None

class ImplyCustomer(BaseModel):
    customer: Customer
    dimensions: List[Dimension]
    measures: List[Measure]
    dimensionsNonFinancial: List[Dimension]
    measuresNonFinancial: List[Measure]
    datasource: str
    columns: List[Column]

class CustomerDetails(BaseModel):
    imply_customer: List[ImplyCustomer]
    color: str
    datasource: str
    mode: str

class PostRequest(BaseModel):
    uri: str
    entity: str

class DataCubeCreateRestRequest(BaseModel):
    token: str
    name: str
    title: str
    client_id: str
    datasource: str

class DashboardCreateRestRequest(BaseModel):
    token: str
    name: str
    client_id: str
    title: str
    page_tile: str
    page_tile_name: str
    data_cube: str

def handle_request_errors(exception, flow_name: str):
    status_code = 503
    error_message = "Network issue"

    if exception.response is not None:
        error_message = exception.response.text
        status_code = exception.response.status_code
        
    logging.error(
        f"Error in Executing the HTTP Request, Status Code: ${status_code}, Error: ${error_message}"
    )
    raise AirflowException(f'Not all the {flow_name} processes succeeded')

def parse_custom_datasource_list(tuple_list) -> List[DataSource]:
    # Extract unique data source names and their corresponding s3 buckets
    unique_data_sources = {}
    for tup in tuple_list:
        name, s3_bucket, _, _ = tup
        if name not in unique_data_sources:
            unique_data_sources[name] = {s3_bucket}
        else:
            unique_data_sources[name].add(s3_bucket)
    
    def _replace_bucketfrom_env(bucket: str):
        parts = bucket.split('/')
        parts[2] = env_resolver.s3_datalake_bucket  # Replace the bucket name
        return '/'.join(parts)
    
    # Create DataSource objects
    data_sources = []
    for name, s3_buckets in unique_data_sources.items():
        columns = []
        s3_buckets = [_replace_bucketfrom_env(b) for b in s3_buckets]
        for tup in tuple_list:
            if tup[0] == name:
                _, _, fieldtype, col_name = tup
                column = Column(id = 0, fieldtype=fieldtype, name=col_name)
                columns.append(column)
        data_source = DataSource(columns=columns, name=update_name_with_env(name, env), s3_buckets=list(s3_buckets))
        data_sources.append(data_source)
    return data_sources

def get_color_resource_name(name: str, color: Optional[str]):
    if color is not None:
        return name + "_" + color
    else:
        return name

def update_name_with_env(name: str, env: str):
    if env == "prod":
        return name
    else:
        return name + "_" + env


def get(request: DruidFlowGetRequest):
    for retr in range(1,API_CALLS_RETRIES+1):
        logging.info(f'Current retry number {retr}')
        try:
            response = requests.get(url=request.uri)
            return response.json()
        except Exception as e:
            logging.error(e)
            time.sleep(WAIT_BETWEEN_CALLS)
        

def post(pr: PostRequest, flow_name: str):
    response = None
    attempt = 0
    max_retries = 2
    retry_delay = 1.0
    while attempt <= max_retries:
        try:
            logging.info(f"Request for the flow {flow_name} is {pr}")
            headers = {"Content-Type": CONTENT_TYPE}
            data_json = json.loads(pr.entity)
            response = requests.post(pr.uri, headers=headers, json=data_json)
            logging.info(f"Response for the flow {flow_name} is {response.text}")
            response.raise_for_status()  # Check for successful response (2xx status code)
            return response.json()
        except Exception as e:
            logging.info(f"Error in API request: {e} from {pr}")
            if response is not None and response.status_code == 500:
                return response.text
            attempt += 1
            if attempt > max_retries:
                handle_request_errors(e, flow_name)
            else:
                logging.warning(f"Retrying {flow_name} in {retry_delay} seconds...")
                time.sleep(retry_delay)


def switch_color(old_color: str) -> str:
    color = old_color
    if color == "green":
        return "blue"
    else:
        return "green"

def post_with_basic_authentication(
        request: PostWithBasicAuthenticationRequest,
):
    headers = {"Authorization": "Basic "
                         + b64encode(f"{request.user}:{request.password}".encode("ascii")).decode("ascii"),
        "Content-Type": CONTENT_TYPE
    }
    response = requests.request("POST", request.uri, headers=headers, data=request.entity, verify=False) # NOSONAR
    response.raise_for_status()
    return response.json()

def basic_authentication(uri: str, method: str):
    headers = {"Authorization": "Basic "
                         + b64encode(f"{druid_user}:{druid_password}".encode("ascii")).decode("ascii"),
        "Content-Type": CONTENT_TYPE
    }
    response = requests.request(method, uri, headers=headers, verify=False) #NOSONAR
    response.raise_for_status()
    if method == "DELETE":
        return response
    return response.json()

data_main_source_templates = '''
{{
    "type": "index_parallel",
    "context": {{
        "taskPriority": 100,  
        "forceTimeChunkLock": true,
        "useLineageBasedSegmentAllocation": true,
        "priority": 100
        }},
    "spec": {{
        "ioConfig": {{
            "type": "index_parallel",
            "inputSource": {{
                "type": "s3",
                "prefixes": [
                    "{}"
                ]
            }},
            "inputFormat": {{
                "type": "parquet"
            }}
        }},
        "tuningConfig": {{
            "type": "index_parallel",
            "awaitSegmentAvailabilityTimeoutMillis": 300000,
            "maxNumConcurrentSubTasks": 6,
            "partitionsSpec": {{
                "type": "dynamic"
            }}
        }},
        "dataSchema": {{
            "dataSource": "{}",
            "timestampSpec": {{
                "column": "date",
                "format": "auto"
            }},
            "dimensionsSpec": {{
                "dimensions": [{}]
            }},
            "transformSpec": {{}},
            "granularitySpec": {{
                "queryGranularity": "DAY",
                "rollup": false,
                "segmentGranularity": "YEAR"
            }}
        }}
    }}
}}
'''

data_source_templates = '''
{{
    "type": "index_parallel",
    "spec": {{
        "ioConfig": {{
            "type": "index_parallel",
            "inputSource": {{
                "type": "s3",
                "prefixes": [
                    "{}"
                ]
            }},
            "inputFormat": {{
                "type": "parquet"
            }}
        }},
        "tuningConfig": {{
            "type": "index_parallel",
            "awaitSegmentAvailabilityTimeoutMillis": 900000,
            "partitionsSpec": {{
                "type": "dynamic"
            }}
        }},
        "dataSchema": {{
            "dataSource": "{}",
            "timestampSpec": {{
                "column": "date",
                "format": "auto"
            }},
            "dimensionsSpec": {{
                "dimensions": [{}]
            }},
            "transformSpec": {{}},
            "granularitySpec": {{
                "queryGranularity": "day",
                "rollup": false,
                "segmentGranularity": "year"
            }}
        }}
    }}
}}
'''

def get_job_status(job_id: str) -> str:
    uri = f"{druid_host}:{druid_port}/druid/indexer/v1/task/{job_id}/status"
    task_status = basic_authentication(uri, "GET")
    return task_status["status"]["status"]

def s3_ingestion(
        uri: str,
        user: str,
        password: str,
        prefixes: List[str],
        datasource: str,
        columns: List[Column]
) -> PostWithBasicAuthenticationRequest:
    sources = ','.join('"' + source + '"' for source in prefixes)
    sources_normalized = sources[1:-1]
    columns_as_string = [
        '{"type":"double","name":"%s"}' % col.name if col.fieldtype == "double" else
        '{"type":"long","name":"%s"}' % col.name if col.fieldtype == "long" else
        '"%s"' % col.name
        for col in columns if col.name != "__time"
    ]
    
    body_template = data_main_source_templates if main_datasource_name in datasource else data_source_templates    
    request_body = body_template.format(sources_normalized, datasource, ','.join(columns_as_string))    
    
    return PostWithBasicAuthenticationRequest(uri=uri, user=user, password=password, entity=request_body)


def ingest(request: DataSource):
    uri = f"{druid_host}:{druid_port}/{druid_datasource_ingestion_url}"
    response: PostWithBasicAuthenticationRequest = s3_ingestion(uri, druid_user, druid_password,
                                      request.s3_buckets, request.name, request.columns)
    result = post_with_basic_authentication(response)
    return result, response.entity


def ingest_datasource(datasource: DataSource) -> Tuple[Tuple[str, str], str]:
    """
    Ingests a single datasource and returns the job name and ID.
    """
    logging.info(f"Ingesting the datasource {datasource.name}")
    job_id, template = ingest(datasource)
    time.sleep(2)
    return (datasource.name, job_id) , template



def poll_jobs(job_ids: List[Tuple[str, str]], num_jobs: int, **kwargs) -> bool:
    """
    Polls the status of jobs and updates the job_ids list accordingly.
    Returns True if any job failed.
    """
    start_time = time.time()
    fail_flag = False
    logging.info(f'Triggered {len(job_ids)} jobs out of {num_jobs}')
    run_number = kwargs['dag_run'].conf.get('current_run_number')
    while job_ids:
        logging.info(f'Polling jobs.. running {len(job_ids)}/{num_jobs}')
        time.sleep(80)
        for job_name, job_run_id in job_ids[:]:
            time.sleep(2)
            job_state = get_job_status(job_run_id)
            if job_state in ['SUCCESS', 'FAILED']:
                logging.info(f'Job {job_name} finished with the status: {job_state}')
                if job_state == "FAILED":
                    fail_flag = True
                    logging.error(f"Job: {job_name} failed")
                increment_metric("airflow_execution_time.increment", env, job_name,
                                 round(time.time() - start_time), additional_tags=[f"run_number:{run_number}"])
                increment_metric('airflow_imply_dag.increment', env, job_name,
                     additional_tags=[f"status:{job_state}", f"run_number:{run_number}"])
                job_ids.remove((job_name, job_run_id))
    return fail_flag

def delete_datasources(all_datasources: List[DataSource]) -> None:
    for datasource in all_datasources:
        # DataSources. mark as unused
        uri = f"{druid_host}:{druid_port}/druid/coordinator/v1/datasources/{datasource.name}"
        logging.info(f"URI for datasources mark unused {uri}")
        basic_authentication(uri, "DELETE")



def execute_datasources(all_datasources: List[DataSource], color: str, **kwargs) -> List:
    """
    Executes ingestion for all provided datasources.
    """
    job_ids_and_templates = [ingest_datasource(ds) for ds in all_datasources]
    data_source_templates = [item[1] for item in job_ids_and_templates]
    job_ids = [item[0] for item in job_ids_and_templates]
    
    # push metadata to xcom to be retrieved in the next task
    kwargs['ti'].xcom_push(key='job_ids', value=job_ids)
    kwargs['ti'].xcom_push(key='color', value=color)
    kwargs['ti'].xcom_push(key='ds_templates', value=data_source_templates)


def start_datasource_polling(**kwargs):
    if kwargs['dag_run'].conf.get('skip_ingestion'): 
        return
    job_ids = kwargs['ti'].xcom_pull(task_ids='prepare_datasources_metadada', key='job_ids')
    job_ids = [(item[0], item[1]['task']) for item in job_ids]
    color = kwargs['ti'].xcom_pull(task_ids='prepare_datasources_metadada', key='color')
    fail_flag = poll_jobs(job_ids, len(job_ids), **kwargs)
    if fail_flag:
        raise AirflowException('Not all datasources tasks Succeeded')
    else:
        # Changing the color in the database
        insert_sql = "INSERT INTO color_state_history (active_color, create_date) VALUES (%s, NOW())"
        pg_hook.run(insert_sql, parameters=(color,))


def prepare_datasources_metadada(**kwargs):
    """
    function for the first step
    to prepare metadata for all of the datasources we should ingest
    """
    if env is None:
        raise AirflowException('No environment variable exists')
    mode = kwargs['dag_run'].conf.get('ingestion_mode')
    if not mode:
        mode = 'deploy'

    logging.info(f"The Deploy mode is {mode}")

    # Reading the list of custom datacubes
    custom_datasources_sql = "SELECT custom_datasources.name AS custom_datasources_name, custom_datasources.s3_buckets AS s3_buckets, custom_columns.fieldType AS column_fieldType, custom_columns.name AS column_name FROM custom_datasources JOIN custom_datasource_columns ON custom_datasource_columns.custom_datasource_id = custom_datasources.id JOIN custom_columns ON custom_columns.id = custom_datasource_columns.column_id"


    custom_datasources_list = pg_hook.get_records(custom_datasources_sql)
    custom_datasources = parse_custom_datasource_list(custom_datasources_list)
    logging.info(f"Custom Datasources list {[i.name for i in custom_datasources]}")

    # Getting the Active Color
    active_color_sql = "select *  from color_state_history where id = (select MAX(id) from color_state_history)"
    active_color_sql_result = pg_hook.get_records(active_color_sql)
    active_color = active_color_sql_result[0][1]
    logging.info(f"Current Active Color is {active_color}")

    # Switch color
    color = switch_color(active_color)
    logging.info(f"Switching to a new color {color}")

    main_datasource = get_color_resource_name(main_datasource_name, update_name_with_env(color, env))
    logging.info(f"Main Datasource {main_datasource}")

    # Get the list of Enterprise Customers
    enterprise_customer_list = get(
        DruidFlowGetRequest(uri=f"{druid_flow_host}/{enterprise_customer_list_url}"))
    print(enterprise_customer_list)
    imply_enterprise_customers: list[ImplyCustomer] = [ImplyCustomer(**item) for item in
                                                    enterprise_customer_list]

    # Get the list of unique columns for the main datasource
    unique_column_names = set()
    columns: List[Column] = []
    for imply_customer in imply_enterprise_customers:
        for col in imply_customer.columns:
            if col.name not in unique_column_names:
                unique_column_names.add(col.name)
                columns.append(col)

    main_datasources: DataSource = DataSource(columns=columns, name=main_datasource, s3_buckets=[f"s3://{AWS_S3_BUCKET}/final/druid_v2/main"])

    sources_to_ingest = [main_datasources] + custom_datasources
    logging.info(f"The List of all the datasources {[d.name for d in custom_datasources]}")
    if mode == "recreate":
        logging.info("Triggering datasource delete")
        delete_datasources(sources_to_ingest)
    if kwargs['dag_run'].conf.get('skip_ingestion'):        
        color = active_color
        logging.info(f"Ingestion skip parameter found, skipping datasource creation and switchin color back to {color}, main_datasource_name is {main_datasource_name}")
        return (CustomerDetails(imply_customer=imply_enterprise_customers, color=color, datasource=main_datasource_name, mode=mode).dict())
    execute_datasources(sources_to_ingest, color, **kwargs)
    return (CustomerDetails(imply_customer=imply_enterprise_customers, color=color, datasource=main_datasource_name, mode=mode).dict())


def trigger_datacubes(customers_data, **kwargs):
    customer_details: CustomerDetails = parse_obj_as(CustomerDetails, customers_data)

    imply_enterprise_customers = customer_details.imply_customer
    color = customer_details.color
    datasource = customer_details.datasource + "_" + color
    logging.info(f"The Datasource is {datasource}")
    flow_name = "datacube"
    max_workers = 4
    run_number = kwargs['dag_run'].conf.get('current_run_number')
    clients_for_retry = {
        "financial": [],
        "non-financial": [],
    }

    # Initialize the execution report
    execution_report = {
        "financial": {},
        "non-financial": {},
    }

    def init_execution_report(customers, key):
        for customer in customers:
            execution_report[key].update({customer.customer.real_name: {"delete_response": {}, "create_response": {}}})

    # Initialize execution report for each type of customer
    init_execution_report(imply_enterprise_customers, "financial")
    init_execution_report(imply_enterprise_customers, "non-financial")

    def process_datacube(customer, data_type, create_suffix="", delete_suffix=""):
        # Delete datacube
        delete_data = {"name": customer.customer.inner_name + delete_suffix, "token": druid_token}
        delete_res = post(PostRequest(uri=f"{druid_flow_host}/datacubes/delete", entity=json.dumps(delete_data)), flow_name)
        execution_report[data_type][customer.customer.real_name]["delete_response"] = delete_res
        time.sleep(1.2)
        # Create datacube
        create_data = DataCubeCreateRestRequest(
            token=druid_token,
            name=customer.customer.inner_name + create_suffix,
            title=customer.customer.real_name + create_suffix,
            client_id=customer.customer.client_id,
            datasource=datasource,
        )
        create_url = f"{druid_flow_host}/datacubes/create/{data_type}"

        create_res = post(PostRequest(uri=create_url, entity=create_data.json()), flow_name)
        try:
            creation_status = create_res.get("status", "fail")            
        except AttributeError as err:
            creation_status = "fail"
            logging.info(f"Failed to get the creation status for customer ID: {customer.customer.client_id} because: {err} ")
        if creation_status == "fail":
                clients_for_retry[data_type].append(customer.customer.client_id)
        
        increment_metric('airflow_imply_dag.increment', env, flow_name,
                         additional_tags=[f"status:{creation_status}",
                                          f"company_id:{customer.customer.client_id}",
                                          f"datacube_name:{customer.customer.real_name + create_suffix}",
                                          f"run_number:{run_number}"])            
            
        execution_report[data_type][customer.customer.real_name]["create_response"] = create_res

    def process_customers(customers, data_type, create_suffix="", delete_suffix=""):
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(process_datacube, customer, data_type, create_suffix, delete_suffix) for customer in customers]
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as exc:
                    logging.error(f"Generated an exception: {exc}")

    # Process customers for each datacube type
    process_customers(imply_enterprise_customers, "financial")
    process_customers(imply_enterprise_customers, "non-financial", "-non-financial", "-non-financial")


    def retry_failed_cubes(clients_for_retry):
        for cube_type, failed_clients in clients_for_retry.items():
            if not failed_clients:
                continue  # Skip if no failed clients

            logging.info(f"We have failed to create {cube_type} cubes for these clients: {failed_clients}. Retrying them once more...")

            retry_customers = [
                customer for customer in imply_enterprise_customers
                if customer.customer.client_id in failed_clients
            ]

            clients_for_retry[cube_type] = []  # Reset failed list before retry

            if cube_type != "financial":
                process_customers(retry_customers, cube_type, "-non-financial", "-non-financial")
            else:
                process_customers(retry_customers, cube_type)

            if clients_for_retry[cube_type]:  # Check if retry still failed
                logging.error(f"FINAL FAILURE: These {cube_type} cubes could NOT be created even after retry: {clients_for_retry[cube_type]}")
    
    retry_failed_cubes(clients_for_retry)

    def analyze_report(report):
        def is_failed(details):
            response = details.get('create_response')
            return response is None or isinstance(response, str) or (isinstance(response, dict) and response.get('status') != 'success')

        failed_customers = [customer for customer, details in report.items() if is_failed(details)]
        total_count = len(report)
        success_count = total_count - len(failed_customers)
        
        return total_count, success_count, failed_customers



    for category in ["financial", "non-financial"]:
        datacubes = execution_report.get(category, {})
        total, success, failed_customers = analyze_report(datacubes)

        logging.info(f"\nProcess summary:\n{success}/{total} enterprise {category} datacubes created")
        
        if failed_customers:
            logging.info(f"List of clients with failed {category} datacubes: {failed_customers}")

    if kwargs["dag_run"].conf.get("detailed_logging"):
        logging.info(f"Execution Report JSON: {json.dumps(execution_report, indent=4)}")
        
    return customers_data


def trigger_dashboards(customers_data, **kwargs):
    customer_details: CustomerDetails = parse_obj_as(CustomerDetails, customers_data)
    imply_enterprise_customers = customer_details.imply_customer
    color = customer_details.color
    flow_name = "dashboards"
    max_workers = 4
    run_number = kwargs['dag_run'].conf.get('current_run_number')

    # Initialize the execution report
    execution_report = {
        "financial": {},
        "non-financial": {},
    }

    def init_execution_report(customers, key):
        for customer in customers:
            execution_report[key].update({customer.customer.real_name: {"delete_response": {}, "create_response": {}}})

    # Initialize execution report for each type of customer
    init_execution_report(imply_enterprise_customers, "financial")
    init_execution_report(imply_enterprise_customers, "non-financial")

    def process_dashboard(customer, data_type, create_suffix="", delete_suffix=""):
        # Delete dashboard
        delete_data = {"name": customer.customer.inner_name + delete_suffix, "token": druid_token}
        delete_res = post(PostRequest(uri=f"{druid_flow_host}/dashboards/delete", entity=json.dumps(delete_data)), flow_name)
        execution_report[data_type][customer.customer.real_name]["delete_response"] = delete_res
        
        # Create dashboard
        create_data = DashboardCreateRestRequest(
            token=druid_token,
            name=customer.customer.inner_name + create_suffix,
            client_id=customer.customer.client_id,
            title=customer.customer.real_name + create_suffix,
            page_tile=customer.customer.inner_name + create_suffix,
            page_tile_name=customer.customer.inner_name,
            data_cube=customer.customer.inner_name + create_suffix,
        )
        create_url = f"{druid_flow_host}/dashboards/create/{data_type}"

        create_res = post(PostRequest(uri=create_url, entity=create_data.json()), flow_name)
        try:
            creation_status = create_res.get("status", "fail")
        except AttributeError as err:
            creation_status = "fail"
            logging.info(f"Failed to get the creation status for customer ID: {customer.customer.client_id} because: {err} ")    

        increment_metric('airflow_imply_dag.increment', env, flow_name,
                         additional_tags=[f"status:{creation_status}",
                                          f"company_id:{customer.customer.client_id}",
                                          f"dashboard_name:{customer.customer.real_name + create_suffix}",
                                          f"run_number:{run_number}"])

        execution_report[data_type][customer.customer.real_name]["create_response"] = create_res

    def process_customers(customers, data_type, create_suffix="", delete_suffix=""):
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(process_dashboard, customer, data_type, create_suffix, delete_suffix) for customer in customers]
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as exc:
                    logging.error(f"Generated an exception: {exc}")

    # Process customers for each dashboard type
    process_customers(imply_enterprise_customers, "financial")
    process_customers(imply_enterprise_customers, "non-financial", "-non-financial", "-non-financial")

    def analyze_report(report):
        total_count = len(report)
        failed_creation = [customer for customer, details in report.items()
                   if details.get('create_response') is None 
                   or isinstance(details['create_response'], str)
                   or (isinstance(details['create_response'], dict) and details['create_response'].get('status') != 'success')]
        success_count = total_count - len(failed_creation)
        return total_count, success_count, failed_creation

    fin_datacubes = execution_report.get("financial", {})
    non_fin_datacubes = execution_report.get("non-financial", {})

    total_fin_count, success_fin_count, failed_fin_customers = analyze_report(fin_datacubes)
    total_non_fin_count, success_non_fin_count, failed_non_fin_customers = analyze_report(non_fin_datacubes)

    logging.info("\nProcess summary:\n"
                 f"{success_fin_count}/{total_fin_count} enterprise financial dashboards created\n"
                 f"{success_non_fin_count}/{total_non_fin_count} enterprise non-financial dashboards created\n")
    if failed_fin_customers:
        logging.info(f"List of clients with failed financial dashboards: {failed_fin_customers}")
    if failed_non_fin_customers:
        logging.info(f"List of clients with failed non-financial dashboards: {failed_non_fin_customers}")

    if kwargs['dag_run'].conf.get('detailed_logging'):
        logging.info(f"Execution Report JSON: {json.dumps(execution_report, indent=4)}")


def trigger_imply_roles(**kwargs):
    logging.info("Creating roles for all the imply customers")
    roles_res = post(PostRequest(uri=f"{druid_flow_host}/roles/create/all", entity=json.dumps({"token": druid_token})), "roles")
    logging.info(f"Roles Creation response {roles_res}")

    # push runtimer to datadog
    push_metrics_to_datadog(**kwargs)


def process_datasource(req):
    req = ControllerRequest(req)
    snowflake_proc = SnowflakeSourceProcessor(
        "druid_sources"
    )
    snowflake_proc.process_datasource(req)


def copy_to_snowflake(**kwargs):
    if kwargs['dag_run'].conf.get('skip_ingestion'):
        logging.info('skip ingestion key was found skipping ingestion to the snoflake...')
        return
    reqs = kwargs['ti'].xcom_pull(task_ids='prepare_datasources_metadada', key='ds_templates')
    reqs = [json.loads(r) for r in reqs]
    threads = []
    for req in reqs:
        thread = threading.Thread(target=process_datasource, args=(req,))
        threads.append(thread)
        thread.start()

    # Wait for all threads to finish
    for thread in threads:
        thread.join()

def send_kafka_completion_message(**kwargs):
    """
    Sends a completion message to Kafka after snowflake data updated
    """
    if kwargs['dag_run'].conf.get('skip_ingestion'):
        logging.info('skip ingestion key was found skipping sending Kafka message')
        return
    try:
        bootstrap_servers = [server.strip() for server in env_resolver.kafka_servers.split(',')]

        kafka_config = {
            'bootstrap_servers': bootstrap_servers,
            'retries': 4,
            'retry_backoff_ms': 1000,
            'key_serializer': lambda x: x.encode('utf-8') if x is not None else None,
            'value_serializer': lambda v: json.dumps(v).encode('utf-8')
        }

        producer = KafkaProducer(**kafka_config)

        current_time = datetime.datetime.now(pytz.utc)
        message_payload = {
            'dag_id': kwargs['dag'].dag_id,
            'run_id': kwargs['dag_run'].run_id,
            'completion_time': current_time.isoformat(),
            'environment': env,
            'status': 'completed',
        }

        topic_name = 'snowflake-update-status'

        producer.send(
            topic=topic_name,
            key=kwargs['dag_run'].run_id,
            value=message_payload
        )

        logging.info(f"Successfully sent completion message to Kafka topic '{topic_name}' ")
        producer.close()
    except Exception as e: #NOSONAR
        logging.error(f"Failed to send Kafka completion message: {str(e)}")
        return


with DAG(FLOW_NAME,
         description='imply flow dag',
         schedule_interval=None,
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False,
         params=DagConfigSetter(FLOW_NAME).get_params(),
         ) as dag:


    prepare_data_source_metadata = PythonOperator(
        task_id="prepare_datasources_metadada",
        on_failure_callback=failure_callback,
        python_callable=prepare_datasources_metadada,
        executor_config=IMPLY_EXTRAGLUE_PODS
    )

    run_datasource = PythonOperator(
        task_id="run_datasource",
        on_failure_callback=failure_callback,
        python_callable=start_datasource_polling,
        executor_config=IMPLY_EXTRAGLUE_PODS
    )

    snowflake_copy = PythonOperator(
        task_id="copy_data_source_to_snowflake",
        on_failure_callback=failure_callback,
        python_callable=copy_to_snowflake,
        executor_config=IMPLY_EXTRAGLUE_PODS
    )

    send_kafka_message = PythonOperator(
        task_id="send_kafka_completion_message",
        on_failure_callback=failure_callback,
        python_callable=send_kafka_completion_message,
        executor_config=IMPLY_EXTRAGLUE_PODS
    )

    run_datacubes = PythonOperator(
        task_id="run_datacubes",
        on_failure_callback=failure_callback,
        op_kwargs={"customers_data": XComArg(prepare_data_source_metadata)},
        python_callable=trigger_datacubes,
        executor_config=IMPLY_EXTRAGLUE_PODS
    )

    run_dashboards = PythonOperator(
        task_id="run_dashboards",
        on_failure_callback=failure_callback,
        op_kwargs={"customers_data": XComArg(prepare_data_source_metadata)},
        python_callable=trigger_dashboards,
        executor_config=IMPLY_EXTRAGLUE_PODS
    )

    run_imply_roles = PythonOperator(
        task_id="run_imply_roles",
        on_failure_callback=failure_callback,
        python_callable=trigger_imply_roles,
        executor_config=IMPLY_EXTRAGLUE_PODS
    )


    prepare_data_source_metadata >> [run_datasource, snowflake_copy]
    run_datasource >> run_datacubes  # run_datacubes depends only on run_datasource
    run_datacubes >> run_dashboards >> run_imply_roles
    snowflake_copy >> send_kafka_message
