import os
import datetime
import json
import logging
import time
import traceback
import requests
import pandas as pd

from typing import List, <PERSON><PERSON>
from io import String<PERSON>
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pandas import DataFrame
from datetime import date, timedelta

from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.constants import APSFLYER_API_STR, APSFLYER_ETL_ENDPOINT, EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE
from myn_airflow_lib.commons import (
    EtlEndpointProcessor, failure_callback, filter_out_standard_queries, set_execution_dates, _get_db_value,
    set_config, enable_hist_log_check_based_on_conf, get_date_to_execute_on, iter_batch
)
from myn_airflow_lib.exceptions import AuthError, Mint<PERSON><PERSON>Failed
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog

state = {
    'dates': {}
}
dates = state['dates']
pg_hook = PostgresHook(postgres_conn_id="uri_reports")
pg_hook.log.disabled = True
FLOW_NAME = "APPSFLYER"
REPORTS_TABLE_NAME = 'reports_appsflyer_performance_v2'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)


def split_to_monthly_date_range(date_from: date, date_to: date) -> List[Tuple[date, date]]:
    """Split year period to months"""
    monthly_date_ranges = []
    current_month_start = date_from
    current_month_end = current_month_start + timedelta(days=30)
    while current_month_end <= date_to:
        # Append the current monthly date_from and date_to to the list
        monthly_date_ranges.append((current_month_start, current_month_end))
        # Move to the next month
        current_month_start = current_month_end + timedelta(days=1)
        current_month_end = current_month_start + timedelta(days=30)
    # Append the last date_to if it's not complete
    if current_month_start <= date_to:
        monthly_date_ranges.append((current_month_start, date_to))
    return monthly_date_ranges


def get_profiles(**kwargs):
    logging.info(f"Environments are {json.dumps(dict(os.environ))}")
    config_mock = kwargs["dag_run"].conf.get("etl_endpoint_mock")
    if config_mock:
        profiles = config_mock
    else:
        processor = EtlEndpointProcessor(APSFLYER_ETL_ENDPOINT, **kwargs)
        profiles = processor.apply_all()
    items = profiles
    return items


def log_hist_request(company_id, campaign_details, campaign_type):
    sql_query = f"""
                insert into {HISTORICAL_LOG_TABLE}(company_id,campaign_type,campaign_detail)
                values({company_id},'{campaign_type}','{campaign_details}')
    """
    pg_hook.run(sql_query)


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(detail: str) -> bool:
    query = f"""
        select id from {HISTORICAL_LOG_TABLE}
        where
        campaign_type = '{FLOW_NAME}'
            and campaign_detail = '{detail}' limit 1;
    """
    df = pg_hook.get_pandas_df(query)
    return not df.empty


def _execute_sql_query(report: DataFrame):
    """Prepare and runs sql UPSERT query"""
    report = report.drop_duplicates()
    logging.info("Starting pushing to DB")
    logging.info(report.shape)
    columns = ", ".join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        query = f"""INSERT INTO {REPORTS_TABLE_NAME} ({columns})
                        VALUES
                        {values_str} ON CONFLICT (date, media_source, campaign, platform, activity_name, app_id)
                        DO UPDATE
                        set impressions=Excluded.impressions,
                        clicks=Excluded.clicks,
                        installs=Excluded.installs,
                        sessions=Excluded.sessions,
                        event_count=Excluded.event_count,
                        unique_users_count=Excluded.unique_users_count
                      """
        pg_hook.run(query, True)


def create_request(app_id: str, date_to: date, date_from: date, report_type="partners_by_date_report") -> str:

    request_str = APSFLYER_API_STR.format(report_type=report_type, app_id=app_id, date_to=date_to, date_from=date_from)
    return request_str


def send_request(request_str: str, headers: dict, report_type: str = None, company_id: int = None, **kwargs) -> requests.Response:
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    for _ in range(3):
        logging.info(request_str)
        response = requests.request("GET", request_str, headers=headers)
        logging.info(f"REQUEST STATUS {response.status_code}")
        if response.status_code == 200:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:success",
                                              f"client:{company_id}", f'run_number:{current_run_number}'])
            return response
        else:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:error",
                                              f"client:{company_id}", f'run_number:{current_run_number}'])
            if response.status_code == 401:
                logging.critical("!! Auth error. Token is invalid")
                raise AuthError(FLOW_NAME)
            elif response.status_code in [400, 403, 404, 429, 500, 502, 503, 504]:
                logging.error(
                    f"API doesn't respond. The request was stopped by status code - "
                    f"(Status Code {response.status_code}): {response.text}")
                raise MintFlowFailed(FLOW_NAME)

            logging.info(response.status_code)
            time.sleep(env_resolver.gcm2_wait_sleep * 8)
    raise MintFlowFailed(FLOW_NAME)


def parse_etl_response(etl_items: list, **kwargs) -> list:
    requests_list = []
    historical_range_mock = kwargs['dag_run'].conf.get('historical_range_in_days')
    year_ago = dates['YEAR_AGO']
    yesterday = dates["YESTERDAY"]
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        year_ago = dates['TODAY'] - datetime.timedelta(historical_range_mock)
    for item in etl_items:
        api_token = item["api_token"]
        company_id = item.get("company_id")
        for setting in item["settings"]:
            for setting_item in setting["items"]:
                app_id = setting_item["id"]
                platform = setting_item["platform"]
                campaign_details = {
                    'id': app_id,
                    'platform': platform
                }
                for month_period in split_to_monthly_date_range(year_ago, yesterday):
                    request_str = create_request(app_id, month_period[1], month_period[0])
                    # Create hist query

                    query = {
                        "app_id": app_id,
                        "company_id": company_id,
                        "platform": platform,
                        "request_str": request_str,
                        "type": "hist",
                        "token": api_token,
                        "date_to": month_period[1].strftime("%Y-%m-%d"),
                        "date_from": month_period[0].strftime("%Y-%m-%d"),
                        'details': campaign_details
                    }
                    logging.info("Hist query")
                    logging.info(query)
                    if not does_hist_log_exist(json.dumps(query['details'])):
                        logging.info("Hist request has not been processed")
                        requests_list.append(query)
                    else:
                        logging.info("hist request already processed")
                        # Create scheduled
                        campaign_details = {'id': app_id, 'platform': platform}
                        date_from = get_date_to_execute_on(dates, date_format="%Y-%m-%d", **kwargs)
                        request_str = create_request(app_id, dates["YESTERDAY"], date_from)
                        query = {
                            "app_id": app_id,
                            "company_id": company_id,
                            'details': campaign_details,
                            "platform": platform,
                            "request_str": request_str,
                            "type": "scheduled",
                            "token": api_token,
                            "date_to": yesterday.strftime("%Y-%m-%d"),
                            "date_from": date_from,
                        }
                        if query not in requests_list:
                            requests_list.append(query)

    # filter out the queries with empty app_id
    requests_list_filtered = list(filter(lambda x: bool(x['app_id']), requests_list))

    # dedup
    result = []
    [result.append(x) for x in requests_list_filtered if x not in result]
    return filter_out_standard_queries([i for i in result], **kwargs)


def format_data_types(parsed_df_with_metrics: pd.DataFrame, original_df: pd.DataFrame) -> pd.DataFrame:
    """
    this function will be called inside parse_different_metrics
    it converts column to the right data types
    and also formats df to contain non custom metrics only in one row

    ARGUMENTS:
    ### parsed_df_with_metrics
    report df with splitted custom metrict
    to different columns and joined together (see func parse_different_metrics)
    ### original_df
    raw report df
    """
    dim_cols = ["date", "media_source", "campaign", "platform", "app_id"]
    default_metrics = ["impressions", "clicks", "installs", "sessions"]
    custom_metrics = ["event_count", "unique_users_count", "event_revenue"]
    final_cols = dim_cols + ["activity_name"] + default_metrics + custom_metrics
    # formatting raw data to have only default metrics and dims
    original_df[custom_metrics] = 0
    original_df["activity_name"] = "0"
    original_df = original_df[final_cols]
    # formatting splited df to have only custom metrics
    parsed_df_with_metrics[default_metrics] = 0
    # union

    result_df = pd.concat(
        [parsed_df_with_metrics, original_df], ignore_index=True, axis=0)[final_cols].reindex()
    # filling none types
    result_df = result_df.fillna(dict.fromkeys(custom_metrics + default_metrics, 0))
    # casting to datatypes
    # extracting only numbers from the columns
    for col in default_metrics:
        if result_df[col].dtype == object:
            result_df[col] = result_df[col].str.extract("(\d+)")
    result_df = result_df.fillna(dict.fromkeys(custom_metrics + default_metrics, 0))
    result_df[custom_metrics + default_metrics] = result_df[
        custom_metrics + default_metrics
    ].astype(int)
    result_df["event_revenue"] = result_df["event_revenue"].astype("float")
    result_df = (result_df.groupby(
        dim_cols + ["activity_name"], as_index=False)[default_metrics + custom_metrics].sum().drop_duplicates())
    return result_df


def parse_different_metrics(df: pd.DataFrame, platform: str, app_id: str) -> pd.DataFrame:
    """
    parses different types of custom metrics
    - Sales in EUR
    - Unique users
    - Event counter
    separately and joins resulting into one
    """
    result_dfs = []  # the number is strictly 3
    map_dict = {
        "event_count": "Event counter",
        "unique_users_count": "Unique users",
        "event_revenue": "Sales in EUR",
    }
    df = df.rename(
        {
            "Date": "date",
            "Media Source (pid)": "media_source",
            "Campaign (c)": "campaign",
            "Impressions": "impressions",
            "Clicks": "clicks",
            "Installs": "installs",
            "Sessions": "sessions",
        },
        axis="columns",
    )

    for custom_metric_type in list(map_dict.keys()):
        df["platform"] = platform
        df["app_id"] = app_id
        col_list = [
            "date",
            "media_source",
            "campaign",
            "platform",
            "app_id",
            "impressions",
            "clicks",
            "installs",
            "sessions",
        ]

        # Getting list of present custom metrics, is dynamic for each api call
        custom_metrics = list(
            filter(
                lambda x, metric_type=custom_metric_type: (map_dict[metric_type] in x),
                df.columns.to_list(),
            )
        )
        all_cols = col_list + custom_metrics
        df_selected = df[all_cols]
        unpivoted_df = pd.melt(df_selected, id_vars=col_list, value_vars=custom_metrics)
        unpivoted_df = unpivoted_df.rename(
            {"variable": "activity_name", "value": custom_metric_type}, axis="columns"
        )
        unpivoted_df["activity_name"] = unpivoted_df["activity_name"].apply(
            lambda n: (n.split(" ", 1)[0])
        )
        result_dfs.append(unpivoted_df)

    cols_join = [
        "date",
        "media_source",
        "campaign",
        "clicks",
        "installs",
        "sessions",
        "impressions",
        "activity_name",
        "platform",
        "app_id"
    ]

    df_final = pd.merge(result_dfs[0], result_dfs[1], "outer", on=cols_join)
    df_final = pd.merge(df_final, result_dfs[2], "outer", on=cols_join)

    df_final = format_data_types(df_final, df)

    return df_final


def format_report(report: str, platform: str, app_id: str) -> pd.DataFrame:
    df = pd.read_csv(StringIO(report))
    if df.empty:
        logging.info(f'report: "{report}" is empty')
        return
    df_final = parse_different_metrics(df, platform, app_id)
    return df_final


@set_config(state)
@set_execution_dates(dates)
def get_requests_list(**kwargs) -> list:
    profiles = get_profiles(**kwargs)
    request_list = parse_etl_response(profiles, **kwargs)
    return request_list


@set_execution_dates(dates)
def get_files(requests: List[dict], **kwargs) -> List[dict]:
    files = []
    errors = {"historical": 0, "standard": 0}
    for request_dict in requests:
        platform = request_dict["platform"]
        company_id = request_dict['company_id']
        try:
            response = send_request(
                request_str=request_dict["request_str"],
                headers={"Authorization": request_dict["token"]},
                report_type=request_dict["type"],
                company_id=company_id,
                **kwargs
            )
            # Log hist query
            if response.status_code == 200 and request_dict["type"] == "hist":
                logging.info('Success on retrieving the file! status code 200')
                print(request_dict)
            files.append({"file": response.text, "platform": platform, 'query': request_dict})
            logging.info("Sleeping for 60 seconds")
            time.sleep(60)
        except Exception:
            error_message = (
                f"had an error on {json.dumps(request_dict)} item \n"
                + traceback.format_exc()
            )
            logging.error(error_message)
            if request_dict["type"] == "hist":
                errors["historical"] = +1
            else:
                errors["standard"] = +1
    logging.info(
        f"""
    Error statistic:
    Historical errors: {errors['historical']}
    Standard errors: {errors['standard']}
    """
    )
    return files


def process_files(files: List[dict], **kwargs) -> None:
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    for report_data in files:
        try:
            file = report_data["file"]
            platform = report_data["platform"]
            query = report_data['query']
            app_id = query["app_id"]
            company_id = query["company_id"]
            report = format_report(file, platform, app_id)
            if not report.empty:
                _execute_sql_query(report=report)
                increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                                 [f"table:{REPORTS_TABLE_NAME}", f"client:{company_id}", f'run_number:{current_run_number}'])
                if query['type'] == 'hist':
                    log_hist_request(
                        company_id,
                        json.dumps(query['details']),
                        FLOW_NAME,
                    )

        except Exception:
            error_message = (
                f"had an error during formating and inserting item \n" + traceback.format_exc()
            )
            logging.error(error_message)


with DAG(
    FLOW_NAME,
    description="appsflyer report dag",
    schedule_interval=None,
    tags=['ingestion_flow'],
    params=DagConfigSetter(FLOW_NAME).get_params(),
    start_date=datetime.datetime(2022, 1, 1),
    catchup=False,
) as dag:

    t1 = PythonOperator(
        task_id="get_requests",
        python_callable=get_requests_list,
        on_failure_callback=failure_callback,
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id="get_files",
        python_callable=get_files,
        on_failure_callback=failure_callback,
        op_kwargs={"requests": XComArg(t1)},
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="process_reports",
        python_callable=process_files,
        on_failure_callback=failure_callback,
        op_kwargs={"files": XComArg(t2)},
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )
    
    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> cleanup_xcoms
