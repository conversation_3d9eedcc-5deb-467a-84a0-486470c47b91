from collections import defaultdict
import copy
import concurrent.futures
import time
import datetime
import io
import json
import logging
import re
import traceback
import pandas as pd
import requests
from copy import deepcopy
from dataclasses import dataclass
from typing import List

from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pandas import DataFrame

from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.commons import (EtlEndpointProcessorWithParams, failure_callback, filter_out_standard_queries,
                                     set_execution_dates, _get_db_value, load_static_template, iter_batch, set_config,
                                     enable_hist_log_check_based_on_conf, get_date_to_execute_on)
from myn_airflow_lib.constants import (DV360_ETL_ENDPOINT, DV360_CREATE_QUERY_URL, DV360_RUN_REPORTS_URL,
                                       DV360_DOWNLOAD_REPORT_URL, REQUESTS_BAD_STATUSES,
                                       EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE)
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.exceptions import QuotaIsReached
from myn_airflow_lib.resolvers import EnvironmentResolver, GlobalsResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog

pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True
FLOW_NAME = 'DV360'
env_resolver = EnvironmentResolver()
globals_resolver = GlobalsResolver()
CAMPAIGN_TYPE = 'dbm_v2'
ENVIRONMENT = env_resolver.environment
FILTER_TEMPLATE = load_static_template('static/dv360_filter_template.json')
FILTER_TEMPLATE_YOUTUBE = load_static_template('static/dv360_youtube_filter_template.json')
REPORTS_TABLE_NAME = 'dv360_base_reports'
REPORTS_TABLE_NAME_YOUTUBE = 'dv360_base_reports_youtube'

state = {
    'dates': {}
}
dates = state['dates']
access_tokens = {}

init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)


@dataclass
class QueryData:
    data: dict
    type: str
    profile: dict
    log_data: dict
    report_class: str = 'standard'
    external_query_id: str = None
    external_report_id: str = None

    @property
    def campaign_detail(self):
        return json.dumps(self.log_data, sort_keys=True)

    @property
    def client_id(self):
        return self.profile['client_id']

    @property
    def company_id(self):
        return self.profile.get('company_id')

    @property
    def client_secret(self):
        return self.profile['client_secret']

    @property
    def refresh_token(self):
        return self.profile['refresh_token']

    def to_dict(self):
        return {'data': self.data,
                'type': self.type,
                'profile': self.profile,
                'log_data': self.log_data,
                'external_query_id': self.external_query_id,
                'external_report_id': self.external_report_id,
                'report_class': self.report_class}


def filter_profiles(profile: dict):
    """Quick filter of profiles"""
    return profile['advertiser_id'] is not None and profile['campaigns']


def group_profiles(profiles: list):
    """Group all profiles by advertiser_id and respective unique campaigns"""

    # create single advertisers with campaigns instead of multiple identical advertisers
    grouped_dict = {}
    for profile in profiles:
        grouped_dict.setdefault(f"{profile['advertiser_id']}:{profile['refresh_token']}",
                                {'advertiser_id': profile['advertiser_id'],
                                 'campaigns': [],
                                 'client_secret': profile['client_secret'],
                                 'client_id': profile['client_id'],
                                 'refresh_token': profile['refresh_token'],
                                 'company_id': profile['company_id']})['campaigns'] \
            .extend(profile['campaigns'])

    # remove duplicated campaigns for each advertiser
    unique_profile_campaigns = []
    for k, v in grouped_dict.items():
        # create unique campaigns list without duplicates
        camp_list = list(set([item['external_campaign_id'] for item in v['campaigns']]))
        unique_profile_campaigns.append({'advertiser_id': v['advertiser_id'],
                                         'campaigns': camp_list,
                                         'client_id': v['client_id'],
                                         'client_secret': v['client_secret'],
                                         'refresh_token': v['refresh_token'],
                                         'company_id': v['company_id']})
    return unique_profile_campaigns


def get_profiles(**kwargs):
    processor = EtlEndpointProcessorWithParams(DV360_ETL_ENDPOINT, **kwargs)
    # use this mock for testing, stage profiles don't match with campaigns on CM360
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    if config_mock:
        logging.info('endpoing mock was found using that...')
        profiles = config_mock
    else:
        profiles = processor.apply_all()
    # use xcom only for small amount of data, not big dataframes
    filtered_profiles = list(filter(filter_profiles, profiles))
    grouped_profiles = group_profiles(filtered_profiles)
    return grouped_profiles


@set_config(state)
@set_execution_dates(dates)
def get_google_queries(profiles, **kwargs):
    queries = []
    historical_range_mock = state['dag_run'].conf.get('historical_range_in_days')
    for profile in profiles:
        historical_query = get_historical_query(profile, historical_range_mock)
        if historical_query:
            queries.append(historical_query)
            historical_query = get_historical_query_youtube(profile, historical_range_mock)
            queries.append(historical_query)
        setting_sched_query = get_standard_query(profile, **kwargs)
        setting_sched_query_youtube = get_standard_query_youtube(profile, **kwargs)
        queries.append(setting_sched_query)
        queries.append(setting_sched_query_youtube)
    dict_queries = list(map(lambda x: x.to_dict(), queries))
    dict_queries = filter_out_standard_queries(dict_queries, **kwargs)
    logging.info(f'Going to process {len(dict_queries)} queries')
    return dict_queries


def get_historical_payload(date_from, date_to, filters):
    json_report_raw = copy.deepcopy(FILTER_TEMPLATE)
    json_report_raw['metadata'][
        'title'] = f"{ENVIRONMENT}_historical_{date_from}_{date_to}"
    json_report_raw['params']['filters'] = filters
    json_report_raw['metadata']['dataRange']['customStartDate'] = {'year': date_from.year,
                                                                   'month': date_from.month,
                                                                   'day': date_from.day}
    json_report_raw['metadata']['dataRange']['customEndDate'] = {'year': date_to.year,
                                                                 'month': date_to.month,
                                                                 'day': date_to.day}
    return json_report_raw


def get_historical_payload_youtube(date_from, date_to, filters):
    json_report_raw = copy.deepcopy(FILTER_TEMPLATE_YOUTUBE)
    json_report_raw['metadata'][
        'title'] = f"{ENVIRONMENT}_historical_{date_from}_{date_to}"
    json_report_raw['params']['filters'] = filters
    json_report_raw['metadata']['dataRange']['customStartDate'] = {'year': date_from.year,
                                                                   'month': date_from.month,
                                                                   'day': date_from.day}
    json_report_raw['metadata']['dataRange']['customEndDate'] = {'year': date_to.year,
                                                                 'month': date_to.month,
                                                                 'day': date_to.day}
    return json_report_raw


def get_historical_query_youtube(profile, historical_range_mock):
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        year_ago = dates['TODAY'] - datetime.timedelta(historical_range_mock)
    else:
        year_ago = dates['YEAR_AGO']
    filters = [{'type': "FILTER_ADVERTISER", 'value': str(profile["advertiser_id"])}]
    filtered_campaigns = profile['campaigns']
    if not filtered_campaigns:
        return
    campaign_detail = {"campaigns": filtered_campaigns,
                       "advertiser_id": profile['advertiser_id'], 'type': 'youtube'}

    json_report = get_historical_payload_youtube(year_ago, dates['TODAY'], filters)
    query = QueryData({
        'json_report': json_report,
        'log_date_from': year_ago.strftime('%Y-%m-%d'),
        'log_date_to': dates['TODAY'].strftime('%Y-%m-%d')},
        'historical',
        profile,
        campaign_detail,
        'youtube')
    return query


def get_historical_query(profile, historical_range_mock):
    two_years_ago = dates['TWO_YEARS_AGO']
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        two_years_ago = dates['TODAY'] - datetime.timedelta(historical_range_mock)
    filters = [{'type': "FILTER_ADVERTISER", 'value': str(profile["advertiser_id"])}]
    filtered_campaigns = profile['campaigns']
    if not filtered_campaigns:
        return
    for campaign in filtered_campaigns:
        filters.append({'type': "FILTER_MEDIA_PLAN", 'value': str(campaign)})

    campaign_detail = {"campaigns": filtered_campaigns,
                       "advertiser_id": profile['advertiser_id']}

    json_report = get_historical_payload(two_years_ago, dates['TODAY'], filters)
    query = QueryData({
        'json_report': json_report,
        'log_date_from': two_years_ago.strftime('%Y-%m-%d'),
        'log_date_to': dates['TODAY'].strftime('%Y-%m-%d')},
        'historical',
        profile,
        campaign_detail)
    if not does_hist_log_exist(query):
        return query
    else:
        return


def get_campaign_detail(req: QueryData):
    return req.campaign_detail


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(hist_request: QueryData):
    campaign_detail = get_campaign_detail(hist_request)

    query = f"""
            select id from {HISTORICAL_LOG_TABLE}
            where 
             campaign_type = '{CAMPAIGN_TYPE}'
             and campaign_detail = '{campaign_detail}' limit 1;
            """
    df = pg_hook.get_pandas_df(query)
    return not df.empty


def get_body_for_standart(profile, is_youtube=False, **kwargs):
    
    filters = [{'type': "FILTER_ADVERTISER", 'value': str(profile["advertiser_id"])}]
    if is_youtube:
        raw_report = copy.deepcopy(FILTER_TEMPLATE_YOUTUBE)
    else:
        raw_report = copy.deepcopy(FILTER_TEMPLATE)
        for campaign in profile["campaigns"]:
            filters.append({'type': "FILTER_MEDIA_PLAN",
                        'value': str(campaign)})

    date_from = get_date_to_execute_on(dates, **kwargs)
    date_from = datetime.datetime.strptime(date_from, '%Y%m%d')
    date_to = dates['TODAY']
    raw_report['metadata']['title'] = f"{ENVIRONMENT}_YESTERDAY_{str(profile['advertiser_id'])}"
    raw_report['params']['filters'] = filters
    raw_report['metadata']['dataRange']['customStartDate'] = {'year': date_from.year,
                                                              'month': date_from.month,
                                                              'day': date_from.day}
    raw_report['metadata']['dataRange']['customEndDate'] = {'year': date_to.year,
                                                            'month': date_to.month,
                                                            'day': date_to.day}
    return raw_report


def get_standard_query(profile, **kwargs):
    raw_report = get_body_for_standart(
        profile,
        **kwargs
    )
    campaign_details = {"advertiser_id": profile['advertiser_id'],
                        'campaigns': profile["campaigns"]}
    standard_query = QueryData({"json_report": raw_report}, 'standard', profile,
                               campaign_details)
    return standard_query


def get_standard_query_youtube(profile, **kwargs):
    raw_report = get_body_for_standart(
        profile,
        True,
        **kwargs
    )
    campaign_details = {"advertiser_id": profile['advertiser_id'],
                        'campaigns': profile["campaigns"], "type": "youtube"}
    standard_query = QueryData({"json_report": raw_report}, 'standard', profile,
                               campaign_details, 'youtube')
    return standard_query


def get_access_token(client_id, refresh_token, client_secret, force_update=False):
    if refresh_token in access_tokens and not force_update:
        return access_tokens[refresh_token]
    response = requests.post(
        'https://accounts.google.com/o/oauth2/token',
        data={
            'client_id': client_id,
            'client_secret': client_secret,
            'refresh_token': refresh_token,
            'grant_type': 'refresh_token'
        }
    )
    logging.info('Access token resp received')
    if 'access_token' not in response.json():
        logging.error(f"No `access_token_key` in resp {response.text}")
        return None
    access_tokens[refresh_token] = response.json()['access_token']
    return access_tokens[refresh_token]


def send_google_request(query: QueryData, url: str, method: str, json_data: dict = None, report_type: str = None, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    access_token = get_access_token(query.client_id, query.refresh_token, query.client_secret)
    headers = {
        'Authorization': f"Bearer {access_token}"
    }
    request_data = {'headers': headers,
                    'url': url,
                    'method': method}
    if json_data:
        request_data['json'] = json_data
    if headers:
        request_data['headers'].update(headers)
    logging.info(request_data)
    for _ in range(5):
        response = requests.request(**request_data)
        if response.status_code in REQUESTS_BAD_STATUSES:
            increment_metric('airflow_request_counter.increment', ENVIRONMENT, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:error",
                                              f"client:{query.company_id}",
                                              f'run_number:{current_run_number}', ])
            logging.warning(f'send request with data = {json.dumps(request_data)} \n'
                            f'received {response.status_code} \n'
                            f'{response.text}')
            if response.status_code == 401:
                logging.info('need new access token')
                access_token_retry = get_access_token(
                    query.client_id, query.refresh_token, query.client_secret,
                    force_update=True
                )
                headers['Authorization'] = f"Bearer {access_token_retry}"
        else:
            increment_metric('airflow_request_counter.increment', ENVIRONMENT, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:success",
                                              f"client:{query.company_id}", f'run_number:{current_run_number}'])
            return response
    data_to_log = deepcopy(request_data)
    data_to_log.pop('headers')
    raise QuotaIsReached(FLOW_NAME)


def put_report_to_db_youtube(report: DataFrame):
    columns_order = ['date', 'advertiser_id', 'line_item_id', 'io_id', 'youtube_ad_id', 'youtube_ad_name',
                     'dsp', 'clicks', 'impressions', 'viewable_impressions',
                     'quartile_1', 'quartile_2', 'quartile_3', 'quartile_4', 'spent', 'true_view_views',
                     'advertiser_currency']
    report = report[columns_order].drop_duplicates()
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]

    # batching data to push in DB
    for i, batch_rows in enumerate(iter_batch(fixed_rows, 5000)):
        logging.info(f"Inserting batch[max_size=1000] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        query = f"""INSERT INTO {REPORTS_TABLE_NAME_YOUTUBE} ({columns})
                    VALUES
                    {values_str} ON CONFLICT (date, advertiser_id, line_item_id,
                                              youtube_ad_id, dsp, io_id, youtube_ad_name)
                    DO UPDATE
                    SET clicks = Excluded.clicks, impressions = Excluded.impressions,
                    spent = Excluded.spent, viewable_impressions = Excluded.viewable_impressions,
                    quartile_1 = Excluded.quartile_1, quartile_2 = Excluded.quartile_2,
                    quartile_3 = Excluded.quartile_3, quartile_4 = Excluded.quartile_4,
                    true_view_views = Excluded.true_view_views,
                    advertiser_currency = Excluded.advertiser_currency
            """
        pg_hook.run(query, True)


def put_report_to_db(report: DataFrame):
    columns_order = ['date', 'advertiser_id', 'campaign_id', 'line_item_id', 'io_id', 'creative_id', 'creative_name',
                     'dsp', 'clicks', 'conversions', 'post_views', 'post_clicks', 'impressions', 'viewable_impressions',
                     'quartile_1', 'quartile_2', 'quartile_3', 'quartile_4', 'spent', 'starts_video', 'true_view_views',
                     'advertiser_currency']
    report = report[columns_order].drop_duplicates()
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]

    # batching data to push in DB
    for i, batch_rows in enumerate(iter_batch(fixed_rows, 5000)):
        logging.info(f"Inserting batch[max_size=1000] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        query = f"""INSERT INTO {REPORTS_TABLE_NAME} ({columns})
                    VALUES
                    {values_str} ON CONFLICT (date, advertiser_id, campaign_id, line_item_id,
                                              creative_id, dsp, io_id)
                    DO UPDATE
                    SET clicks = Excluded.clicks, conversions = Excluded.conversions, post_views = Excluded.post_views,
                    post_clicks = Excluded.post_clicks, impressions = Excluded.impressions,
                    spent = Excluded.spent, viewable_impressions = Excluded.viewable_impressions,
                    quartile_1 = Excluded.quartile_1, quartile_2 = Excluded.quartile_2,
                    quartile_3 = Excluded.quartile_3, quartile_4 = Excluded.quartile_4,
                    starts_video = Excluded.starts_video, true_view_views = Excluded.true_view_views,
                    advertiser_currency = Excluded.advertiser_currency, creative_name = Excluded.creative_name;
            """
        pg_hook.run(query, True)


def log_hist_request(query: QueryData):
    campaign_detail = get_campaign_detail(query)
    company_id = query.company_id
    if 'type' in json.loads(campaign_detail):
        logging.info('Hist log push is skipped for youtube query')
        return
    query = f"""
                INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
                Values (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
                """
    data = {
        'company_id': company_id,
        'campaign_type': CAMPAIGN_TYPE,
        'campaign_detail': campaign_detail}
    pg_hook.run(query, True, data)


def prepare_report(df):
    df = df.rename(columns={"Date": "date",
                            "AdvertiserID": "advertiser_id",
                            "LineItemID": "line_item_id",
                            "CreativeID": 'creative_id',
                            "Creative": 'creative_name',
                            "CampaignID": 'campaign_id',
                            "InsertionOrderID": 'io_id',
                            'Impressions': 'impressions',
                            "Clicks": 'clicks',
                            'TotalConversions': "conversions",
                            'First-QuartileViewsVideo': 'quartile_1',
                            'MidpointViewsVideo': 'quartile_2',
                            'Third-QuartileViewsVideo': 'quartile_3',
                            'CompleteViewsVideo': 'quartile_4',
                            'RevenueAdvCurrency': 'spent',
                            'ActiveViewViewableImpressions': 'viewable_impressions',
                            'Post-ClickConversions': "post_clicks",
                            'Post-ViewConversions': 'post_views',
                            'StartsVideo': 'starts_video',
                            'TrueViewViews': 'true_view_views',
                            'AdvertiserCurrency': 'advertiser_currency'
                            })
    df['dsp'] = 'dbm'
    df['date'] = df['date'].apply(lambda x: datetime.datetime.strptime(str(x), '%Y%m%d').strftime('%Y-%m-%d'))
    df = df[['date', 'dsp', 'advertiser_id', 'line_item_id', 'creative_id', 'creative_name', 'campaign_id', 'io_id', 'impressions',
             'clicks', 'conversions', 'quartile_1', 'quartile_2', 'quartile_3', 'quartile_4', 'spent', 'viewable_impressions',
             'post_clicks', 'post_views', 'starts_video', 'true_view_views', 'advertiser_currency']]
    return df


def prepare_report_youtube(df):
    df = df.rename(columns={"Date": "date",
                            "AdvertiserID": "advertiser_id",
                            "YouTubeAdID": "youtube_ad_id",
                            "YouTubeAd": "youtube_ad_name",
                            "LineItemID": "line_item_id",
                            "CampaignID": 'campaign_id',
                            "InsertionOrderID": 'io_id',
                            'Impressions': 'impressions',
                            "Clicks": 'clicks',
                            'First-QuartileViewsVideo': 'quartile_1',
                            'MidpointViewsVideo': 'quartile_2',
                            'Third-QuartileViewsVideo': 'quartile_3',
                            'CompleteViewsVideo': 'quartile_4',
                            'RevenueAdvCurrency': 'spent',
                            'ActiveViewViewableImpressions': 'viewable_impressions',
                            'TrueViewViews': 'true_view_views',
                            'AdvertiserCurrency': 'advertiser_currency'
                            })
    df['dsp'] = 'dbm'
    df['date'] = df['date'].apply(lambda x: datetime.datetime.strptime(str(x), '%Y%m%d').strftime('%Y-%m-%d'))
    df = df[['date', 'dsp', 'advertiser_id', 'youtube_ad_id', 'youtube_ad_name',  'line_item_id', 'io_id', 'impressions',
             'clicks', 'quartile_1', 'quartile_2', 'quartile_3', 'quartile_4', 'spent', 'viewable_impressions',
             'true_view_views', 'advertiser_currency']]
    return df


def download_report(query, url, report_type, report_class, **kwargs):
    logging.info(report_class)
    report_response = send_google_request(query, url, 'GET', report_type=report_type, **kwargs)
    text = report_response.text.replace(
        ' ', '').replace(
        '/', '', ).replace(
        ':', '', ).replace(
        '(', '', ).replace(
        ')', '', ).replace(
        "'", '', ).replace(
        "'", '', )
    text = re.sub(r'\n\D.*', '', text)
    df = pd.read_csv(io.StringIO(text), sep=",")
    if report_class == 'youtube':
        df = prepare_report_youtube(df)
    else:
        df = prepare_report(df)
    return df


def run_query(query: QueryData, **kwargs):
    # create query_id
    response_create_query = send_google_request(query, DV360_CREATE_QUERY_URL, 'POST', query.data['json_report'],
                                                report_type=query.type, **kwargs)
    external_query_id = response_create_query.json()['queryId']
    # run reports
    response_run_report = send_google_request(query, DV360_RUN_REPORTS_URL.format(query_id=external_query_id), 'POST',
                                              report_type=query.type, **kwargs)
    external_report_id = response_run_report.json()['key']['reportId']
    return external_query_id, external_report_id


def run_single_report(query_data, **kwargs):
    query = QueryData(**query_data)
    try:
        external_query_id, external_report_id = run_query(query, **kwargs)
        query.external_query_id = external_query_id
        query.external_report_id = external_report_id
    except QuotaIsReached:
        logging.critical(f'Quota is reached for {query_data}')
        raise
    return query


def run_reports(queries: List[dict], **kwargs):
    result = []
    errors = {"historical": 0,
              "standard": 0}
    for query_data in queries:
        try:
            query_result = run_single_report(query_data, **kwargs)
            result.append(query_result)
        except Exception:
            errors.setdefault(query_data['type'], 0)
            errors[query_data['type']] += 1
            error_message = f'had an error on {json.dumps(query_data)} item \n' + traceback.format_exc()
            logging.error(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x["type"] == error_type, queries)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')
    dict_queries = list(map(lambda x: x.to_dict(), result))
    return dict_queries


def download_report_link(query, **kwargs):
    link = DV360_DOWNLOAD_REPORT_URL.format(query_id=query.external_query_id, report_id=query.external_report_id)
    logging.info(f'download report link = {link}')
    retries = 12
    while retries:
        response = send_google_request(query, link, "GET", report_type=query.type, **kwargs)
        resp_state = response.json()['metadata']['status']['state']
        if resp_state == 'DONE':
            report_link = response.json()['metadata']['googleCloudStoragePath']
            logging.info(f'Link {report_link} received')
            return report_link
        elif resp_state in ('QUEUED', 'RUNNING'):
            retries -= 1
            time.sleep(150)
            logging.info(f'Getting link is still in progress')
            continue
        else:
            logging.error(
                f"Can't process request. Response status is not DONE/QUEUED/RUNNING")
            break
    message = f"Can't download link for url {link}"
    logging.error(message)
    raise Exception(message)


def load_single_report(query_data, **kwargs):
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    query = QueryData(**query_data)
    try:
        cloud_storage_path = download_report_link(query, **kwargs)
    except Exception as e:
        logging.warning(f"Exception during download_report_link {e}")
        logging.warning(f'Error while getting download link for company={query.company_id} and {query.report_class}, {query.type}')
        return
    report = download_report(query, cloud_storage_path, report_type=query.type, report_class=query.report_class, **kwargs)
    if report.empty:
        message = f'report {cloud_storage_path} is empty with type {query.type}'
        logging.warning(message)
    else:
        if query.report_class == 'youtube':
            put_report_to_db_youtube(report)
        else:
            put_report_to_db(report)
        increment_metric('airflow_data_pushed.increment', ENVIRONMENT, FLOW_NAME, report.shape[0],
                         [f"table:{REPORTS_TABLE_NAME}", f"client:{query.company_id}", f'run_number:{current_run_number}'])
        logging.debug('report is pushed')
    if query.type == 'historical':
        log_hist_request(query)
        logging.debug('log is pushed')


@set_execution_dates(dates)
def load_reports(queries, **kwargs):
    errors = {"historical": 0,
              "standard": 0}
    for query_data in queries:
        try:
            load_single_report(query_data, **kwargs)
        except Exception:
            errors.setdefault(query_data['type'], 0)
            errors[query_data['type']] += 1
            error_message = f'had an error on {query_data} query \n' + traceback.format_exc()
            logging.error(error_message)

    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x["type"] == error_type, queries)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')


def process_single_profile(query, **kwargs):
    """
    this function handles the whole process of requesting
    downloading report and putting to the db
    """
    try:
        query_result = run_single_report(query, **kwargs)
        result = query_result.to_dict()
        load_single_report(result, **kwargs)
        return True, query['type'], query['report_class']
    except Exception:
        error_message = f'had an error on {query} query \n' + traceback.format_exc()
        logging.error(error_message)
        return False, query['type'], query['report_class']


@set_execution_dates(dates)
def run_and_load_reports(queries, **kwargs):
    report_summary = defaultdict(lambda: {"success": 0, "failure": 0})
    total_reports = len(queries)

    with concurrent.futures.ThreadPoolExecutor(max_workers=29) as executor:
        futures = [executor.submit(process_single_profile, query, **kwargs) for query in queries]

        for future in concurrent.futures.as_completed(futures):
            try:
                success, report_type, report_class = future.result()  # Unpack the result

                if success:
                    report_summary[(report_type, report_class)]["success"] += 1
                else:
                    report_summary[(report_type, report_class)]["failure"] += 1
            except Exception as e:
                logging.error(f'Error in future result: {e}')

    # Print the overall summary
    print(f"Processed {total_reports} reports in total.")

    # Print a breakdown of successes and failures by type and class
    for (report_type, report_class), counts in report_summary.items():
        print(f"Report type: {report_type}, class: {report_class} -> "
              f"Success: {counts['success']}, Failure: {counts['failure']}")


with DAG(FLOW_NAME,
         description='dv360 flow dag',
         schedule_interval=None,
         tags=['ingestion_flow'],
         params=DagConfigSetter(FLOW_NAME).get_params(),
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False) as dag:
    t1 = PythonOperator(
        task_id='get_profiles',
        python_callable=get_profiles,
        on_failure_callback=failure_callback,
        provide_context=True,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id="get_google_queries",
        python_callable=get_google_queries,
        on_failure_callback=failure_callback,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="process_reports",
        python_callable=run_and_load_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"queries": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> cleanup_xcoms
