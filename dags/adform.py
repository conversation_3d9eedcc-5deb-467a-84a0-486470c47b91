# API - https://mint-ai.atlassian.net/wiki/spaces/TEAMJ/pages/4327866389/Adform+API+Investigation
# IMPLEMENTATION - https://mint-ai.atlassian.net/wiki/spaces/TEAMJ/pages/3772973362/Post-IMPL+Doc+Adfrom+Airflow+Migration
import datetime
from typing import List, Optional

import traceback
import json
import logging
import time
from dataclasses import dataclass

import pandas as pd
import requests
from airflow_common.dag_config_setter import DagConfigSetter
import numpy as np
from myn_airflow_lib.datadog import increment_metric, init_datadog
from myn_airflow_lib.custom_operators import CleanupXcomsOperator

from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook

from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.exceptions import MintFlowFailed
from myn_airflow_lib.commons import (
    EtlEndpointProcessor, failure_callback, filter_out_standard_queries, set_execution_dates, _get_db_value,
    load_static_template, iter_batch, set_config, enable_hist_log_check_based_on_conf, get_date_to_execute_on)
from myn_airflow_lib.constants import (ADFORM_ETL_URL, ADFORM_REFRESH_TOKEN_URL_FORMAT, ADFORM_STATS_ENDPOINT,
                                       ADFORM_GRAB_NEW_TOKEN_URL_FORMAT, EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE)

REQUESTS_BAD_STATUSES = [400, 401, 403, 404, 405, 415, 429, 500, 501, 503]  # Adform API statuses from docs
REQUESTS_GOOD_STATUSES = [200, 201, 202]  # Adform API statuses from docs


pg_hook = PostgresHook("uri_reports")
pg_hook.log.disabled = True
FLOW_NAME = 'Adform'
CAMPAIGN_TYPE = 'adform_v3'
env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
state = {
    'dates': {},
    'quota_exceeded_tokens': set(),
}
dates = state['dates']
REPORTS_TABLE_NAME = 'reports_adformconversion_v2'


@dataclass
class QueryData:
    id: int
    body: str
    type: str
    access_token: str
    advertiser: int
    tracking_filters: int
    date_from: str
    date_to: str
    campaigns: List[int]
    profile: dict
    report_location: str = None
    report_created_at: float = None

    @property
    def campaign_details(self):
        return get_campaign_details(self.advertiser, self.campaigns, self.tracking_filters)

    @property
    def company_id(self):
        return self.profile['company_id']
    
    @property
    def unique_id(self):
        return json.dumps(
            {
                "campaign_details": self.campaign_details,
                "type": self.type,
                "id": self.id,
            },
            sort_keys=True
        )

    def to_dict(self):
        return {
            'id': self.id,
            'body': self.body,
            'type': self.type,
            'access_token': self.access_token,
            'advertiser': self.advertiser,
            'tracking_filters': self.tracking_filters,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'campaigns': self.campaigns,
            'profile': self.profile,
            'report_location': self.report_location,
            'report_created_at': self.report_created_at
        }
    

def get_campaign_details(advertiser, campaigns, tracking_filters):
    campaign_details = {
        "advertiser": str(advertiser),
        "campaigns": campaigns,
        "tracking": tracking_filters
    }
    return json.dumps(campaign_details, sort_keys=True)


def hit_datintell_endpoint(**kwargs):
    mocked_resp = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    # use this mock for testing
    if mocked_resp:
        return mocked_resp
    response = EtlEndpointProcessor(ADFORM_ETL_URL, **kwargs).apply_all()
    return list(response)


def refresh_token_request(integration: int):
    """
    Update token in db
    """
    url = ADFORM_REFRESH_TOKEN_URL_FORMAT.format(integration=integration)
    return requests.request("GET", url).json().get("message") in ["success", "Action not required"]


def grab_new_token(integration: int):
    """
    get new token from db
    """
    url = ADFORM_GRAB_NEW_TOKEN_URL_FORMAT.format(integration=integration)
    response = requests.request("GET", url)
    return response.json()["access_token"]


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(advertiser, campaigns, tracking_filters):
    campaign_details = get_campaign_details(advertiser, campaigns, tracking_filters)
    query_string = f"""
        select id from {HISTORICAL_LOG_TABLE}
        where
            campaign_type = '{CAMPAIGN_TYPE}'
            and campaign_detail = '{campaign_details}'
        limit 1
    """
    df = pg_hook.get_pandas_df(query_string)
    return not df.empty


def log_hist_request(campaign_details, company_id):
    sql_query = f"""
        insert into {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
        values('{company_id}','{CAMPAIGN_TYPE}','{campaign_details}')
    """
    pg_hook.run(sql_query)


def _get_query_tpl():
    return load_static_template('static/adform_template.json')


def create_body_with_dates(tracking: int, campaigns: list,
                           date_from: str, date_to: str):
    body = _get_query_tpl()
    body["filter"]["date"] = {
        "from": date_from,
        "to": date_to,
    }
    body["filter"]["tracking"]["id"] = tracking
    body["filter"]["campaign"]["id"] = campaigns
    return json.dumps(body)


def create_body_standard(tracking: int, campaigns: list, **kwargs):
    week_ago_format = get_date_to_execute_on(dates, "%Y-%m-%d", **kwargs)
    yesterday_format = dates['YESTERDAY'].strftime("%Y-%m-%d")
    return create_body_with_dates(
        tracking,
        campaigns,
        week_ago_format,
        yesterday_format,
    )


def create_body_historical(tracking: int, campaigns: list, historical_range_mock: Optional[int]):
    date_from = dates['YEAR_AGO']
    yesterday_format = dates['YESTERDAY'].strftime("%Y-%m-%d")
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        date_from = dates['TODAY'] - datetime.timedelta(historical_range_mock)
    date_from = date_from.strftime('%Y-%m-%d')
    return create_body_with_dates(
        tracking,
        campaigns,
        date_from,
        yesterday_format,
    )


@set_config(state)
@set_execution_dates(dates)
def generate_queries(**kwargs):
    dag_run = state['dag_run']
    historical_range_mock = dag_run.conf.get('historical_range_in_days')
    disable_historical = dag_run.conf.get('disable_historical')
    datintell_endpoint_resp = hit_datintell_endpoint(**kwargs)
    if len(datintell_endpoint_resp) == 0:
        return []
    settings_df = parse_datintell_endpoint_resp(datintell_endpoint_resp)
    standard_queries, historical_queries = {}, {}
    for i_row, row in settings_df.iterrows():
        standard_body = create_body_standard(row.tracking_filters, row.campaigns, **kwargs)
        standard_query = QueryData(row.id, standard_body, "standard", row.access_token,
                                   row.advertiser, row.tracking_filters, row.date_from, row.date_to,
                                   row.campaigns, row.to_dict())
        standard_queries[standard_query.unique_id] = standard_query
        if disable_historical:
            continue
        if row.date_to and row.date_to < dates['YEAR_AGO'].strftime('%Y-%m-%d'):
            # too old setting
            continue
        histlog_exist = does_hist_log_exist(row.advertiser, row.campaigns, row.tracking_filters)
        if histlog_exist:
            continue
        historical_body = create_body_historical(row.tracking_filters, row.campaigns, historical_range_mock)
        if historical_body is None:
            logging.warning(f"Cannot generate body for historical query {row.to_dict()}")
            continue
        historical_query = QueryData(row.id, historical_body, "historical", row.access_token,
                                     row.advertiser, row.tracking_filters, row.date_from, row.date_to,
                                     row.campaigns, row.to_dict())
        historical_queries[historical_query.unique_id] = historical_query
    logging.info(f"Generated {len(standard_queries) + len(historical_queries)} queries"
                 f" -> {len(historical_queries)} historical and {len(standard_queries)} standard")
    return filter_out_standard_queries([
        query.to_dict() for query in list(standard_queries.values())
                                     + list(historical_queries.values())
    ], **kwargs)


def hitting_stats_api(body, access_token, integration, query_type, company_id, **kwargs):
    """
    1 STEP. REPORT ORDERING to find operation location
    https://api.adform.com/v1/buyer/stats/data
    """
    url = ADFORM_STATS_ENDPOINT
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    if refresh_token_request(integration=integration):
        access_token = grab_new_token(integration)
        logging.info("Got new Access token")

    for _ in range(5):
        if access_token in state['quota_exceeded_tokens']:
            logging.warning("Access token had quota_exceeded error. Going to skip")
            break
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        response = requests.request("POST", url, headers=headers, data=body)
        time.sleep(7)
        if response.status_code in REQUESTS_BAD_STATUSES:
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}", f"request_status:error",
                                 f"client:{company_id}", f'run_number:{current_run_number}'])
            logging.warning(
                f"send report ordering request with data = {json.dumps(body)} \n"
                f"received code {response.status_code} \n"
                f"received response {response.text}"
            )
            if response.status_code == 401:
                if refresh_token_request(integration=integration):
                    access_token = grab_new_token(integration)
                    logging.info("Got new Access token")
                else:
                    logging.error(f"Failed to update access token after receiving 401")
                    break
            elif response.status_code == 429:
                logging.error(f"Error 429 received `{response.text}`")
                if "Quota per Day exceeded" in response.text:
                    state['quota_exceeded_tokens'].add(access_token)
                    break
            elif response.status_code == 400:
                logging.warning(
                f"send report ordering request with data = {json.dumps(body)} \n"
                f"received code {response.status_code} \n"
                f"received response {response.text}"
                )                
        elif response.status_code in REQUESTS_GOOD_STATUSES:        
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}", f"request_status:success",
                                 f"client:{company_id}", f'run_number:{current_run_number}'])
            return response.headers["operation-location"]
        else:
            logging.error(f"hitting_stats_api report failed"
                          f" full resp {response.text} with response status code {response.status_code}")
            raise MintFlowFailed(f"{FLOW_NAME} failed when hitting_stats_api"
                                 f" report status code is `{response.status_code}`")
    increment_metric(
        'airflow_critical_error_counter.increment', env_type, FLOW_NAME,
        additional_tags=[f"request_type:{query_type}", f"response_code:{response.status_code}",
                         f"client:{company_id}", f'run_number:{current_run_number}'])
    raise MintFlowFailed(FLOW_NAME)


def hitting_oper_location_api(operation_location, access_token, integration, query_type, company_id, **kwargs):
    """
    2 STEP. Bombarding operation location endpoint to get report link
    waiting_report_sleep_time -> 1+7*2+1+7*2*2+1+7*2*2*2+1+7*2*2*2*2+1+7*2*2*2*2*2 = 439 seconds / 5 iterations
    """
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    if refresh_token_request(integration=integration):
        access_token = grab_new_token(integration)
        logging.info("Got new Access token")
    waiting_report_sleep_time = 7
    for _ in range(5):
        if access_token in state['quota_exceeded_tokens']:
            logging.warning("Access token had quota_exceeded error. Going to skip")
            break
        url = f"https://api.adform.com{operation_location}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        response = requests.request(method="get", headers=headers, url=url)
        logging.info(f'RESPONSE STATUS CODE - {response.status_code}')
        waiting_report_sleep_time *= 2
        time.sleep(1)
        if response.status_code in REQUESTS_BAD_STATUSES:
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}",
                                 f"request_status:error", f"client:{company_id}", f'run_number:{current_run_number}'])
            logging.warning(
                f"send operation location request with data = {operation_location} \n"
                f"received code {response.status_code} \n"
                f"received response {response.text}"
            )
            if response.status_code == 401:
                if refresh_token_request(integration=integration):
                    access_token = grab_new_token(integration)
                    logging.info("Got new Access token")
                else:
                    logging.error(f"Failed to update access token after receiving 401")
                    break
            elif response.status_code == 429:
                logging.error(f"Error 429 received `{response.text}`")
                if "Quota per Day exceeded" in response.text:
                    state['quota_exceeded_tokens'].add(access_token)
                    break
        elif response.status_code in REQUESTS_GOOD_STATUSES:
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}", f"request_status:success", f"client:{company_id}", f'run_number:{current_run_number}'])
            report_status = response.json()["status"].lower()
            logging.info(f"REPORT STATUS CODE - {report_status}")
            if report_status == "succeeded":
                return response.json()["location"]
            elif report_status in ["notstarted", "running"]:
                logging.info(f"Report is not ready yet for {operation_location} with status {report_status}")
                time.sleep(waiting_report_sleep_time)
        else:
            logging.error(f"hitting_oper_location_api report failed"
                          f" full resp {response.text} with response status code {response.status_code}")
            raise MintFlowFailed(f"{FLOW_NAME} failed when hitting_oper_location_api"
                                 f" report status code is `{response.status_code}`")
    increment_metric(
        'airflow_critical_error_counter.increment', env_type, FLOW_NAME,
        additional_tags=[f"request_type:{query_type}", f"response_code:{response.status_code}", f"client:{company_id}", f'run_number:{current_run_number}'])
    raise MintFlowFailed(FLOW_NAME)


def hitting_location_api(location, access_token, integration, query_type, company_id, **kwargs):
    """
    3 STEP. Report downloading with data
    """
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    if refresh_token_request(integration=integration):
        access_token = grab_new_token(integration)
        logging.info("Got new Access token")
    for _ in range(5):
        if access_token in state['quota_exceeded_tokens']:
            logging.warning("Access token had quota_exceeded error. Going to skip")
            break
        url = f"https://api.adform.com{location}"
        logging.info(f"location url : {url}")
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        response = requests.request(method="get", headers=headers, url=url)
        time.sleep(7)
        if response.status_code in REQUESTS_BAD_STATUSES:
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}",
                f"request_status:error", f"client:{company_id}", f'run_number:{current_run_number}'])
            logging.warning(
                f"send location request with data = {location} \n"
                f"received {response.status_code}"
            )
            if response.status_code == 401:
                if refresh_token_request(integration=integration):
                    access_token = grab_new_token(integration)
                    logging.info("Got new Access token")
                else:
                    logging.error(f"Failed to update access token after receiving 401")
                    break
            elif response.status_code == 429:
                logging.error(f"Error 429 received `{response.text}`")
                if "Quota per Day exceeded" in response.text:
                    state['quota_exceeded_tokens'].add(access_token)
                    break
                elif "Quota per Minute exceeded" in response.text:
                    time.sleep(60)
        else:
            increment_metric(
                'airflow_request_counter.increment', env_type, FLOW_NAME,
                additional_tags=[f"request_type:{query_type}", f"request_status:success",
                 f"client:{company_id}", f'run_number:{current_run_number}'])
            return response.json()
    increment_metric(
        'airflow_critical_error_counter.increment', env_type, FLOW_NAME,
        additional_tags=[f"request_type:{query_type}", f"response_code:{response.status_code}",
         f"client:{company_id}", f'run_number:{current_run_number}'])
    raise MintFlowFailed(FLOW_NAME)


def parse_datintell_endpoint_resp(datintell_endpoint_resp):
    src_df = pd.DataFrame.from_records(datintell_endpoint_resp)
    # extracting settings
    settings_df = src_df[["id", "settings", "advertiser", "company_id"]]
    settings_df = settings_df[settings_df.settings.str.len() > 0]
    tmp = settings_df.explode("settings").reset_index(drop=True)
    settings_df = pd.concat([tmp, pd.json_normalize(tmp["settings"])], axis=1).drop(
        "settings", axis=1
    )
    # Removing empty campaigns
    settings_df = settings_df[settings_df["campaigns"].str.len() > 0]
    # Filter active campaigns only: is_active = True
    campaigns = [[camp_dict.get("id") for camp_dict in list(
            filter(lambda x: bool(x["is_active"]), camp_list))] for camp_list in settings_df["campaigns"]]
    settings_df["campaigns"] = campaigns
    # we need to fetch tracking_filters=0 for all
    settings_df["tracking_filters"] = settings_df["tracking_filters"].apply(
        lambda tf: tf if 0 in tf else (tf + [0])
    )
    # splitting tracking filters
    settings_df = settings_df.explode("tracking_filters").reset_index(drop=True)
    # Adding access_token to df
    settings_df = settings_df.merge(
        src_df[["id", "access_token"]], how="left", on="id"
    )
    # need to replace numpy `nan` with plain python None to prevent issues
    settings_df['advertiser'] = settings_df.replace({np.nan: None})['advertiser']

    # group settings with expanded date range (min date_from and max date_to range from each grouped subset)
    settings_df = settings_df.groupby(["id", "advertiser", "tracking_filters", "access_token", "company_id"]).agg(
        {'campaigns': sum, 'date_from': min, 'date_to': max}).reset_index()
    return settings_df


def put_reports_to_db(report, company_id, **kwargs):
    """Prepare and runs sql UPSERT query"""
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    logging.info(f"Starting pushing to DB {len(report)} rows")
    columns = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        query = f"""
            INSERT INTO {REPORTS_TABLE_NAME}({columns})    
            VALUES {values_str}
            ON CONFLICT (date, advertiser_id, banner_id, media_id, line_item_id,
                         campaign_id, tracker_id, tag_id) 
            DO UPDATE SET
                conversions_post_imp = EXCLUDED.conversions_post_imp,
                conversions_post_click = EXCLUDED.conversions_post_click,
                conversions = EXCLUDED.conversions,
                clicks=EXCLUDED.clicks,
                impressions=EXCLUDED.impressions,
                video_play_start_count=EXCLUDED.video_play_start_count,
                video_complete_count=EXCLUDED.video_complete_count,
                video_events_play_time_percent_25=EXCLUDED.video_events_play_time_percent_25,
                video_events_play_time_percent_50=EXCLUDED.video_events_play_time_percent_50,
                video_events_play_time_percent_75=EXCLUDED.video_events_play_time_percent_75;
        """
        pg_hook.run(query, True)
        increment_metric('airflow_data_pushed.increment', env_type,
                         FLOW_NAME, len(batch_rows), [f"table:{REPORTS_TABLE_NAME}",
                           f"client:{company_id}", f'run_number:{current_run_number}'])


def _get_report_columns(report):
    column_names = []
    for col in report["reportData"]["columns"]:
        if col["key"] == 'videoEventsPlayTimePercent':
            column_name = f'{col["key"]}{col["specs"]["videoEventType"][:2]}'
        else:
            column_name = col["key"]
        column_names.append(column_name)
    return column_names


def load_report(query, **kwargs):
    company_id = query.profile.get('company_id')
    loc = hitting_oper_location_api(
        operation_location=query.report_location,
        access_token=query.access_token, integration=query.id,
        query_type=query.type, company_id=company_id, **kwargs)
    logging.info(f"hitting_oper_location_api res {loc}")
    report = hitting_location_api(location=loc, access_token=query.access_token,
                                  integration=query.id, query_type=query.type,
                                  company_id=company_id, **kwargs)
    logging.info(f"hitting_location_api res {str(report)[:100]}")
    if 'reportData' not in report:
        logging.warning(f"Empty report but status is `succeeded`."
                        f" res {str(report)[:5000]}")
        return True
    rows = report["reportData"]["rows"]
    cols = _get_report_columns(report)
    result_df = pd.DataFrame(columns=cols, data=rows)
    if result_df.empty:
        logging.info("Received empty report. Exiting")
        return True
    result_df["tracking"] = query.tracking_filters
    result_df["advertiser"] = query.advertiser
    del result_df["campaign"]
    result_df.columns = ['campaignID', 'mediaID', 'date', 'bannerID', 'lineItemID', 'tagID',
        'conversions_post_imp', 'conversions_post_click', 'conversions', 'clicks', 'impressions', 'videoPlayStartCount',
        'videoCompleteCount', 'videoEventsPlayTimePercent25',
        'videoEventsPlayTimePercent50', 'videoEventsPlayTimePercent75', 'tracking', 'advertiser']

    result_df = result_df[[
        'date', 'bannerID', 'mediaID', 'campaignID', 'lineItemID', 'tracking', "tagID",
        'conversions_post_imp', 'conversions_post_click', "conversions", 'advertiser', 'clicks', 'impressions',
        'videoPlayStartCount', 'videoCompleteCount', 'videoEventsPlayTimePercent25',
        'videoEventsPlayTimePercent50', 'videoEventsPlayTimePercent75'
    ]]
    # rename cols to match pg
    result_df.columns = [
        'date', 'banner_id', 'media_id', 'campaign_id', 'line_item_id', 'tracker_id', "tag_id",
        'conversions_post_imp', 'conversions_post_click', "conversions", 'advertiser_id', 'clicks', 'impressions',
        'video_play_start_count', 'video_complete_count', 'video_events_play_time_percent_25',
        'video_events_play_time_percent_50', 'video_events_play_time_percent_75'
    ]
    # Upsert report into df
    put_reports_to_db(result_df, company_id, **kwargs)
    return True


@set_execution_dates(dates)
def load_reports(queries_data: List[dict], **kwargs):
    logging.info(f"load_reports {len(queries_data)} queries total")
    execution_status = {}
    for i_data, data in enumerate(queries_data):
        logging.info(f"i_data={i_data} query_data={data}")
        query = QueryData(**data)
        # getting operation_location
        try:
            success = load_report(query, **kwargs)
        except Exception as e:
            logging.error(f"Exception {e} {FLOW_NAME} when process {query}. {traceback.format_exc()}")
            success = False
        if success and query.type == 'historical':
            log_hist_request(query.campaign_details, query.company_id)
        # log count
        execution_status_key = (query.type, 'success' if success else 'fail')
        execution_status[execution_status_key] = \
            execution_status.get(execution_status_key, 0) + 1
    execution_status_str = "\n".join([
        f"{key} -> {execution_status[key]}"
        for key in sorted(execution_status.keys())
    ])
    logging.info(f"Execution status counts:\n{execution_status_str}")


def run_reports(queries_data: List[dict], **kwargs):
    logging.info(f"run_reports num queries = {len(queries_data)}")
    result_queries = []
    for i_data, data in enumerate(queries_data):
        logging.info(f"i_data={i_data} query_data={data}")
        query = QueryData(**data)
        try:
            oper_loc = hitting_stats_api(
                body=query.body, access_token=query.access_token,
                integration=query.id, query_type=query.type,
                company_id=query.profile.get('company_id'),
                **kwargs
            )
            logging.info(f"operation_location = {oper_loc}")
            query.report_location = oper_loc
            query.report_created_at = time.time()
            result_queries.append(query)
        except Exception as e:
            msg = f"{FLOW_NAME} failed when ordering report for {data}"
            logging.error(msg)
            logging.error(f"Exception {e} {traceback.format_exc()}")
    return [
        query.to_dict() for query in result_queries
    ]


with DAG(FLOW_NAME,
         description="adform flow",
         schedule_interval=None,
         tags=['ingestion_flow'],
         params=DagConfigSetter(FLOW_NAME).get_params(),
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False) as dag:
    t1 = PythonOperator(
        python_callable=generate_queries,
        task_id="generate_queries",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id="run_reports",
        python_callable=run_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"queries_data": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="load_reports",
        python_callable=load_reports,
        on_failure_callback=failure_callback,
        op_kwargs={"queries_data": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> cleanup_xcoms
