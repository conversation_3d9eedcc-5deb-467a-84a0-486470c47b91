snowflake_dbt_kk:
  target: dev
  outputs:
    dev:
      type: snowflake
      account: "twodkrt-se24955"

      # User/password auth
      user: "RUSLANKUPRIYANOV"
      password: "*******************"

      role: "ACCOUNTADMIN"
      database: "DATAMART"
      warehouse: "TEST_WAREHOUSE"
      schema: "tests"
      threads: 5
      client_session_keep_alive: False
      query_tag: ""

      # optional
      connect_retries: 0 # default 0
      connect_timeout: 10 # default: 10
      retry_on_database_errors: False # default: false
      retry_all: False  # default: false
      reuse_connections: True # default: True if client_session_keep_alive is False, otherwise None

snowflake_dbt:
  target: dev
  outputs:
    dev:
      type: snowflake
      account: "TWODKRT-NON_PROD"
      user: "<EMAIL>"

      # Key pair auth
      private_key_path: "/home/<USER>/work/airflow/airflow/dags/dbt/snowflake_dbt/rsa_key.p8"  # Update this path

      role: "TEST_DBT_USER"
      database: "TEST_STAGE"
      warehouse: "TEST_WRITE_WAREHOUSE"
      schema: "MAIN"
      threads: 8
      client_session_keep_alive: False
#      query_tag: [anything]
 
      # optional
      connect_retries: 0 # default 0
      connect_timeout: 10 # default: 10
      retry_on_database_errors: False # default: false
      retry_all: False  # default: false
      reuse_connections: True #
