
{% set feed_table = ref("bridge_adform_media_campaign_mapping_transform") %}
{% set target_table = safe_source("CORE", "BRIDGE_ADFORM_MEDIA_CAMPAIGN_MAPPING_CORE") %}


WITH new_and_updated_records AS (
    {% if target_table %}
        -- Incremental logic: historize data based on existing target table
        {{ historize_dim(
            feed_table=feed_table,
            target_table=target_table,
            key_columns=[
                'adformcampaignmapping_id',
                'adformmediamapping_id'
            ],
            scd_type='2a'
        ) }}
    {% else %}
        -- Initial load logic: Use init_hist_dim_table to initialize the table structure
        {{ init_hist_dim_table(feed_table) }}
    {% endif %}
)

SELECT * FROM new_and_updated_records
