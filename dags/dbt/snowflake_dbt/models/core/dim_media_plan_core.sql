
{% set feed_table = ref("dim_media_plan_transform") %}
{% set target_table = safe_source("CORE", "DIM_MEDIA_PLAN_CORE") %}




WITH new_and_updated_records AS (

    {% if target_table %}
        -- Incremental logic: historize data based on existing target table
        {{ historize_dim(
            feed_table=feed_table,
            target_table=target_table,
            key_columns=['media_plan_id'],
            scd_type='2a'
        ) }}
    {% else %}
        -- Initial load logic: Simply load all records from the feed table
        {{ init_hist_dim_table(feed_table) }}
    {% endif %}

)

SELECT * FROM new_and_updated_records