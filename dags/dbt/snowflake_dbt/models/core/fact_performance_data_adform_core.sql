
{% set feed_table = ref("fact_performance_adform_data_transform") %}
{% set target_table = safe_source("CORE", "FACT_PERFORMANCE_DATA_ADFORM_CORE") %}



{#Uses too many columns for histrorisation#}
WITH new_and_updated_records AS (
    {% if target_table %}
        -- Incremental logic: historize data based on existing target table
        {{ historize_fact(
        feed_table=feed_table,
        target_table=target_table,
        key_columns=[
        "fact_date",
        "external_campaign_id",
        "external_media_id",
        "advertiser_id",
        "external_tracking_filter_id",
        "external_tag_id",
        "fact_name"
        ]
    ) }}
    {% else %}
        -- Initial load logic: Use init_hist_dim_table to initialize the table structure
        {{ init_hist_fact_table(feed_table) }}
    {% endif %}
)

SELECT * FROM new_and_updated_records
