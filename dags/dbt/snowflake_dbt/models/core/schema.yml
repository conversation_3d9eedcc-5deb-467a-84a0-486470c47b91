version: 2

models:
  - name: dim_media_plan_core
    description: "Core dimension table for media plans with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_channel_core
    description: "Core dimension table for channels with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_platform_core
    description: "Core dimension table for platforms with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_tile_core
    description: "Core dimension table for tiles with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_journey_core
    description: "Core dimension table for journeys with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_media_row_core
    description: "Core dimension table for media rows with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_ad_core
    description: "Core dimension table for ads with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_ad_group_core
    description: "Core dimension table for ad groups with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_external_campaign_core
    description: "Core dimension table for external campaigns with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: bridge_platform_channel_core
    description: "Core bridge table linking platforms and channels with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: bridge_ad_ad_group_core
    description: "Core bridge table linking ads and ad groups with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: bridge_media_plan_journey_core
    description: "Core bridge table linking media plans and journeys with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: bridge_ad_group_external_campaign_core
    description: "Core bridge table linking ad groups and external campaigns with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: bridge_media_row_platform_core
    description: "Core bridge table linking media rows and platforms with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: bridge_tile_external_campaign_core
    description: "Core bridge table linking tiles and external campaigns with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: bridge_channel_media_plan_core
    description: "Core bridge table linking channels and media plans with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: fact_performance_data_core
    description: "Core fact table for performance data with historization"
    columns:
      - name: updated_at
        description: Timestamp when the record was last updated
        tests:
          - not_null

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [true, false]

  - name: dim_conversion_core
    description: "Core dim conversion table with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [ true, false ]

  - name: dim_creative_core
    description: "Core dimension table for creatives with historization"
    columns:
      - name: date_from
        description: Timestamp when the record became active
        tests:
          - not_null

      - name: date_to
        description: Timestamp when the record became inactive (NULL for active records)

      - name: is_active
        description: Boolean flag indicating if the record is currently active
        tests:
          - not_null
          - accepted_values:
              values: [ true, false ]