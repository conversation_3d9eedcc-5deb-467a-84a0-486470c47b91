# models/schema.yml

version: 2

models:
#  - name: fact_performance_data_core
#    tests:
#      - dbt_utils.equality:
#          compare_model: ref('mock_expected_result_fact_performance_data_core')
#          config:
#            vars:
#              use_mocks: true
#              mock_timestamp_value: '2024-11-01 12:00:00'

    
    
  - name: bridge_ad_ad_group_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_bridge_ad_ad_group_transform')
    
    
  - name: bridge_channel_media_plan_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_bridge_channel_media_plan_transform')
    
    
  - name: bridge_media_plan_journey_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_bridge_media_plan_journey_transform')
    
    
  - name: bridge_media_row_platform_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_bridge_media_row_platform_transform')
    
    
  - name: bridge_platform_channel_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_bridge_platform_channel_transform')
    
    
  - name: bridge_tile_external_campaign_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_bridge_tile_external_campaign_transform')
    
    
  - name: dim_ad_group_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_ad_group_transform')
    
    
  - name: dim_ad_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_ad_transform')
    
    
  - name: dim_channel_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_channel_transform')
    
    
  - name: dim_external_campaign_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_external_campaign_transform')
    
    
  - name: dim_journey_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_journey_transform')
    
    
  - name: dim_media_plan_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_media_plan_transform')
    
    
  - name: dim_media_row_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_media_row_transform')
    
    
  - name: dim_platform_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_platform_transform')
    
    
  - name: dim_tile_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_dim_tile_transform')
    
    
  - name: fact_performance_data_transform
    tests:
      - dbt_utils.equality:
          compare_model: ref('mock_expected_result_fact_performance_data_transform')
