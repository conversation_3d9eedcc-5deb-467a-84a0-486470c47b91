WITH
  LATEST_CAMPAIGN_COLLECTION AS (
    SELECT
      *
    FROM
      DATINTELL.CAMPAIGN_CAMPAIGNCOLLECTION
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'campaign_campaigncollection'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_MEDIA_PLAN_ITEM AS (
    SELECT
      *
    FROM
      DATINTELL.BUILDER_MODE_MEDIAPLANITEM
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'builder_mode_mediaplanitem'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CAMPAIGN_CCAMPAIGN AS (
    SELECT
      *
    FROM
      DATINTELL.CAMPAIGN_CAMPAIGN
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'campaign_campaign'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_MEDIA_PLAN AS (
    SELECT
      *
    FROM
      DATINTELL.BUILDER_MODE_MEDIAPLAN
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'builder_mode_mediaplan'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_MEDIA_MIX AS (
    SELECT
      *
    FROM
      DATINTELL.CAMPAIGN_MEDIAMIX
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'campaign_mediamix'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  DATINTELL_CHANNEL_LEVEL AS (
    SELECT
      "id",
      "name",
      "media_plan_id"
    FROM
      LATEST_MEDIA_PLAN_ITEM
    WHERE
      "type" = 'channel'
  ),
  DATINTELL_PLATFORM_LEVEL AS (
    SELECT
      "id",
      "name",
      "parent_id"
    FROM
      LATEST_MEDIA_PLAN_ITEM
    WHERE
      "type" IN ('media_ad_network', 'platform')
  ),
  DATINTELL_ROW_LEVEL AS (
    SELECT
      "id",
      "name",
      "fields",
      "parent_id",
      "campaign_id",
      "overridden_currency",
      "is_locked_for_recommendations"
    FROM
      LATEST_MEDIA_PLAN_ITEM
    WHERE
      "type" = 'campaign'
  )
SELECT
  CC."company_id",
  CC."id" AS "journey_id",
  CC."name" AS "journey_name",
  CC."date_from" AS "journey_date_from",
  CC."date_to" AS "journey_date_to",
  CC."budget" AS "journey_budget",
  CC."ad_server" AS "journey_ad_server",
  CC."is_for_test" AS "journey_is_for_test",
  CC."is_deleted",
  MP."id" AS "media_plan_id",
  MP."name" AS "media_plan_name",
  MX."date_from" AS "media_plan_date_from",
  MX."date_to" AS "media_plan_date_to",
  MX."budget" AS "media_plan_budget",
  DCL."id" AS "channel_id",
  DCL."name" AS "channel_name",
  DPL."id" AS "platform_id",
  DPL."name" AS "platform_name",
  T."id" AS "tile_id",
  T."name" AS "tile_name",
  T."date_from" AS "tile_date_from",
  T."date_to" AS "tile_date_to",
  DRL."id" AS "media_row_id",
  DRL."name" AS "media_row_name",
  COALESCE(DRL."overridden_currency", CC."currency") AS "mediarow_currency",
  DRL."fields":budget::TEXT AS "media_row_budget",
  DRL."fields":date_to::TEXT AS "media_row_date_to",
  DRL."fields":date_from::TEXT AS "media_row_date_from",
  DRL."is_locked_for_recommendations"
FROM
  LATEST_CAMPAIGN_COLLECTION AS CC
  JOIN LATEST_MEDIA_PLAN AS MP ON MP."campaign_collection_id" = CC."id"
  JOIN LATEST_MEDIA_MIX AS MX ON MX."id" = MP."media_mix_id"
  JOIN DATINTELL_CHANNEL_LEVEL AS DCL ON DCL."media_plan_id" = MP."id"
  JOIN DATINTELL_PLATFORM_LEVEL AS DPL ON DPL."parent_id" = DCL."id"
  JOIN DATINTELL_ROW_LEVEL AS DRL ON DRL."parent_id" = DPL."id"
  JOIN LATEST_CAMPAIGN_CCAMPAIGN AS T ON T."id" = DRL."campaign_id"