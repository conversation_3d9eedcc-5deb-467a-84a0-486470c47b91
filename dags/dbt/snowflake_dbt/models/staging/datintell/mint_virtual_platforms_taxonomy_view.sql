
WITH
  LATEST_GHOST_CAMPAIGN AS (
    SELECT
      *
    FROM
      DATINTELL.CAMPAIGN_GHOSTCAMPAIGN
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'campaign_ghostcampaign'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_PRICING_ITEM AS (
    SELECT
      *
    FROM
      DATINTELL.CAMPAIGN_GHOSTCAMPAIGNPRICINGITEM
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMES<PERSON>MP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'campaign_ghostcampaignpricingitem'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  DATINTELL_TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "media_plan_date_from",
      "media_plan_date_to",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  )
SELECT DISTINCT
  DT."company_id",
  DT."journey_id",
  DT."journey_name",
  DT."media_plan_id",
  DT."media_plan_name",
  DT."media_plan_date_from",
  DT."media_plan_date_to",
  DT."channel_id",
  DT."channel_name",
  DT."platform_id",
  DT."platform_name",
  DT."tile_id",
  DT."tile_name",
  DT."media_row_id",
  DT."media_row_name",
  DT."media_row_budget",
  DT."media_row_date_to",
  DT."media_row_date_from",
  DT."mediarow_currency",
  PI."id" AS "pricing_item_id",
  PI."date_from" AS "pricing_item_date_from",
  PI."date_to" AS "pricing_item_date_to",
  PI."pricing_type" AS "pricing_item_type",
  PI."name" AS "pricing_item_name",
  PI."user_defined_id" AS "pricing_item_user_defined_id"
FROM
  DATINTELL_TAXONOMY as DT
  JOIN LATEST_GHOST_CAMPAIGN AS GK ON DT."tile_id" = GK."campaign_id"
  JOIN LATEST_PRICING_ITEM AS PI ON GK."id" = PI."campaign_id"