
WITH
  --<PERSON><PERSON><PERSON><PERSON>
  DATINTELL_TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "media_plan_date_from",
      "media_plan_date_to",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  DATINTEL_MEDIA_ROW_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "media_plan_date_from",
      "media_plan_date_to",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name",
      NULL AS "platform_tile_id",
      NULL AS "platform_campaign_internal_id",
      NULL AS "platform_campaign_external_id",
      NULL AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      "mediarow_currency" AS "currency",
      'media_row' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      DATINTELL_TAXONOMY
  ),
  DATINTEL_PLATFORM_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "media_plan_date_from",
      "media_plan_date_to",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      NULL AS "media_row_id",
      NULL AS "media_row_name",
      NULL AS "media_row_budget",
      NULL AS "media_row_date_to",
      NULL AS "media_row_date_from",
      NULL AS "mediarow_currency",
      NULL AS "tile_id",
      NULL AS "tile_name",
      NULL AS "platform_tile_id",
      NULL AS "platform_campaign_internal_id",
      NULL AS "platform_campaign_external_id",
      NULL AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      "mediarow_currency" AS "currency",
      'platform' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      DATINTELL_TAXONOMY
  ),
  DATINTEL_PRICING_ITEM_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "media_plan_date_from",
      "media_plan_date_to",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name",
      NULL AS "platform_tile_id",
      CAST("pricing_item_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("pricing_item_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("pricing_item_name" AS TEXT) AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      "mediarow_currency" AS "currency",
      'pricing_item' AS "granularity_level",
      "pricing_item_type" AS "pricing_item_type"
    FROM
      {{ ref('mint_virtual_platforms_taxonomy_view') }}
  ),
  -- Google ads
  ADWORDS_AD_LEVEL AS (
    SELECT DISTINCT
      "google_ads_tile_id",
      CAST("google_ads_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("google_ads_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("google_ads_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("google_ads_internal_adgroup_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("google_ads_external_adgroup_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("google_ads_adgroup_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("google_ads_internal_ad_id" AS TEXT) AS "ad_asset_internal_id",
      CAST("google_ads_external_ad_id" AS TEXT) AS "ad_asset_external_id",
      CAST("google_ads_ad_name" AS TEXT) AS "ad_asset",
      CAST("google_ads_currency_code" AS TEXT) AS "currency",
      'ad' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('google_ads_taxonomy_view') }}
    WHERE
      "google_ads_external_ad_id" IS NOT NULL
  ),
  ADWORDS_ADGROUP_LEVEL AS (
    SELECT DISTINCT
      "google_ads_tile_id",
      CAST("google_ads_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("google_ads_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("google_ads_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("google_ads_internal_adgroup_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("google_ads_external_adgroup_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("google_ads_adgroup_name" AS TEXT) AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("google_ads_currency_code" AS TEXT) AS "currency",
      'adgroup' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('google_ads_taxonomy_view') }}
    WHERE
      "google_ads_external_adgroup_id" IS NOT NULL
  ),
  ADWORDS_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "google_ads_tile_id",
      CAST("google_ads_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("google_ads_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("google_ads_campaign_name" AS TEXT) AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("google_ads_currency_code" AS TEXT) AS "currency",
      'campaign' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('google_ads_taxonomy_view') }}
    WHERE
      "google_ads_external_campaign_id" IS NOT NULL
  ),
    ADWORDS_PERFMAX_LEVEL AS (
    SELECT DISTINCT
      "google_ads_tile_id",
      CAST("google_ads_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("google_ads_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("google_ads_campaign_name" AS TEXT) AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("google_ads_currency_code" AS TEXT) AS "currency",
      'perfmax' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('google_ads_taxonomy_view') }}
    WHERE
      "google_ads_external_campaign_id" IS NOT NULL
      AND "google_ads_external_adgroup_id" IS NULL
  ),
  -- DV360
  DV360_LI_LEVEL AS (
    SELECT DISTINCT
      "dv360_tile_id",
      CAST("dv360_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("dv360_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("dv360_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("dv360_internal_insertion_order_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("dv360_external_insertion_order_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("dv360_insertion_order_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("dv360_internal_li_id" AS TEXT) AS "ad_asset_internal_id",
      CAST("dv360_external_li_id" AS TEXT) AS "ad_asset_external_id",
      CAST("dv360_li_name" AS TEXT) AS "ad_asset",
      CAST("dv360_currency_code" AS TEXT) AS "currency",
      'ad' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('dv360_taxonomy_view') }}
    WHERE
      "dv360_external_li_id" IS NOT NULL
  ),
  DV360_IO_LEVEL AS (
    SELECT DISTINCT
      "dv360_tile_id",
      CAST("dv360_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("dv360_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("dv360_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("dv360_internal_insertion_order_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("dv360_external_insertion_order_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("dv360_insertion_order_name" AS TEXT) AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("dv360_currency_code" AS TEXT) AS "currency",
      'adgroup' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('dv360_taxonomy_view') }}
    WHERE
      "dv360_external_insertion_order_id" IS NOT NULL
  ),
  DV360_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "dv360_tile_id",
      CAST("dv360_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("dv360_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("dv360_campaign_name" AS TEXT) AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("dv360_currency_code" AS TEXT) AS "currency",
      'campaign' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('dv360_taxonomy_view') }}
    WHERE
      "dv360_external_campaign_id" IS NOT NULL
  ),
  -- Meta
  META_AD_LEVEL AS (
    SELECT DISTINCT
      "meta_tile_id",
      CAST("meta_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("meta_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("meta_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("meta_internal_adset_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("meta_external_adset_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("meta_adset_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("meta_internal_ad_id" AS TEXT) AS "ad_asset_internal_id",
      CAST("meta_external_ad_id" AS TEXT) AS "ad_asset_external_id",
      CAST("meta_ad_name" AS TEXT) AS "ad_asset",
      CAST("meta_currency_code" AS TEXT) AS "currency",
      'ad' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('meta_taxonomy_view') }}
    WHERE
      "meta_external_ad_id" IS NOT NULL
  ),
  META_ADSET_LEVEL AS (
    SELECT DISTINCT
      "meta_tile_id",
      CAST("meta_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("meta_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("meta_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("meta_internal_adset_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("meta_external_adset_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("meta_adset_name" AS TEXT) AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("meta_currency_code" AS TEXT) AS "currency",
      'adgroup' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('meta_taxonomy_view') }}
    WHERE
      "meta_external_adset_id" IS NOT NULL
  ),
  META_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "meta_tile_id",
      CAST("meta_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("meta_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("meta_campaign_name" AS TEXT) AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("meta_currency_code" AS TEXT) AS "currency",
      'campaign' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('meta_taxonomy_view') }}
    WHERE
      "meta_external_campaign_id" IS NOT NULL
  ),
  --Zemanta
  ZEMANTA_AD_LEVEL AS (
    SELECT DISTINCT
      "zemanta_tile_id",
      CAST("zemanta_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("zemanta_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("zemanta_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("zemanta_internal_adgrp_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("zemanta_external_adgrp_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("zemanta_adgrp_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("zemanta_internal_ad_id" AS TEXT) AS "ad_asset_internal_id",
      CAST("zemanta_external_ad_id" AS TEXT) AS "ad_asset_external_id",
      CAST("zemanta_ad_name" AS TEXT) AS "ad_asset",
      CAST("zemanta_currency_code" AS TEXT) AS "currency",
      'ad' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('zemanta_taxonomy_view') }}
    WHERE
      "zemanta_external_ad_id" IS NOT NULL
  ),
  ZEMANTA_ADGROUP_LEVEL AS (
    SELECT DISTINCT
      "zemanta_tile_id",
      CAST("zemanta_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("zemanta_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("zemanta_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("zemanta_internal_adgrp_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("zemanta_external_adgrp_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("zemanta_adgrp_name" AS TEXT) AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("zemanta_currency_code" AS TEXT) AS "currency",
      'adgroup' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('zemanta_taxonomy_view') }}
    WHERE
      "zemanta_external_adgrp_id" IS NOT NULL
  ),
  ZEMANTA_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "zemanta_tile_id",
      CAST("zemanta_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("zemanta_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("zemanta_campaign_name" AS TEXT) AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("zemanta_currency_code" AS TEXT) AS "currency",
      'campaign' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('zemanta_taxonomy_view') }}
    WHERE
      "zemanta_external_campaign_id" IS NOT NULL
  ),
  --Microsoft Ads
  MICROSOFT_ADS_AD_LEVEL AS (
    SELECT DISTINCT
      "microsoft_ads_tile_id",
      CAST("microsoft_ads_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("microsoft_ads_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("microsoft_ads_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("microsoft_ads_internal_adgrp_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("microsoft_ads_external_adgrp_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("microsoft_ads_adgrp_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("microsoft_ads_internal_ad_id" AS TEXT) AS "ad_asset_internal_id",
      CAST("microsoft_ads_external_ad_id" AS TEXT) AS "ad_asset_external_id",
      CAST("microsoft_ads_ad_name" AS TEXT) AS "ad_asset",
      CAST("microsoft_ads_currency_code" AS TEXT) AS "currency",
      'ad' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('microsoft_ads_taxonomy_view') }}
    WHERE
      "microsoft_ads_external_ad_id" IS NOT NULL
  ),
  MICROSOFT_ADS_ADGROUP_LEVEL AS (
    SELECT DISTINCT
      "microsoft_ads_tile_id",
      CAST("microsoft_ads_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("microsoft_ads_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("microsoft_ads_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("microsoft_ads_internal_adgrp_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("microsoft_ads_external_adgrp_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("microsoft_ads_adgrp_name" AS TEXT) AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("microsoft_ads_currency_code" AS TEXT) AS "currency",
      'adgroup' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('microsoft_ads_taxonomy_view') }}
    WHERE
      "microsoft_ads_external_adgrp_id" IS NOT NULL
  ),
  MICROSOFT_ADS_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "microsoft_ads_tile_id",
      CAST("microsoft_ads_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("microsoft_ads_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("microsoft_ads_campaign_name" AS TEXT) AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("microsoft_ads_currency_code" AS TEXT) AS "currency",
      'campaign' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('microsoft_ads_taxonomy_view') }}
    WHERE
      "microsoft_ads_external_campaign_id" IS NOT NULL
  ),
  --Omni
  OMNI_AD_LEVEL AS (
    SELECT DISTINCT
      "omni_tile_id",
      CAST("omni_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("omni_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("omni_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("omni_internal_adgrp_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("omni_external_adgrp_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("omni_adgrp_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("omni_internal_ad_id" AS TEXT) AS "ad_asset_internal_id",
      CAST("omni_external_ad_id" AS TEXT) AS "ad_asset_external_id",
      CAST("omni_ad_name" AS TEXT) AS "ad_asset",
      CAST("omni_currency_code" AS TEXT) AS "currency",
      'ad' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('omni_taxonomy_view') }}
    WHERE
      "omni_external_ad_id" IS NOT NULL
  ),
  OMNI_ADGROUP_LEVEL AS (
    SELECT DISTINCT
      "omni_tile_id",
      CAST("omni_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("omni_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("omni_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("omni_internal_adgrp_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("omni_external_adgrp_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("omni_adgrp_name" AS TEXT) AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("omni_currency_code" AS TEXT) AS "currency",
      'adgroup' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('omni_taxonomy_view') }}
  ),
  OMNI_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "omni_tile_id",
      CAST("omni_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("omni_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("omni_campaign_name" AS TEXT) AS "platform_campaign_name",
      NULL AS "platform_campaign_internal_group_id",
      NULL AS "platform_campaign_external_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_internal_id",
      NULL AS "ad_asset_external_id",
      NULL AS "ad_asset",
      CAST("omni_currency_code" AS TEXT) AS "currency",
      'campaign' AS "granularity_level",
      NULL AS "pricing_item_type"
    FROM
      {{ ref('omni_taxonomy_view') }}
  ),
  UNIFIED_TAXONOMY AS (
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN ADWORDS_AD_LEVEL AS AADL ON DT."tile_id" = AADL."google_ads_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN ADWORDS_ADGROUP_LEVEL AS AGRPL ON DT."tile_id" = AGRPL."google_ads_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN ADWORDS_CAMPAIGN_LEVEL AS ACL ON DT."tile_id" = ACL."google_ads_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN ADWORDS_PERFMAX_LEVEL AS ACL ON DT."tile_id" = ACL."google_ads_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN DV360_LI_LEVEL AS DLIL ON DT."tile_id" = DLIL."dv360_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN DV360_IO_LEVEL AS DIOL ON DT."tile_id" = DIOL."dv360_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN DV360_CAMPAIGN_LEVEL AS DCL ON DT."tile_id" = DCL."dv360_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN META_AD_LEVEL AS MADL ON DT."tile_id" = MADL."meta_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN META_ADSET_LEVEL AS MGRPL ON DT."tile_id" = MGRPL."meta_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN META_CAMPAIGN_LEVEL AS MCL ON DT."tile_id" = MCL."meta_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN ZEMANTA_AD_LEVEL AS ZADL ON DT."tile_id" = ZADL."zemanta_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN ZEMANTA_ADGROUP_LEVEL AS ZGRPL ON DT."tile_id" = ZGRPL."zemanta_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN ZEMANTA_CAMPAIGN_LEVEL AS ZCL ON DT."tile_id" = ZCL."zemanta_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN MICROSOFT_ADS_AD_LEVEL AS MAADL ON DT."tile_id" = MAADL."microsoft_ads_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN MICROSOFT_ADS_ADGROUP_LEVEL AS MAGRPL ON DT."tile_id" = MAGRPL."microsoft_ads_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN MICROSOFT_ADS_CAMPAIGN_LEVEL AS MACL ON DT."tile_id" = MACL."microsoft_ads_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN OMNI_AD_LEVEL AS OADL ON DT."tile_id" = OADL."omni_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN OMNI_ADGROUP_LEVEL AS OGRPL ON DT."tile_id" = OGRPL."omni_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN OMNI_CAMPAIGN_LEVEL AS OCL ON DT."tile_id" = OCL."omni_tile_id"
    UNION ALL
    SELECT
      *
    FROM
      DATINTEL_MEDIA_ROW_LEVEL
    UNION ALL
    SELECT
      *
    FROM
      DATINTEL_PLATFORM_LEVEL
    UNION ALL
    SELECT
      *
    FROM
      DATINTEL_PRICING_ITEM_LEVEL
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "media_plan_date_from",
  "media_plan_date_to",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  "media_row_budget",
  "media_row_date_to",
  "media_row_date_from",
  "tile_id",
  "tile_name",
  "platform_campaign_internal_id",
  "platform_campaign_external_id",
  "platform_campaign_name",
  "platform_campaign_internal_group_id",
  "platform_campaign_external_group_id",
  "platform_campaign_group_name",
  "ad_asset_internal_id",
  "ad_asset_external_id",
  "ad_asset",
  COALESCE("currency", "mediarow_currency") AS "currency",
  "granularity_level",
  "pricing_item_type"
FROM
  UNIFIED_TAXONOMY