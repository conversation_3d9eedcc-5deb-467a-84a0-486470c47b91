
WITH LATEST_INTEGRATION AS (
    SELECT * FROM  OMNI_CONNECTOR.INTEGRATIONS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'OMNI_CONNECTOR'
              AND "TABLE_NAME" = 'integrations'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_PLATFORM_SETTINGS AS (
    SELECT * FROM  OMNI_CONNECTOR.TILE_SETTINGS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'OMNI_CONNECTOR'
              AND "TABLE_NAME" = 'tile_settings'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_CAMPAIGN AS (
    SELECT * FROM  OMNI_CONNECTOR.CAMPAIGNS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'OMNI_CONNECTOR'
              AND "TABLE_NAME" = 'campaigns'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_ADGROUP AS (
    SELECT * FROM  OMNI_CONNECTOR.AD_GROUPS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'OMNI_CONNECTOR'
              AND "TABLE_NAME" = 'ad_groups'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_AD AS (
    SELECT * FROM  OMNI_CONNECTOR.ADS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'OMNI_CONNECTOR'
              AND "TABLE_NAME" = 'ads'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
)

SELECT 
  PS."tile_id" AS "omni_tile_id",
  INTGR."id" AS "omni_integration_id",
  INTGR."currency" AS "omni_currency_code",
  INTGR."platform" AS "omni_platform_name",
  CAMP."id" AS "omni_internal_campaign_id",
  CAMP."external_id" AS "omni_external_campaign_id",
  CAMP."name" AS "omni_campaign_name",
  CAMP."start_date" AS "omni_campaign_date_from",
  CAMP."end_date" AS "omni_campaign_date_to",  
  CAMP."lifetime_budget" AS "omni_campaign_lifetime_budget",
  CAMP."daily_budget" AS "omni_campaign_daily_budget",
  CAMP."execution_status" AS "omni_campaign_internal_status",
  CAMP."external_execution_status" AS "omni_campaign_external_status",
  CAMP."external_type" AS "omni_campaign_type", 
  ADGRP."id" AS "omni_internal_adgrp_id",
  ADGRP."external_id" AS "omni_external_adgrp_id",
  ADGRP."name" AS "omni_adgrp_name",
  ADGRP."start_date" AS "omni_adgrp_date_from",
  ADGRP."end_date" AS "omni_adgrp_date_to",  
  ADGRP."lifetime_budget" AS "omni_adgrp_lifetime_budget",
  ADGRP."daily_budget" AS "omni_adgrp_daily_budget",  
  ADGRP."execution_status" AS "omni_adgrp_internal_status",
  ADGRP."external_execution_status" AS "omni_adgrp_external_status",
  ADGRP."external_type" AS "omni_adgrp_type",  
  AD."id" AS "omni_internal_ad_id",
  AD."external_id" AS "omni_external_ad_id",
  AD."name" AS "omni_ad_name",
  AD."start_date" AS "omni_ad_date_from",
  AD."end_date" AS "omni_ad_date_to",  
  AD."lifetime_budget" AS "omni_ad_lifetime_budget",
  AD."daily_budget" AS "omni_ad_daily_budget",  
  AD."execution_status" AS "omni_ad_internal_status",
  AD."external_execution_status" AS "omni_ad_external_status",
  AD."external_type" AS "omni_ad_type"
FROM
  LATEST_INTEGRATION AS INTGR
  JOIN LATEST_PLATFORM_SETTINGS AS PS ON INTGR."id" = PS."integration_id"
  JOIN LATEST_CAMPAIGN AS CAMP ON PS."id" = CAMP."tile_settings_id" and INTGR."platform" = CAMP."platform"
  LEFT JOIN LATEST_ADGROUP ADGRP ON CAMP."id" = ADGRP."campaign_id" AND CAMP."platform" = ADGRP."platform"
  LEFT JOIN LATEST_AD AD ON ADGRP."id" = AD."ad_group_id" AND ADGRP."platform" = AD."platform"