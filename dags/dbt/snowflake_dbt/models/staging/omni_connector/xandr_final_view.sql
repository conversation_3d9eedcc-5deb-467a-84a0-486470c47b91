
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  "tile_id",
  "tile_name",
  "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset_id",
  "ad_asset",
  "currency",
  "data_source",
  "data_source_details",
  "date",
  "impressions",
  "clicks",
  "spend",
  "completed_views_first",
  "completed_views_mid",
  "completed_views_third",
  "completed_views_full",
  "video_start",
  "conversion_metric",
  "conversions",
    NULL as "in_app_actions",
    NULL as "platform_conversions",
    Null as "platform_installations",
    Null as "search_impression_share",
    Null as "total_eligible_impressions",
    Null as "video_views"
FROM
  {{ ref('xandr_base_ad_level_report_case_view') }}