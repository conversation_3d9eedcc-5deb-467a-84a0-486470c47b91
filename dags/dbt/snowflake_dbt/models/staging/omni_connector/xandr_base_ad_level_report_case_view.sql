
WITH
  LATEST_BASE_XANDR_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_XANDR_PERFORMANCE_V3
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_xandr_performance_v3'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  XANDR_AD_LEVEL AS (
    SELECT DISTINCT
      "omni_tile_id",
      "omni_external_campaign_id",
      "omni_campaign_name",
      "omni_external_adgrp_id",
      "omni_adgrp_name",
      "omni_currency_code"
    FROM
      {{ ref('omni_taxonomy_view') }}
    WHERE
      "omni_external_adgrp_id" IS NOT NULL
      AND "omni_platform_name" = 'xandr_integrated'
  ),
  XANDR_BASE_REPORTS AS (
    SELECT
      "date",
      "insertion_order_id",
      "line_item_id",
      "creative_id",
      "creative_name",
      CASE
        WHEN "conversion_name" = '--' THEN NULL
        ELSE "conversion_name"
      END AS "conversion_name",
      "currency",
      SUM("impressions") AS "impressions",
      SUM("clicks") AS "clicks",
      SUM("spend") As "spend",
      SUM("completed_views_first") AS "completed_views_first",
      SUM("completed_views_mid") AS "completed_views_mid",
      SUM("completed_views_third") AS "completed_views_third",
      SUM("completed_views_full") AS "completed_views_full",
      SUM("video_start") AS "video_start",
      SUM("platform_conversions") AS "conversions"
    FROM
      LATEST_BASE_XANDR_REPORTS
    GROUP BY
      "date",
      "insertion_order_id",
      "line_item_id",
      "creative_id",
      "creative_name",
      "conversion_name",
      "currency"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  TX."tile_id",
  "tile_name",
  CAST("omni_external_campaign_id" AS TEXT) AS "platform_campaign_id",
  CAST("omni_campaign_name" AS TEXT) AS "platform_campaign_name",
  CAST("omni_external_adgrp_id" AS TEXT) AS "platform_campaign_group_id",
  CAST("omni_adgrp_name" AS TEXT) AS "platform_campaign_group_name",
  CAST("creative_id" AS TEXT) AS "ad_asset_id",
  CAST("creative_name" AS TEXT) AS "ad_asset",
  COALESCE(
    "currency",
    "omni_currency_code",
    "mediarow_currency"
  ) AS "currency",
  'Xandr' AS "data_source",
  'reports_xandr_performance_v3' AS "data_source_details",
  "date",
  "impressions",
  "clicks",
  "spend",
  "completed_views_first",
  "completed_views_mid",
  "completed_views_third",
  "completed_views_full",
  "video_start",
  "conversion_name" AS "conversion_metric",
  "conversions"
FROM
  TAXONOMY AS TX
  JOIN XANDR_AD_LEVEL AS XAN_AD ON TX."tile_id" = XAN_AD."omni_tile_id"
  JOIN XANDR_BASE_REPORTS as XAN_REP
  ON CAST(XAN_AD."omni_external_campaign_id" AS TEXT) = CAST(XAN_REP."insertion_order_id" AS TEXT)
  AND CAST(XAN_AD."omni_external_adgrp_id" AS TEXT) = CAST(XAN_REP."line_item_id" AS TEXT)
WHERE
  XAN_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"