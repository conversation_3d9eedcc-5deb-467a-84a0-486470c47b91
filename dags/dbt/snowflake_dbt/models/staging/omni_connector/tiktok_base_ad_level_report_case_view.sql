
WITH LATEST_BASE_TIKTOK_REPORTS AS (
    SELECT * FROM  REPORTS.REPORTS_TIKTOK_PERFORMANCE
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_tiktok_performance'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  TIKTOK_AD_LEVEL AS (
    SELECT DISTINCT
      "omni_tile_id",
      "omni_external_campaign_id",
      "omni_campaign_name",
      "omni_external_adgrp_id",
      "omni_adgrp_name",
      "omni_external_ad_id",
      "omni_ad_name",
      "omni_currency_code"
    FROM
      {{ ref('omni_taxonomy_view') }}
    WHERE
      "omni_external_ad_id" IS NOT NULL
      AND "omni_platform_name" = 'tiktok_integrated'
  ),
  TIKTOK_BASE_REPORTS AS (
    SELECT
      "date",
      "campaign_id",
      "ad_group_id",
      "ad_id",
      SUM("impressions") AS "impressions",
      SUM("clicks") AS "clicks",
      SUM("spend") As "spend",
      SUM("completed_views_first") AS "completed_views_first",
      SUM("completed_views_mid") AS "completed_views_mid",
      SUM("completed_views_third") AS "completed_views_third",
      SUM("completed_views_full") AS "completed_views_full",
      SUM("video_start") AS "video_start",
      SUM("video_views") AS "video_views",
      SUM("platform_conversions") AS "platform_conversions",
      SUM("paid_follower") AS "paid_follower"
    FROM
      LATEST_BASE_TIKTOK_REPORTS
    GROUP BY
      "date",
      "campaign_id",
      "ad_group_id",
      "ad_id"
  )

    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      TX."tile_id",
      "tile_name",
      CAST("omni_external_campaign_id" AS TEXT) AS "platform_campaign_id",
      CAST("omni_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("omni_external_adgrp_id" AS TEXT) AS "platform_campaign_group_id",
      CAST("omni_adgrp_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("omni_external_ad_id" AS TEXT) AS "ad_asset_id",
      CAST("omni_ad_name" AS TEXT) AS "ad_asset",
      COALESCE("omni_currency_code","mediarow_currency") AS "currency",
      'TikTok' AS "data_source",
      'reports_linkedin_performance' AS "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "completed_views_first",
      "completed_views_mid",
      "completed_views_third",
      "completed_views_full",
      "video_start",
      "video_views",
      "platform_conversions",
      "paid_follower"
    FROM
      TAXONOMY AS TX
      JOIN TIKTOK_AD_LEVEL AS TT_AD ON TX."tile_id" = TT_AD."omni_tile_id"
      JOIN TIKTOK_BASE_REPORTS as TT_REP ON CAST(TT_AD."omni_external_campaign_id" AS TEXT) = CAST(TT_REP."campaign_id" AS TEXT)
      AND CAST(TT_AD."omni_external_adgrp_id" AS TEXT) = CAST(TT_REP."ad_group_id" AS TEXT)
      AND CAST(TT_AD."omni_external_ad_id" AS TEXT) = CAST(TT_REP."ad_id" AS TEXT)
    WHERE
      TT_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"