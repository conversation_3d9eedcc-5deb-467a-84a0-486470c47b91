
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  "tile_id",
  "tile_name",
  "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset",
  "ad_asset_id",
  "currency",
  "date",
  "data_source",
  "data_source_details",
  "clicks",
  "completed_views_first",
  "completed_views_full",
  "completed_views_mid",
  "completed_views_third",
  "impressions",
  "in_app_actions",
  "platform_conversions",
  "platform_installations",
  "spend",
  NULL AS "total_eligible_impressions",
  NULL AS "search_impression_share",
  "video_start",
  "video_views",
  NULL AS "conversion_metric",
  NULL AS "conversions"
FROM
{{ ref('google_ads_base_mobile_ad_level_report_case_view') }}

UNION ALL
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  "tile_id",
  "tile_name",
  "platform_campaign_id",
  "platform_campaign_name",
  NULL AS "platform_campaign_group_id",
  NULL AS "platform_campaign_group_name",
  NULL AS "ad_asset",
  NULL AS "ad_asset_id",
  "currency",
  "date",
  "data_source",
  "data_source_details",
  "clicks",
  "completed_views_first",
  "completed_views_full",
  "completed_views_mid",
  "completed_views_third",
  "impressions",
  NULL AS "in_app_actions",
  "platform_conversions",
  NULL AS "platform_installations",
  "spend",
  NULL AS "total_eligible_impressions",
  NULL AS "search_impression_share",
  "video_start",
  "video_views",
  NULL AS "conversion_metric",
  NULL AS "conversions"
FROM
{{ ref('google_ads_base_campaign_level_report_case_view') }}

UNION ALL
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  "tile_id",
  "tile_name",
  "platform_campaign_id",
  "platform_campaign_name",
  NULL AS "platform_campaign_group_id",
  NULL AS "platform_campaign_group_name",
  NULL AS "ad_asset",
  NULL AS "ad_asset_id",
  "currency",
  "date",
  "data_source",
  "data_source_details",
  NULL AS "clicks",
  NULL AS "completed_views_first",
  NULL AS "completed_views_full",
  NULL AS "completed_views_mid",
  NULL AS "completed_views_third",
  NULL AS "impressions",
  NULL AS "in_app_actions",
  NULL AS "platform_conversions",
  NULL AS "platform_installations",
  NULL AS "spend",
  "total_eligible_impressions",
  "search_impression_share",
  NULL AS "video_start",
  NULL AS "video_views",
  NULL AS "conversion_metric",
  NULL AS "conversions"
FROM
{{ ref('google_ads_estimated_campaign_level_report_case_view') }}
UNION ALL
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  "tile_id",
  "tile_name",
  "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset",
  "ad_asset_id",
  "currency",
  "date",
  "data_source",
  "data_source_details",
  NULL AS "clicks",
  NULL AS "completed_views_first",
  NULL AS "completed_views_full",
  NULL AS "completed_views_mid",
  NULL AS "completed_views_third",
  NULL AS "impressions",
  NULL AS "in_app_actions",
  NULL AS "platform_conversions",
  NULL AS "platform_installations",
  NULL AS "spend",
  NULL AS "total_eligible_impressions",
  NULL AS "search_impression_share",
  NULL AS "video_start",
  NULL AS "video_views",
  "conversion_metric",
  "conversions"
FROM
    {{ ref('google_ads_conversions_all_levels_report_case_view') }}