
WITH
  LATEST_CONVERSION_MAPPING AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_ADWORDS_ACTION_MAPPING WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_adwords_action_mapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CONVERSION_REPORT_AD_LEVEL AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_ADWORDS_AD_CONVERSION_PERFORMANCE WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_adwords_ad_conversion_performance'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CONVERSION_REPORT_CAMPAIGN_LEVEL AS (
    SELECT
      *
    FROM
      REPORTS.SEARCH_CAMPAIGN_CONVERSION_PERFORMANCE WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'search_campaign_conversion_performance'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  ADWORDS_AD_LEVEL AS (
    SELECT DISTINCT
      "google_ads_tile_id",
      "google_ads_external_campaign_id",
      "google_ads_campaign_name",
      "google_ads_external_adgroup_id",
      "google_ads_adgroup_name",
      "google_ads_external_ad_id",
      "google_ads_ad_name",
      "google_ads_currency_code"
    FROM
      {{ ref('google_ads_taxonomy_view') }}
    WHERE
      "google_ads_external_ad_id" IS NOT NULL
  ),
  ADWORDS_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "google_ads_tile_id",
      "google_ads_external_campaign_id",
      "google_ads_campaign_name",
      "google_ads_currency_code"
    FROM
      {{ ref('google_ads_taxonomy_view') }}
    WHERE
      "google_ads_external_adgroup_id" IS NULL
  ),
  CONVERSION_MAPPING AS (
    SELECT DISTINCT
      "conversion_action_id",
      "conversion_action_name"
    FROM
      LATEST_CONVERSION_MAPPING
      -- filtering that is applied here is to reduce the volume of data by taking only portion of conversions:
      -- client can have thousands of conversion_actions and in reality needs 5-10 of them for reporting
      -- in the future this filtering must become dynamic based on user input via metric manager
    WHERE
      "include_in_conversions_metric" = TRUE
      OR "conversion_action_name" IN ('TYP - Start', 'TYP - Next', 'TYP - Premium')
  ),
  ADWORDS_REPORTS_AD_LEVEL AS (
    SELECT
      CONV."date",
      CONV."campaign_id",
      CONV."adgroup_id",
      CONV."ad_id",
      C_MAP."conversion_action_name" AS "conversion_metric",
      SUM(CONV."all_conversions") AS "conversions"
    FROM
      LATEST_CONVERSION_REPORT_AD_LEVEL AS CONV
      JOIN CONVERSION_MAPPING AS C_MAP ON CONV."conversion_action_id" = C_MAP."conversion_action_id"
    GROUP BY
      CONV."date",
      CONV."campaign_id",
      CONV."adgroup_id",
      CONV."ad_id",
      C_MAP."conversion_action_name"
  ),
  ADWORDS_REPORTS_CAMPAIGN_LEVEL AS (
    SELECT
      CONV."date",
      CONV."campaign_id",
      C_MAP."conversion_action_name" AS "conversion_metric",
      SUM(CONV."all_conversions") AS "conversions"
    FROM
      LATEST_CONVERSION_REPORT_CAMPAIGN_LEVEL AS CONV
      JOIN CONVERSION_MAPPING AS C_MAP ON CONV."conversion_action_id" = C_MAP."conversion_action_id"
    GROUP BY
      CONV."date",
      CONV."campaign_id",
      C_MAP."conversion_action_name"
  ),
  ADWORDS_CONVERSIONS_AD_LEVEL AS (
    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      TX."tile_id",
      "tile_name",
      "google_ads_external_campaign_id" AS "platform_campaign_id",
      "google_ads_campaign_name" AS "platform_campaign_name",
      "google_ads_external_adgroup_id" AS "platform_campaign_group_id",
      "google_ads_adgroup_name" AS "platform_campaign_group_name",
      "google_ads_external_ad_id" AS "ad_asset_id",
      "google_ads_ad_name" AS "ad_asset",
      COALESCE("google_ads_currency_code", "mediarow_currency") AS "currency",
      "date",
      "conversion_metric",
      "conversions",
      'Google Ads' AS "data_source",
      'reports_adwords_ad_conversion_performance,reports_adwords_action_mapping' AS "data_source_details"
    FROM
      TAXONOMY AS TX
      JOIN ADWORDS_AD_LEVEL AS ADW_AD ON TX."tile_id" = ADW_AD."google_ads_tile_id"
      JOIN ADWORDS_REPORTS_AD_LEVEL AS ADW_REP ON ADW_AD."google_ads_external_campaign_id" = ADW_REP."campaign_id"
      AND ADW_AD."google_ads_external_adgroup_id" = ADW_REP."adgroup_id"
      AND ADW_AD."google_ads_external_ad_id" = ADW_REP."ad_id"
    WHERE
      ADW_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"
  ),
  ADWORDS_CONVERSIONS_CAMPAIGN_LEVEL AS (
    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      TX."tile_id",
      "tile_name",
      "google_ads_external_campaign_id" AS "platform_campaign_id",
      "google_ads_campaign_name" AS "platform_campaign_name",
      NULL AS "platform_campaign_group_id",
      NULL AS "platform_campaign_group_name",
      NULL AS "ad_asset_id",
      NULL AS "ad_asset",
      COALESCE("google_ads_currency_code", "mediarow_currency") AS "currency",
      "date",
      "conversion_metric",
      "conversions",
      'Google Ads' AS "data_source",
      'search_campaign_conversion_performance,reports_adwords_action_mapping' AS "data_source_details"
    FROM
      TAXONOMY AS TX
      JOIN ADWORDS_CAMPAIGN_LEVEL AS ADW_CM ON TX."tile_id" = ADW_CM."google_ads_tile_id"
      JOIN ADWORDS_REPORTS_CAMPAIGN_LEVEL AS ADW_REP_CM ON ADW_CM."google_ads_external_campaign_id" = ADW_REP_CM."campaign_id"
    WHERE
      ADW_REP_CM."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"
  )
SELECT
  *
FROM
  ADWORDS_CONVERSIONS_AD_LEVEL
UNION ALL
SELECT
  *
FROM
  ADWORDS_CONVERSIONS_CAMPAIGN_LEVEL