
WITH
  LATEST_BASE_ADWORDS_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_ADWORDS_AD_PERFORMANCE
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_adwords_ad_performance'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_MOBILE_ADWORDS_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.SEARCH_CAMPAIGN_PERFORMANCE_MOBILE
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'search_campaign_performance_mobile'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  ADWORDS_AD_LEVEL AS (
    SELECT DISTINCT
      "google_ads_tile_id",
      "google_ads_external_campaign_id",
      "google_ads_campaign_name",
      "google_ads_external_adgroup_id",
      "google_ads_adgroup_name",
      "google_ads_external_ad_id",
      "google_ads_ad_name",
      "google_ads_currency_code"
    FROM
      {{ ref('google_ads_taxonomy_view') }}
    WHERE
      "google_ads_external_ad_id" IS NOT NULL
  ),
  ADWORDS_BASE_REPORTS AS (
    SELECT
      "date",
      "campaign_id",
      "adgroup_id",
      "ad_id",
      "currency_code",
      SUM("impression") AS "impressions",
      SUM("clicks") AS "clicks",
      SUM("budget_spent") AS "spend",
      SUM("views_25p") AS "completed_views_first",
      SUM("views_50p") AS "completed_views_mid",
      SUM("views_75p") AS "completed_views_third",
      SUM("complete_views") AS "completed_views_full",
      SUM("video_views") AS "video_views",
      SUM("active_view_impressions") AS "video_start",
      SUM("all_conversions") AS "platform_conversions"
    FROM
      LATEST_BASE_ADWORDS_REPORTS
    GROUP BY
      "date",
      "campaign_id",
      "adgroup_id",
      "ad_id",
      "currency_code"
  ),
  ADWORDS_MOBILE_REPORTS AS (
    SELECT
      "date",
      "campaign_id",
      "ad_group_id",
      "ad_id",
      SUM("installations") AS "platform_installations",
      SUM("in_app_actions") AS "in_app_actions"
    FROM
      LATEST_MOBILE_ADWORDS_REPORTS
    GROUP BY
      "date",
      "campaign_id",
      "ad_group_id",
      "ad_id"
  ),
  ADWORDS_REPORTS AS (
    SELECT
      ABR."date",
      ABR."campaign_id",
      ABR."adgroup_id",
      ABR."ad_id",
      ABR."currency_code",
      ABR."impressions",
      ABR."clicks",
      ABR."spend",
      ABR."platform_conversions",
      ABR."video_start",
      ABR."video_views",
      ABR."completed_views_first",
      ABR."completed_views_mid",
      ABR."completed_views_third",
      ABR."completed_views_full",
      AMR."platform_installations",
      AMR."in_app_actions"
    FROM
      ADWORDS_BASE_REPORTS AS ABR
      LEFT JOIN ADWORDS_MOBILE_REPORTS AS AMR ON ABR."campaign_id" = AMR."campaign_id"
      AND ABR."adgroup_id" = AMR."ad_group_id"
      AND ABR."ad_id" = AMR."ad_id"
      AND ABR."date" = AMR."date"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  TX."tile_id",
  "tile_name",
  "google_ads_external_campaign_id" AS "platform_campaign_id",
  "google_ads_campaign_name" AS "platform_campaign_name",
  "google_ads_external_adgroup_id" AS "platform_campaign_group_id",
  "google_ads_adgroup_name" AS "platform_campaign_group_name",
  "google_ads_external_ad_id" AS "ad_asset_id",
  "google_ads_ad_name" AS "ad_asset",
  COALESCE(
    "currency_code",
    "google_ads_currency_code",
    "mediarow_currency"
  ) AS "currency",
  "date",
  "impressions",
  "clicks",
  "spend",
  "platform_conversions",
  "video_start",
  "video_views",
  "completed_views_first",
  "completed_views_mid",
  "completed_views_third",
  "completed_views_full",
  "platform_installations",
  "in_app_actions",
  'Google Ads' AS "data_source",
  'reports_adwords_ad_performance, search_campaign_performance_mobile' AS "data_source_details"
FROM
  TAXONOMY AS TX
  JOIN ADWORDS_AD_LEVEL AS ADW_AD ON TX."tile_id" = ADW_AD."google_ads_tile_id"
  JOIN ADWORDS_REPORTS AS ADW_REP ON ADW_AD."google_ads_external_campaign_id" = ADW_REP."campaign_id"
  AND ADW_AD."google_ads_external_adgroup_id" = ADW_REP."adgroup_id"
  AND ADW_AD."google_ads_external_ad_id" = ADW_REP."ad_id"
WHERE
  ADW_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"