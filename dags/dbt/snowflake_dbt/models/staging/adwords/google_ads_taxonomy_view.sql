
WITH
  LATEST_INTEGRATION AS (
    SELECT
      *
    FROM
      ADWORDS.EXTERNAL_SERVICES_ADWORDSEXTERNALDATA
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'external_services_adwordsexternaldata'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CAMPAIGN_ADWORDSSEARCHCAMPAIGN AS (
    SELECT
      *
    FROM
      ADWORDS.CAMPAIGN_ADWORDSSEARCHCAMPAIGN
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'campaign_adwordssearchcampaign'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CAMPAIGN_ADWORDSSEARCHRELATEDCAMPAIGN AS (
    SELECT
      *
    FROM
      ADWORDS.CAMPAIGN_ADWORDSSEARCHRELATEDCAMPAIGN
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'campaign_adwordssearchrelatedcampaign'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CAMPAIGN_ADWORDSADGROUP AS (
    SELECT
      *
    FROM
      ADWORDS.CAMPAIGN_ADWORDSADGROUP
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'campaign_adwordsadgroup'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CAMPAIGN_ADWORDSAD AS (
    SELECT
      *
    FROM
      ADWORDS.CAMPAIGN_ADWORDSAD
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'campaign_adwordsad'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_RESPONSIVESEARCHADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_RESPONSIVESEARCHADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_responsivesearchadmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_ADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_ADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_admodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_RESPONSIVEDISPLAYADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_RESPONSIVEDISPLAYADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_responsivedisplayadmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_DISCOVERYCAROUSELADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_DISCOVERYCAROUSELADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_discoverycarouseladmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_DISCOVERYMULTIASSETADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_DISCOVERYMULTIASSETADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_discoverymultiassetadmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_IMAGEADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_IMAGEADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_imageadmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_HTML5UPLOADADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_HTML5UPLOADADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_html5uploadadmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_DYNAMICHTML5ADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_DYNAMICHTML5ADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_dynamichtml5admodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_VIDEOADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_VIDEOADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_videoadmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SERVICE_EXPANDEDDYNAMICTEXTADMODEL AS (
    SELECT
      *
    FROM
      ADWORDS.SERVICE_EXPANDEDDYNAMICTEXTADMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADWORDS'
              AND "TABLE_NAME" = 'service_expandeddynamictextadmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  )
SELECT
  CASC."external_core_campaign_id" AS "google_ads_tile_id",
  GI."id" AS "google_ads_integration_id",
  GI."currency_code" AS "google_ads_currency_code",
  ASRC."id" AS "google_ads_internal_campaign_id",
  ASRC."external_id" AS "google_ads_external_campaign_id",
  ASRC."name" AS "google_ads_campaign_name",
  ASRC."bidding_type" AS "google_ads_campaign_bidding_type",
  ASRC."start_date" AS "google_ads_campaign_start_date",
  ASRC."end_date" AS "google_ads_campaign_end_date",
  ASRC."budget" AS "google_ads_campaign_budget",
  ASRC."status" AS "google_ads_campaign_status",
  ASRC."type" AS "google_ads_campaign_type",
  ADG."id" AS "google_ads_internal_adgroup_id",
  ADG."external_id" AS "google_ads_external_adgroup_id",
  ADG."name" AS "google_ads_adgroup_name",
  ADG."status" AS "google_ads_adgroup_status",
  NAD."id" AS "google_ads_internal_ad_id",
  NAD."external_id" AS "google_ads_external_ad_id",
  NAD."ad_headline1" AS "google_ads_ad_name",
  NAD."status" AS "google_ads_ad_status"
FROM
  LATEST_INTEGRATION AS GI
  JOIN LATEST_CAMPAIGN_ADWORDSSEARCHCAMPAIGN CASC ON GI."id" = CASC."credentials_id"
  JOIN LATEST_CAMPAIGN_ADWORDSSEARCHRELATEDCAMPAIGN ASRC ON CASC."id" = ASRC."parent_campaign_id"
  LEFT JOIN LATEST_CAMPAIGN_ADWORDSADGROUP ADG ON ADG."campaign_id" = ASRC."id"
  LEFT JOIN (
    SELECT
      LATEST_CAMPAIGN_ADWORDSAD."id",
      LATEST_CAMPAIGN_ADWORDSAD."external_id",
      LATEST_CAMPAIGN_ADWORDSAD."status",
      LATEST_CAMPAIGN_ADWORDSAD."ad_group_id",
      LATEST_CAMPAIGN_ADWORDSAD."headline_part1"::TEXT AS "ad_headline1"
    FROM
      LATEST_CAMPAIGN_ADWORDSAD
    UNION
    SELECT
      LATEST_SERVICE_RESPONSIVESEARCHADMODEL."id",
      LATEST_SERVICE_RESPONSIVESEARCHADMODEL."external_id",
      LATEST_SERVICE_RESPONSIVESEARCHADMODEL."status",
      LATEST_SERVICE_RESPONSIVESEARCHADMODEL."ad_group_id",
      COALESCE(PARSE_JSON(LATEST_SERVICE_RESPONSIVESEARCHADMODEL."headlines")[1]::TEXT, PARSE_JSON(LATEST_SERVICE_RESPONSIVESEARCHADMODEL."headlines")[0]::TEXT) AS "ad_headline1"
    FROM
      LATEST_SERVICE_RESPONSIVESEARCHADMODEL
    UNION
    SELECT
      LATEST_SERVICE_ADMODEL."id",
      LATEST_SERVICE_ADMODEL."external_id",
      LATEST_SERVICE_ADMODEL."status",
      LATEST_SERVICE_ADMODEL."ad_group_id",
      LATEST_SERVICE_VIDEOADMODEL."name"::TEXT AS "ad_headline1"
    FROM
      LATEST_SERVICE_ADMODEL
      JOIN LATEST_SERVICE_VIDEOADMODEL ON LATEST_SERVICE_ADMODEL."id" = LATEST_SERVICE_VIDEOADMODEL."ad_id"
    UNION
    SELECT
      LATEST_SERVICE_ADMODEL."id",
      LATEST_SERVICE_ADMODEL."external_id",
      LATEST_SERVICE_ADMODEL."status",
      LATEST_SERVICE_ADMODEL."ad_group_id",
      COALESCE(PARSE_JSON(LATEST_SERVICE_RESPONSIVEDISPLAYADMODEL."headlines")[1]::TEXT, PARSE_JSON(LATEST_SERVICE_RESPONSIVEDISPLAYADMODEL."headlines")[0]::TEXT) AS "ad_headline1"
    FROM
      LATEST_SERVICE_ADMODEL
      JOIN LATEST_SERVICE_RESPONSIVEDISPLAYADMODEL ON LATEST_SERVICE_ADMODEL."id" = LATEST_SERVICE_RESPONSIVEDISPLAYADMODEL."ad_id"
    UNION
    SELECT
      LATEST_SERVICE_ADMODEL."id",
      LATEST_SERVICE_ADMODEL."external_id",
      LATEST_SERVICE_ADMODEL."status",
      LATEST_SERVICE_ADMODEL."ad_group_id",
      LATEST_SERVICE_DISCOVERYCAROUSELADMODEL."headline"::TEXT AS "ad_headline1"
    FROM
      LATEST_SERVICE_ADMODEL
      JOIN LATEST_SERVICE_DISCOVERYCAROUSELADMODEL ON LATEST_SERVICE_ADMODEL."id" = LATEST_SERVICE_DISCOVERYCAROUSELADMODEL."ad_id"
    UNION
    SELECT
      LATEST_SERVICE_ADMODEL."id",
      LATEST_SERVICE_ADMODEL."external_id",
      LATEST_SERVICE_ADMODEL."status",
      LATEST_SERVICE_ADMODEL."ad_group_id",
      COALESCE(PARSE_JSON(LATEST_SERVICE_DISCOVERYMULTIASSETADMODEL."headlines")[1]::TEXT, PARSE_JSON(LATEST_SERVICE_DISCOVERYMULTIASSETADMODEL."headlines")[0]::TEXT) AS "ad_headline1"
    FROM
      LATEST_SERVICE_ADMODEL
      JOIN LATEST_SERVICE_DISCOVERYMULTIASSETADMODEL ON LATEST_SERVICE_ADMODEL."id" = LATEST_SERVICE_DISCOVERYMULTIASSETADMODEL."ad_id"
    UNION
    SELECT
      LATEST_SERVICE_ADMODEL."id",
      LATEST_SERVICE_ADMODEL."external_id",
      LATEST_SERVICE_ADMODEL."status",
      LATEST_SERVICE_ADMODEL."ad_group_id",
      LATEST_SERVICE_IMAGEADMODEL."name"::TEXT AS "ad_headline1"
    FROM
      LATEST_SERVICE_ADMODEL
      JOIN LATEST_SERVICE_IMAGEADMODEL ON LATEST_SERVICE_ADMODEL."id" = LATEST_SERVICE_IMAGEADMODEL."ad_id"
    UNION
    SELECT
      LATEST_SERVICE_ADMODEL."id",
      LATEST_SERVICE_ADMODEL."external_id",
      LATEST_SERVICE_ADMODEL."status",
      LATEST_SERVICE_ADMODEL."ad_group_id",
      LATEST_SERVICE_HTML5UPLOADADMODEL."name"::TEXT AS "ad_headline1"
    FROM
      LATEST_SERVICE_ADMODEL
      JOIN LATEST_SERVICE_HTML5UPLOADADMODEL ON LATEST_SERVICE_ADMODEL."id" = LATEST_SERVICE_HTML5UPLOADADMODEL."ad_id"
    UNION
    SELECT
      LATEST_SERVICE_ADMODEL."id",
      LATEST_SERVICE_ADMODEL."external_id",
      LATEST_SERVICE_ADMODEL."status",
      LATEST_SERVICE_ADMODEL."ad_group_id",
      LATEST_SERVICE_DYNAMICHTML5ADMODEL."name"::TEXT AS "ad_headline1"
    FROM
      LATEST_SERVICE_ADMODEL
      JOIN LATEST_SERVICE_DYNAMICHTML5ADMODEL ON LATEST_SERVICE_ADMODEL."id" = LATEST_SERVICE_DYNAMICHTML5ADMODEL."ad_id"
    UNION
    SELECT
      LATEST_SERVICE_EXPANDEDDYNAMICTEXTADMODEL."id",
      LATEST_SERVICE_EXPANDEDDYNAMICTEXTADMODEL."external_id",
      LATEST_SERVICE_EXPANDEDDYNAMICTEXTADMODEL."status",
      LATEST_SERVICE_EXPANDEDDYNAMICTEXTADMODEL."ad_group_id",
      'Dynamically generated headline'::TEXT AS "ad_headline1"
    FROM
      LATEST_SERVICE_EXPANDEDDYNAMICTEXTADMODEL
    UNION
    SELECT
      LATEST_SERVICE_ADMODEL."id",
      LATEST_SERVICE_ADMODEL."external_id",
      LATEST_SERVICE_ADMODEL."status",
      LATEST_SERVICE_ADMODEL."ad_group_id",
      LATEST_SERVICE_ADMODEL."direct_name"::TEXT AS "ad_headline1"
    FROM
      LATEST_SERVICE_ADMODEL
    WHERE
      LATEST_SERVICE_ADMODEL."type" NOT IN (
        'RESPONSIVE_DISPLAY_AD',
        'DEMAND_GEN_CAROUSEL_AD',
        'DEMAND_GEN_MULTI_ASSET_AD',
        'HTML5_UPLOAD_AD',
        'DYNAMIC_HTML5_AD',
        'IMAGE_AD',
        'VIDEO_TRUEVIEW_IN_STREAM_AD',
        'VIDEO_RESPONSIVE_AD',
        'DISCOVERY_MULTI_ASSET_AD',
        'IN_FEED_VIDEO_AD',
        'VIDEO_NON_SKIPPABLE_IN_STREAM_AD',
        'VIDEO_BUMPER_AD',
        'DISCOVERY_CAROUSEL_AD'
      )
  ) AS NAD ON NAD."ad_group_id" = ADG."id"