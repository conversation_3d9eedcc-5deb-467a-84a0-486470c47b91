
WITH
  LATEST_BASE_ADWORDS_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.SEARCH_CAMPAIGN_PERFORMANCE
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'search_campaign_performance'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  ADWORDS_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "google_ads_tile_id",
      "google_ads_external_campaign_id",
      "google_ads_campaign_name",
      "google_ads_currency_code"
    FROM
      {{ ref('google_ads_taxonomy_view') }}
  ),
  ADWORDS_ESTIMATED_REPORTS AS (
    SELECT
      "date",
      "campaign_id",
      SUM("search_impression_share") AS "search_impression_share",
      SUM("total_eligible_impressions") AS "total_eligible_impressions",
    FROM
      LATEST_BASE_ADWORDS_REPORTS
    GROUP BY
      "date",
      "campaign_id"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  TX."tile_id",
  "tile_name",
  "google_ads_external_campaign_id" AS "platform_campaign_id",
  "google_ads_campaign_name" AS "platform_campaign_name",
  "date",
  COALESCE("google_ads_currency_code", "mediarow_currency") AS "currency",
  "search_impression_share",
  "total_eligible_impressions",
  'Google Ads' AS "data_source",
  'search_campaign_performance' AS "data_source_details"
FROM
  TAXONOMY AS TX
  JOIN ADWORDS_CAMPAIGN_LEVEL AS ADW_C ON TX."tile_id" = ADW_C."google_ads_tile_id"
  JOIN ADWORDS_ESTIMATED_REPORTS AS ADW_EST_REP ON ADW_C."google_ads_external_campaign_id" = ADW_EST_REP."campaign_id"
WHERE
  ADW_EST_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"