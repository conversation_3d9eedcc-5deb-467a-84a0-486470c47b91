version: 2

models:
  - name: google_ads_taxonomy_view
    description: "Taxonomy view for Google Ads data"
    config:
      schema: ADWORDS

  - name: google_ads_base_mobile_ad_level_report_case_view
    description: "Base mobile ad level report case view"
    config:
      schema: ADWORDS


  - name: google_ads_base_campaign_level_report_case_view
    description: "Base campaign level report case view"
    config:
      schema: ADWORDS

  - name: google_ads_estimated_campaign_level_report_case_view
    description: "Estimated campaign level report case view"
    config:
      schema: ADWORDS

  - name: google_ads_conversions_all_levels_report_case_view
    description: "Conversions all levels report case view"
    config:
      schema: ADWORDS

  - name: google_ads_final_view
    description: "Final view combining all Google Ads data"
    config:
      schema: ADWORDS

  - name: mint_taxonomy_view
    description: "Mint taxonomy view for Google Ads data"
    config:
      schema: DATINTELL

  - name: mint_virtual_platforms_taxonomy_view
    description: "Mint taxonomy view for Virtual platforms data"
    config:
      schema: DATINTELL

  - name: dv360_taxonomy_view
    description: "DV360 platform taxonomy view"
    config:
      schema: DV360

  - name: dv360_base_creative_level_report_case_view
    description: "DV360 base creative level report case view"
    config:
      schema: DV360

  - name: dv360_youtube_ad_level_report_case_view
    description: "DV360 YouTube base creative level report case view"
    config:
      schema: DV360

  - name: dv360_final_view
    description: "DV360 view combining all DV360 data"
    config:
      schema: DV360

  - name: meta_taxonomy_view
    description: "Meta platform taxonomy view"
    config:
      schema: FACEBOOK_INSTAGRAM

  - name: meta_base_ad_level_report_case_view
    description: "Meta base ad level report case view"
    config:
      schema: FACEBOOK_INSTAGRAM

  - name: meta_conversions_ad_level_report_case_view
    description: "Meta conversions ad level report case view"
    config:
      schema: FACEBOOK_INSTAGRAM

  - name: meta_final_view
    description: "Meta view combining all Meta data"
    config:
      schema: FACEBOOK_INSTAGRAM

  - name: microsoft_ads_taxonomy_view
    description: "Microsoft Ads platform taxonomy view"
    config:
      schema: BING

  - name: microsoft_ads_base_ad_level_report_case_view
    description: "Microsoft Ads base ad level report case view"
    config:
      schema: BING

  - name: microsoft_ads_base_campaign_level_report_case_view
    description: "Microsoft Ads base campaign level report case view"
    config:
      schema: BING

  - name: microsoft_ads_final_view
    description: "Microsoft Ads view combining all Microsoft Ads data"
    config:
      schema: BING

  - name: zemanta_taxonomy_view
    description: "Zemanta platform taxonomy view"
    config:
      schema: ZEMANTA

  - name: zemanta_base_ad_level_report_case_view
    description: "Zemanta base campaign level report case view"
    config:
      schema: ZEMANTA

  - name: zemanta_final_view
    description: "Zemanta view combining all Zemanta data"
    config:
      schema: ZEMANTA

  - name: omni_taxonomy_view
    description: "Omni platform taxonomy view"
    config:
      schema: OMNI_CONNECTOR

  - name: xandr_base_ad_level_report_case_view
    description: "Xandr base and conversions campaign level report case view"
    config:
      schema: OMNI_CONNECTOR

  - name: xandr_final_view
    description: "Xandr view combining all Xandr data"
    config:
      schema: OMNI_CONNECTOR

  - name: linkedin_base_ad_level_report_case_view
    description: "LinkedIn base and conversions campaign level report case view"
    config:
      schema: OMNI_CONNECTOR

  - name: linkedin_final_view
    description: "LinkedIn view combining all LinkedIn data"
    config:
      schema: OMNI_CONNECTOR

  - name: amazon_base_ad_level_report_case_view
    description: "Amazon base campaign level report case view"
    config:
      schema: OMNI_CONNECTOR

  - name: amazon_final_view
    description: "Amazon view combining all Amazon data"
    config:
      schema: OMNI_CONNECTOR

  - name: tiktok_base_ad_level_report_case_view
    description: "TikTok base and conversions campaign level report case view"
    config:
      schema: OMNI_CONNECTOR

  - name: tiktok_final_view
    description: "TikTok view combining all TikTok data"
    config:
      schema: OMNI_CONNECTOR

  - name: mint_unified_all_platforms_taxonomy_view
    description: "View that unifies all variations of platforms and granularity options"
    config:
      schema: MINT

  - name: cm360_mappings_view
    description: "View that presents all CM360 mappings"
    config:
      schema: CM360

  - name: cm360_base_campaign_level_for_integrated_platform_case
    description: "View for campaign level base reporting data of cm360 for integrated platforms"
    config:
      schema: CM360

  - name: cm360_base_campaign_level_for_virtual_platform_case
    description: "View for campaign level base reporting data of cm360 for virtual platforms"
    config:
      schema: CM360

  - name: cm360_base_adgrpoup_level_for_integrated_platform_case
    description: "View for group level base reporting data of cm360 for integrated platforms"
    config:
      schema: CM360

  - name: cm360_base_ad_level_for_integrated_platform_case
    description: "View for ad level base reporting data of cm360 for integrated platforms"
    config:
      schema: CM360

  - name: cm360_base_dv360_youtube_level_case
    description: "View for cm360 base data for dv360 youtube case"
    config:
      schema: CM360

  - name: cm360_conversions_campaign_level_for_integrated_platforms
    description: "View for campaign level conversions reporting data of cm360 for integrated platforms"
    config:
      schema: CM360

  - name: cm360_conversions_campaign_level_for_virtual_platforms
    description: "View for campaign level conversions reporting data of cm360 for virtual platforms"
    config:
      schema: CM360

  - name: cm360_conversions_adgrpoup_level_for_integrated_platform_case
    description: "View for group level conversions reporting data of cm360 for integrated platforms"
    config:
      schema: CM360

  - name: cm360_conversions_ad_level_for_integrated_platform_case
    description: "View for ad level conversions reporting data of cm360 for integrated platforms"
    config:
      schema: CM360

  - name: cm360_conversions_dv360_youtube_level_case
    description: "View for cm360 conversions data for dv360 youtube case"
    config:
      schema: CM360

  - name: cm360_base_and_conversions_dv360_linkage_case
    description: "View for cm360 base and conversions data for dv360 linkage case"
    config:
      schema: CM360

  - name: cm360_conversions_paid_search_case
    description: "View for cm360 conversions data for paid_search case"
    config:
      schema: CM360

  - name: cm360_final_view
    description: "View to unify all cm360 data"
    config:
      schema: CM360

  - name: cm360_final_view_deduplicated
    description: "View to unify all cm360 data but after deduplication process"
    config:
      schema: CM360

  - name: adform_mappings_view
    description: "View that presents all Adform mappings"
    config:
      schema: ADFORM

  - name: adform_base_campaign_level_for_virtual_platform_case
    description: "View for campaign level base reporting data of Adform for virtual platforms"
    config:
      schema: ADFORM

  - name: adform_conversions_campaign_level_for_integrated_platforms
    description: "View for campaign level conversions reporting data of Adform for integrated platforms"
    config:
      schema: ADFORM

  - name: adform_conversions_campaign_level_for_virtual_platforms
    description: "View for campaign level conversions reporting data of Adform for virtual platforms"
    config:
      schema: ADFORM

  - name: adform_conversions_adgrpoup_level_for_integrated_platform_case
    description: "View for group level conversions reporting data of Adform for integrated platforms"
    config:
      schema: ADFORM

  - name: adform_conversions_ad_level_for_integrated_platform_case
    description: "View for ad level conversions reporting data of Adform for integrated platforms"
    config:
      schema: ADFORM

  - name: adform_conversions_dv360_youtube_level_case
    description: "View for Adform conversions data for DV360 youtube case"
    config:
      schema: ADFORM

  - name: adform_conversions_direct_campaign_to_campaign_mappings_case
    description: "View for campaign level conversion data from Adform for specific direct campaign to campaign mappings"
    config:
      schema: ADFORM

  - name: adform_final_view
    description: "View to unify all Adform data"
    config:
      schema: ADFORM

  - name: adform_final_view_deduplicated
    description: "View to unify all Adform data but after deduplication process"
    config:
      schema: ADFORM

  - name: appsflyer_mappings_view
    description: "View that presents all Appsflyer mappings"
    config:
      schema: APPSFLYER

  - name: appsflyer_base_campaign_level_for_virtual_platform_case
    description: "View for campaign level base reporting data of Appsflyer for virtual platforms"
    config:
      schema: APPSFLYER

  - name: appsflyer_base_campaign_level_for_integrated_platform_case
    description: "View for campaign level base reporting data of Appsflyer for integrated platforms"
    config:
      schema: APPSFLYER

  - name: appsflyer_conversions_campaign_level_for_virtual_platforms
    description: "View for campaign level conversions reporting data of Appsflyer for virtual platforms"
    config:
      schema: APPSFLYER

  - name: appsflyer_conversions_campaign_level_for_integrated_platforms
    description: "View for campaign level conversions reporting data of Appsflyer for integrated platforms"
    config:
      schema: APPSFLYER

  - name: appsflyer_final_view
    description: "View to unify all Appsflyer data"
    config:
      schema: APPSFLYER

  - name: appsflyer_final_view_deduplicated
    description: "View to unify all Appsflyer data but after deduplication process"
    config:
      schema: APPSFLYER

  - name: ga4_mappings_view
    description: "View that presents all ga4 mappings"
    config:
      schema: GA4

  - name: ga4_autotagging_view
    description: "View that presents mappings for ga4 autotaging"
    config:
      schema: GA4

  - name: ga4_mappings_reports_pre_merge_events_view
    description: "View that pre-merges events report data to ga4 mappings based on UTM rule sets"
    config:
      schema: GA4

  - name: ga4_mappings_reports_pre_merge_sessions_view
    description: "View that pre-merges non-events report data to ga4 mappings based on UTM rule sets"
    config:
      schema: GA4

  - name: ga4_base_platform_level_report_case_view
    description: "View for platform level non-events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_base_media_row_level_report_case_view
    description: "View for media row level non-events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_base_campaign_level_integrated_report_case_view
    description: "View for campaign level non-events integrated platforms reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_base_campaign_level_virtual_report_case_view
    description: "View for campaign level non-events virtual platforms reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_base_adgroup_level_report_case_view
    description: "View for adgroup level non-events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_base_ad_level_report_case_view
    description: "View for ad level non-events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_base_youtube_dv360_level_report_case_view
    description: "View for youtube_dv360 level non-events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_events_platform_level_report_case_view
    description: "View for platform level events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_events_media_row_level_report_case_view
    description: "View for media row level events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_events_campaign_level_integrated_report_case_view
    description: "View for campaign level events integrated platforms reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_events_campaign_level_virtual_report_case_view
    description: "View for adgroup level events virtual platforms reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_events_adgroup_level_report_case_view
    description: "View for adgroup level events virtual platforms reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_events_ad_level_report_case_view
    description: "View for ad level events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_events_youtube_dv360_level_report_case_view
    description: "View for youtube_dv360 level events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_base_google_ads_autotagging_report_case_view
    description: "View for google ads autotagging non-events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_events_google_ads_autotagging_report_case_view
    description: "View for google ads autotagging events reporting data of GA4"
    config:
      schema: GA4

  - name: ga4_final_view
    description: "View to unify all ga4 data"
    config:
      schema: GA4