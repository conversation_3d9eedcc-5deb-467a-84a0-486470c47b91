
WITH
  LATEST_BASE_ZEMANTA_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_ZEMANTA_PERFORMANCE_V2
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_zemanta_performance_v2'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  ZEMANTA_AD_LEVEL AS (
    SELECT DISTINCT
      "zemanta_tile_id",
      "zemanta_external_campaign_id",
      "zemanta_campaign_name",
      "zemanta_external_adgrp_id",
      "zemanta_adgrp_name",
      "zemanta_external_ad_id",
      "zemanta_ad_name",
      "zemanta_currency_code"
    FROM
      {{ ref('zemanta_taxonomy_view') }}
    WHERE
      "zemanta_external_ad_id" IS NOT NULL
  ),
  ZEMANTA_BASE_REPORTS AS (
    SELECT
      "date",
      "campaign_id",
      "ad_group_id",
      "content_ad_id",
      "currency",
      SUM("impressions") AS "impressions",
      SUM("clicks") AS "clicks",
      SUM("total_spend") As "spend",
      SUM("video_first_quartile") AS "completed_views_first",
      SUM("video_midpoint") AS "completed_views_mid",
      SUM("video_third_quartile") AS "completed_views_third",
      SUM("video_complete") AS "completed_views_full",
      SUM("video_start") AS "video_start"
    FROM
      LATEST_BASE_ZEMANTA_REPORTS
    GROUP BY
      "date",
      "campaign_id",
      "ad_group_id",
      "content_ad_id",
      "currency"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  TX."tile_id",
  "tile_name",
  "zemanta_external_campaign_id" AS "platform_campaign_id",
  "zemanta_campaign_name" AS "platform_campaign_name",
  "zemanta_external_adgrp_id" AS "platform_campaign_group_id",
  "zemanta_adgrp_name" AS "platform_campaign_group_name",
  "zemanta_external_ad_id" AS "ad_asset_id",
  "zemanta_ad_name" AS "ad_asset",
  COALESCE(
    "currency",
    "zemanta_currency_code",
    "mediarow_currency"
  ) AS "currency",
  'Zemanta' AS "data_source",
  'reports_zemanta_performance_v2' AS "data_source_details",
  "date",
  "impressions",
  "clicks",
  "spend",
  "completed_views_first",
  "completed_views_mid",
  "completed_views_third",
  "completed_views_full",
  "video_start"
FROM
  TAXONOMY AS TX
  JOIN ZEMANTA_AD_LEVEL AS ZEM_AD ON TX."tile_id" = ZEM_AD."zemanta_tile_id"
  JOIN ZEMANTA_BASE_REPORTS as ZEM_REP ON ZEM_AD."zemanta_external_campaign_id" = ZEM_REP."campaign_id"
  AND ZEM_AD."zemanta_external_adgrp_id" = ZEM_REP."ad_group_id"
  AND ZEM_AD."zemanta_external_ad_id" = ZEM_REP."content_ad_id"
WHERE
  ZEM_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"