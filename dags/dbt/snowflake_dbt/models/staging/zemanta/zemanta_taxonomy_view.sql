
WITH
  LATEST_INTEGRATION AS (
    SELECT
      *
    FROM
      ZEMANTA.INTEGRATIONS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ZEMANTA'
              AND "TABLE_NAME" = 'integrations'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_PLATFORM_SETTINGS AS (
    SELECT
      *
    FROM
      ZEMANTA.PLATFORM_SETTINGS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMES<PERSON><PERSON>",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ZEMANTA'
              AND "TABLE_NAME" = 'platform_settings'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CAMPAIGN AS (
    SELECT
      *
    FROM
      ZEMANTA.CAMPAIGNS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ZEMANTA'
              AND "TABLE_NAME" = 'campaigns'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_ADGROUP AS (
    SELECT
      *
    FROM
      ZEMANTA.AD_GROUPS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ZEMANTA'
              AND "TABLE_NAME" = 'ad_groups'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_AD AS (
    SELECT
      *
    FROM
      ZEMANTA.ADS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ZEMANTA'
              AND "TABLE_NAME" = 'ads'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  )
SELECT
  CAMP."platform_settings_id" AS "zemanta_tile_id",
  INTGR."id" AS "zemanta_integration_id",
  INTGR."currency_code" AS "zemanta_currency_code",
  CAMP."id" AS "zemanta_internal_campaign_id",
  CAMP."external_id" AS "zemanta_external_campaign_id",
  CAMP."name" AS "zemanta_campaign_name",
  CAMP."amount" AS "zemanta_campaign_lifetime_budget",
  CAMP."delivery_status" AS "zemanta_campaign_status",
  CAMP."type" AS "zemanta_campaign_type",
  ADGRP."id" AS "zemanta_internal_adgrp_id",
  ADGRP."external_id" AS "zemanta_external_adgrp_id",
  ADGRP."name" AS "zemanta_adgrp_name",
  ADGRP."daily_budget" AS "zemanta_adgrp_daily_budget",
  ADGRP."start_date" AS "zemanta_adgrp_date_from",
  ADGRP."end_date" AS "zemanta_adgrp_date_to",
  ADGRP."state" AS "zemanta_adgrp_status",
  AD."id" AS "zemanta_internal_ad_id",
  AD."external_id" AS "zemanta_external_ad_id",
  AD."name" AS "zemanta_ad_name",
  AD."state" AS "zemanta_ad_status"
FROM
  LATEST_INTEGRATION AS intgr
  JOIN LATEST_PLATFORM_SETTINGS AS PS ON INTGR."id" = PS."integration_id"
  JOIN LATEST_CAMPAIGN AS CAMP ON PS."tile_id" = CAMP."platform_settings_id"
  LEFT JOIN LATEST_ADGROUP ADGRP ON CAMP."id" = ADGRP."internal_campaign_id"
  LEFT JOIN LATEST_AD AD ON ADGRP."id" = AD."internal_ad_group_id"