
SELECT
  M."company_id",
  <PERSON>."journey_id",
  <PERSON>."journey_name",
  <PERSON>."media_plan_id",
  <PERSON>."media_plan_name",
  <PERSON>."channel_id",
  <PERSON>."channel_name",
  <PERSON>."platform_id",
  <PERSON>."platform_name",
  <PERSON><PERSON>"tile_id",
  <PERSON>."tile_name",
  <PERSON>."media_row_id",
  <PERSON>."media_row_name",
  <PERSON>."platform_campaign_external_id" AS "platform_campaign_id",
  <PERSON>."platform_campaign_name" AS "platform_campaign_name",
  <PERSON>."platform_campaign_external_group_id" AS "platform_campaign_group_id",
  M."platform_campaign_group_name" AS "platform_campaign_group_name",
  M."ad_asset_external_id" AS "ad_asset_id",
  M."ad_asset",
  M."currency",
  R."utm_source",
  R."utm_medium",
  R."utm_campaign",
  R."utm_term",
  R."utm_content",
  R."ga4_conversion_metric" AS "conversion_metric",
  R."ga4_cost_based_metric" AS "cost_based_metric",
  'Google Analytics v4' AS "data_source",
  'reports_ga4_base_performance_v2' AS "data_source_details",
  R."date",
  R."active_users",
  R."conversions"
FROM
  {{ ref('mint_unified_all_platforms_taxonomy_view') }} AS M
  JOIN {{ ref('ga4_mappings_reports_pre_merge_events_view') }} AS R ON M."journey_id" = R."ga4_journey_id"
  AND M."channel_name" = R."ga4_channel_name"
  AND M."platform_name" = R."ga4_platform_name"
  AND M."granularity_level" = R."ga4_grouping_level"
WHERE
  M."granularity_level" = 'platform'
  AND (
    R."date" BETWEEN M."media_plan_date_from" AND M."media_plan_date_to"
  )
