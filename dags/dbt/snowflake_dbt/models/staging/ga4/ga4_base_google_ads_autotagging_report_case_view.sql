
WITH
  LATEST_GA4_AUTOTAGGING_BASE_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_GA4_AUTOTAG_SESSIONS_PERFORMANCE_V2
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_ga4_autotag_sessions_performance_v2'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  GOOGLE_ADS_TAXONOMY AS (
    SELECT
      *
    FROM
      {{ ref('mint_unified_all_platforms_taxonomy_view') }}
    WHERE
      "platform_name" IN ('adwords', 'youtube_google_ads_integrated')
  ),
  GA4_MAPPINGS AS (
    SELECT DISTINCT
      "ga4_property_external_id",
      "ga4_tile_id"
    FROM
      {{ ref('ga4_autotagging_view') }}
  ),
  BASE_REPORTS_AD_LEVEL AS (
    SELECT
      "date",
      "property_id",
      "adwords_campaign_id",
      "adwords_adgroup_id",
      "adwords_ad_id",
      SUM("sessions") AS "sessions",
      SUM("total_revenue") AS "total_revenue",
      SUM("transactions") AS "transactions",
      SUM("engaged_sessions") AS "engaged_sessions",
      SUM("gross_purchase_revenue") AS "gross_purchase_revenue"
    FROM
      LATEST_GA4_AUTOTAGGING_BASE_REPORTS
    GROUP BY
      "date",
      "property_id",
      "adwords_campaign_id",
      "adwords_adgroup_id",
      "adwords_ad_id"
  ),
  BASE_REPORTS_CAMPAIGN_LEVEL AS (
    SELECT
      "date",
      "property_id",
      "adwords_campaign_id",
      SUM("sessions") AS "sessions",
      SUM("total_revenue") AS "total_revenue",
      SUM("transactions") AS "transactions",
      SUM("engaged_sessions") AS "engaged_sessions",
      SUM("gross_purchase_revenue") AS "gross_purchase_revenue"
    FROM
      LATEST_GA4_AUTOTAGGING_BASE_REPORTS
    GROUP BY
      "date",
      "property_id",
      "adwords_campaign_id"
  ),
  PERFMAX_AUTOTAGGING AS (
    SELECT
      M."company_id",
      M."journey_id",
      M."journey_name",
      M."media_plan_id",
      M."media_plan_name",
      M."channel_id",
      M."channel_name",
      M."platform_id",
      M."platform_name",
      M."tile_id",
      M."tile_name",
      M."media_row_id",
      M."media_row_name",
      M."platform_campaign_external_id" AS "platform_campaign_id",
      M."platform_campaign_name" AS "platform_campaign_name",
      M."platform_campaign_external_group_id" AS "platform_campaign_group_id",
      M."platform_campaign_group_name" AS "platform_campaign_group_name",
      M."ad_asset_external_id" AS "ad_asset_id",
      M."ad_asset",
      M."currency",
      'Google Analytics v4' AS "data_source",
      'reports_ga4_autotag_sessions_performance_v2' AS "data_source_details",
      R."date",
      R."sessions" AS "ga_sessions",
      R."total_revenue",
      R."transactions",
      R."engaged_sessions",
      R."gross_purchase_revenue"
    FROM
      GOOGLE_ADS_TAXONOMY AS M
      JOIN GA4_MAPPINGS AS ATMP ON M."tile_id" = ATMP."ga4_tile_id"
      JOIN BASE_REPORTS_CAMPAIGN_LEVEL AS R ON M."platform_campaign_external_id" = R."adwords_campaign_id"
      AND ATMP."ga4_property_external_id" = R."property_id"
    WHERE
      M."granularity_level" = 'perfmax'
      AND (
        R."date" BETWEEN M."media_row_date_from" AND M."media_row_date_to"
      )
  ),
  AD_AUTOTAGGING AS (
    SELECT
      M."company_id",
      M."journey_id",
      M."journey_name",
      M."media_plan_id",
      M."media_plan_name",
      M."channel_id",
      M."channel_name",
      M."platform_id",
      M."platform_name",
      M."tile_id",
      M."tile_name",
      M."media_row_id",
      M."media_row_name",
      M."platform_campaign_external_id" AS "platform_campaign_id",
      M."platform_campaign_name" AS "platform_campaign_name",
      M."platform_campaign_external_group_id" AS "platform_campaign_group_id",
      M."platform_campaign_group_name" AS "platform_campaign_group_name",
      M."ad_asset_external_id" AS "ad_asset_id",
      M."ad_asset",
      M."currency",
      'Google Analytics v4' AS "data_source",
      'reports_ga4_autotag_sessions_performance_v2' AS "data_source_details",
      R."date",
      R."sessions" AS "ga_sessions",
      R."total_revenue",
      R."transactions",
      R."engaged_sessions",
      R."gross_purchase_revenue"
    FROM
      GOOGLE_ADS_TAXONOMY AS M
      JOIN GA4_MAPPINGS AS ATMP ON M."tile_id" = ATMP."ga4_tile_id"
      JOIN BASE_REPORTS_AD_LEVEL AS R ON M."platform_campaign_external_id" = R."adwords_campaign_id"
      AND M."platform_campaign_external_group_id" = R."adwords_adgroup_id"
      AND M."ad_asset_external_id" = R."adwords_ad_id"
      AND ATMP."ga4_property_external_id" = R."property_id"
    WHERE
      M."granularity_level" = 'ad'
      AND (
        R."date" BETWEEN M."media_row_date_from" AND M."media_row_date_to"
      )
  )
SELECT
  *
FROM
  PERFMAX_AUTOTAGGING
UNION ALL
SELECT
  *
FROM
  AD_AUTOTAGGING