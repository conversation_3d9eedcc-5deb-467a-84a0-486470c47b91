
WITH
  LATEST_GA4_INTEGRATIONS AS (
    SELECT
      *
    FROM
      GA4.INTEGRATIONS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'integrations'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_GA4_CONFIGS AS (
    SELECT
      *
    FROM
      GA4.UTM_CONFIGS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'utm_configs'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_GA4_GROUPINGS AS (
    SELECT
      *
    FROM
      GA4.GROUPINGS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'groupings'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_GA4_RULES AS (
    SELECT
      *
    FROM
      GA4.RULES
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'rules'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_GA4_CONVERSIONS AS (
    SELECT
      *
    FROM
      GA4.CONVERSIONS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'conversions'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  )
SELECT DISTINCT
  I."company_id",
  I."id" AS "ga4_integration_internal_id",
  I."account_id" AS "ga4_account_external_id",
  I."property_id" AS "ga4_property_external_id",
  I."is_active" AS "ga4_integration_is_active",
  S."id" AS "ga4_settings_id",
  S."campaign_collection_id" AS "ga4_journey_id",
  S."is_automatic_mapping_enabled" AS "ga4_is_auto_tagging_enabled",
  G."channel" AS "ga4_channel_name",
  G."platform" AS "ga4_platform_name",
  G."level" AS "ga4_grouping_level",
  COALESCE(R."tile_id", R."parent_tile_id") AS "ga4_tile_id",
  COALESCE(R."platform_item_id", R."tile_id") AS "ga4_platform_item_id",
  COALESCE(R."platform_item_external_id", R."tile_id") AS "ga4_platform_item_external_id",
  C."name" AS "ga4_event_name",
  C."conversion_metric" AS "ga4_conversion_metric",
  C."cost_based_metric" AS "ga4_cost_based_metric"
FROM
  LATEST_GA4_INTEGRATIONS AS I
  JOIN LATEST_GA4_CONFIGS AS S ON I."id" = S."integration_id"
  JOIN LATEST_GA4_GROUPINGS AS G ON S."id" = G."utm_config_id"
  JOIN LATEST_GA4_RULES AS R ON S."id" = R."utm_config_id"
  AND R."grouping_id" = G."id"
  AND CAST(R."grouping_level" AS TEXT) = CAST(G."level" AS TEXT)
  JOIN LATEST_GA4_CONVERSIONS AS C ON C."utm_config_id" = S."id"
WHERE
  "ga4_is_auto_tagging_enabled" = True
  AND "ga4_platform_name" IN ('adwords', 'youtube_google_ads_integrated')