WITH
  LATEST_GA4_INTEGRATIONS AS (
    SELECT
      *
    FROM
      GA4.INTEGRATIONS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'integrations'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_GA4_CONFIGS AS (
    SELECT
      *
    FROM
      GA4.UTM_CONFIGS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'utm_configs'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_GA4_GROUPINGS AS (
    SELECT
      *
    FROM
      GA4.GROUPINGS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'groupings'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_GA4_RULES AS (
    SELECT
      *
    FROM
      GA4.RULES
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'rules'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_GA4_CONVERSIONS AS (
    SELECT
      *
    FROM
      GA4.CONVERSIONS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'GA4'
              AND "TABLE_NAME" = 'conversions'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  GA4_BASE_VIEW AS (
    SELECT
      I."company_id",
      I."id" AS "ga4_integration_internal_id",
      I."account_id" AS "ga4_account_external_id",
      I."property_id" AS "ga4_property_external_id",
      I."is_active" AS "ga4_integration_is_active",
      S."id" AS "ga4_settings_id",
      S."campaign_collection_id" AS "ga4_journey_id",
      S."is_automatic_mapping_enabled" AS "ga4_is_auto_tagging_enabled",
      G."channel" AS "ga4_channel_name",
      G."platform" AS "ga4_platform_name",
      CASE
        WHEN G."level" IN ('ad_group', 'ad_set', 'io') THEN 'adgroup'
        WHEN G."level" IN ('li') THEN 'ad'
        WHEN G."level" IN ('ad')
        AND G."platform" = 'youtube_dv360_integrated' THEN 'youtube_dv360_ad'
        ELSE G."level"
      END AS "ga4_grouping_level",
      COALESCE(R."tile_id", R."parent_tile_id") AS "ga4_tile_id",
      COALESCE(R."platform_item_id", R."tile_id") AS "ga4_platform_item_id",
      COALESCE(R."platform_item_external_id", R."tile_id") AS "ga4_platform_item_external_id",
      R."sub_rules" AS "ga4_rules"
    FROM
      LATEST_GA4_INTEGRATIONS AS I
      JOIN LATEST_GA4_CONFIGS AS S ON I."id" = S."integration_id"
      JOIN LATEST_GA4_GROUPINGS AS G ON S."id" = G."utm_config_id"
      JOIN LATEST_GA4_RULES AS R ON S."id" = R."utm_config_id"
      AND R."grouping_id" = G."id"
      AND CAST(R."grouping_level" AS TEXT) = CAST(G."level" AS TEXT)
  ),
  UNNESTED_RULESETS AS (
    SELECT
      VW."ga4_property_external_id",
      VW."company_id",
      VW."ga4_integration_internal_id",
      VW."ga4_settings_id",
      VW."ga4_journey_id",
      VW."ga4_is_auto_tagging_enabled",
      VW."ga4_channel_name",
      VW."ga4_platform_name",
      VW."ga4_grouping_level",
      VW."ga4_tile_id",
      VW."ga4_platform_item_id",
      VW."ga4_platform_item_external_id",
      VW."ga4_rules",
      row_number() OVER (
        ORDER BY
          VW."ga4_journey_id"
      ) AS "row_id",
      CAST(JSON_DATA.VALUE AS TEXT) AS "value"
    FROM
      GA4_BASE_VIEW AS VW,
      LATERAL FLATTEN (
        input => PARSE_JSON (REPLACE(VW."ga4_rules", 'None', 'null'))
      ) AS JSON_DATA
  ),
  UNPACKED_UTM_TYPES AS (
    SELECT
      UR.*,
      "flattened".key AS "utm_type",
      "flattened".value::STRING AS "utm_rule_temp"
    FROM
      UNNESTED_RULESETS AS UR,
      LATERAL FLATTEN (input => PARSE_JSON (UR."value")) AS "flattened"
  ),
  GA4_MAPPINGS AS (
    SELECT
      UT."ga4_property_external_id",
      UT."company_id",
      UT."ga4_integration_internal_id",
      UT."ga4_settings_id",
      UT."ga4_journey_id",
      UT."ga4_is_auto_tagging_enabled",
      UT."ga4_channel_name",
      UT."ga4_platform_name",
      UT."ga4_grouping_level",
      UT."ga4_tile_id",
      UT."ga4_platform_item_id",
      UT."ga4_platform_item_external_id",
      C."name" AS "ga4_event_name",
      C."conversion_metric" AS "ga4_conversion_metric",
      C."cost_based_metric" AS "ga4_cost_based_metric",
      UT."utm_type",
      "nested".key AS "utm_rule",
      CASE
        WHEN "nested".key = 'contains' THEN CAST("nested".value[0] AS TEXT)
        WHEN "nested".key = 'equals' THEN CAST("nested".value AS TEXT)
      END AS "utm_value",
      UT."row_id",
      ROW_NUMBER() OVER (
        ORDER BY
          UT."row_id"
      ) AS "utm_parameter_id"
    FROM
      UNPACKED_UTM_TYPES AS UT
      JOIN LATERAL FLATTEN (input => PARSE_JSON (UT."utm_rule_temp")) AS "nested"
      LEFT JOIN LATEST_GA4_CONVERSIONS AS C ON C."utm_config_id" = UT."ga4_settings_id"
    WHERE
      UT."utm_rule_temp" IS NOT NULL
  ),
  -- Mocking madness begins here:
  -- This part is needed to mock some specific conversion mappings for specific clients in order to pull data
  -- This was done in old pipeline as a quick resolution with proper implementation in the future
  MAX_IDS AS (
    SELECT
      MAX("row_id") AS "max_row_id",
      MAX("utm_parameter_id") AS "max_utm_parameter_id"
    FROM
      GA4_MAPPINGS
  ),
  PROPERTY_FILTERED AS (
    SELECT
      *
    FROM
      GA4_MAPPINGS
    WHERE
      "ga4_property_external_id" = *********
  ),
  CUSTOMIZED AS (
    SELECT
      "ga4_property_external_id",
      "company_id",
      "ga4_integration_internal_id",
      "ga4_settings_id",
      "ga4_journey_id",
      "ga4_is_auto_tagging_enabled",
      "ga4_channel_name",
      "ga4_platform_name",
      "ga4_grouping_level",
      "ga4_tile_id",
      "ga4_platform_item_id",
      "ga4_platform_item_external_id",
      'revenue_for_mint' AS "ga4_event_name",
      'revenue_for_mint' AS "ga4_conversion_metric",
      'CP ' || 'revenue_for_mint' AS "ga4_cost_based_metric",
      "utm_type",
      "utm_rule",
      "utm_value",
      "row_id",
      "utm_parameter_id"
    FROM
      PROPERTY_FILTERED
  ),
  REMAPPED_ROWS AS (
    SELECT
      *,
      MAX_IDS."max_row_id" + ROW_NUMBER() OVER (
        ORDER BY
          "row_id"
      ) AS "new_row_id"
    FROM
      CUSTOMIZED,
      MAX_IDS
  ),
  MOCKED_WITH_IDS AS (
    SELECT
      *,
      MAX_IDS."max_utm_parameter_id" + ROW_NUMBER() OVER (
        ORDER BY
          "new_row_id"
      ) AS "new_utm_parameter_id"
    FROM
      REMAPPED_ROWS,
      MAX_IDS
  )
SELECT
  -- override values from mock
  "ga4_property_external_id",
  "company_id",
  "ga4_integration_internal_id",
  "ga4_settings_id",
  "ga4_journey_id",
  "ga4_is_auto_tagging_enabled",
  "ga4_channel_name",
  "ga4_platform_name",
  "ga4_grouping_level",
  "ga4_tile_id",
  "ga4_platform_item_id",
  "ga4_platform_item_external_id",
  "ga4_event_name",
  "ga4_conversion_metric",
  "ga4_cost_based_metric",
  "utm_type",
  "utm_rule",
  "utm_value",
  "new_row_id" AS "row_id",
  "new_utm_parameter_id" AS "utm_parameter_id"
FROM
  MOCKED_WITH_IDS
UNION ALL
SELECT
  *
FROM
  GA4_MAPPINGS