
SELECT
  M."company_id",
  <PERSON><PERSON>"journey_id",
  <PERSON>."journey_name",
  <PERSON>."media_plan_id",
  <PERSON>."media_plan_name",
  <PERSON>."channel_id",
  <PERSON>."channel_name",
  <PERSON>."platform_id",
  <PERSON>."platform_name",
  <PERSON><PERSON>"tile_id",
  <PERSON><PERSON>"tile_name",
  <PERSON>."media_row_id",
  <PERSON>."media_row_name",
  <PERSON>."platform_campaign_external_id" AS "platform_campaign_id",
  <PERSON>."platform_campaign_name" AS "platform_campaign_name",
  <PERSON>."platform_campaign_external_group_id" AS "platform_campaign_group_id",
  M."platform_campaign_group_name" AS "platform_campaign_group_name",
  M."ad_asset_external_id" AS "ad_asset_id",
  M."ad_asset",
  M."currency",
  R."utm_source",
  R."utm_medium",
  R."utm_campaign",
  R."utm_term",
  R."utm_content",
  'Google Analytics v4' AS "data_source",
  'reports_ga4_base_sessions_performance_v2' AS "data_source_details",
  R."date",
  <PERSON>."sessions" AS "ga_sessions",
  R."total_revenue",
  <PERSON>."transactions",
  R."engaged_sessions",
  R."gross_purchase_revenue"
FROM
  {{ ref('mint_unified_all_platforms_taxonomy_view') }} AS M
  JOIN {{ ref('ga4_mappings_reports_pre_merge_sessions_view') }} AS R ON M."journey_id" = R."ga4_journey_id"
  AND M."channel_name" = R."ga4_channel_name"
  AND M."platform_name" = R."ga4_platform_name"
  AND M."granularity_level" = R."ga4_grouping_level"
  AND M."ad_asset_internal_id" = R."ga4_platform_item_id"
  AND M."ad_asset_external_id" = R."ga4_platform_item_external_id"
WHERE
  R."date" BETWEEN M."media_row_date_from" AND M."media_row_date_to"