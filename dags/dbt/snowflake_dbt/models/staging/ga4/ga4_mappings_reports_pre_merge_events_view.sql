
WITH
  LATEST_GA4_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_GA4_BASE_PERFORMANCE_V2
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_ga4_base_performance_v2'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  -- Step #1: Rename columns in LATEST_GA4_REPORTS
  LATEST_GA4_REPORTS_RENAMED AS (
    SELECT
      "property_id",
      "event_name",
      "date",
      "source" AS "utm_source",
      "medium" AS "utm_medium",
      "manual_term" AS "utm_term",
      "ad_content" AS "utm_content",
      "campaign_name" AS "utm_campaign",
      "conversions",
      "active_users"
    FROM
      LATEST_GA4_REPORTS
  ),
  -- Step #2: Unpivot UTM columns into utm_type and utm_value_from_reports
  UNPIVOTED_CONVERSIONS AS (
    SELECT
      *,
      'utm_source' AS "utm_type",
      "utm_source" AS "utm_value_from_reports"
    FROM
      LATEST_GA4_REPORTS_RENAMED
    UNION ALL
    SELECT
      *,
      'utm_medium',
      "utm_medium"
    FROM
      LATEST_GA4_REPORTS_RENAMED
    UNION ALL
    SELECT
      *,
      'utm_campaign',
      "utm_campaign"
    FROM
      LATEST_GA4_REPORTS_RENAMED
    UNION ALL
    SELECT
      *,
      'utm_term',
      "utm_term"
    FROM
      LATEST_GA4_REPORTS_RENAMED
    UNION ALL
    SELECT
      *,
      'utm_content',
      "utm_content"
    FROM
      LATEST_GA4_REPORTS_RENAMED
  ),
  -- Step #3: Filter out null utm_value and calculate rule_count
  RULES_WITH_COUNT AS (
    SELECT
      *,
      COUNT(DISTINCT "utm_parameter_id") OVER (
        PARTITION BY
          "ga4_property_external_id",
          "row_id",
          "ga4_journey_id",
          "ga4_event_name"
        ORDER BY
          "utm_parameter_id" ROWS BETWEEN UNBOUNDED PRECEDING
          AND UNBOUNDED FOLLOWING
      ) AS "rule_count"
    FROM
      {{ ref('ga4_mappings_view') }}
    WHERE
      "utm_value" IS NOT NULL
      AND NOT (
        "ga4_is_auto_tagging_enabled" = True
        AND "ga4_platform_name" IN ('adwords', 'youtube_google_ads_integrated')
      )
  ),
  -- Step #4: Join with GA4 rules and apply rule filters
  FILTERED_RULES AS (
    SELECT
      R.*,
      C."date",
      C."event_name",
      C."conversions",
      C."active_users",
      C."utm_value_from_reports",
      C."utm_type",
      C."utm_source",
      C."utm_medium",
      C."utm_campaign",
      C."utm_term",
      C."utm_content"
    FROM
      RULES_WITH_COUNT R
      INNER JOIN UNPIVOTED_CONVERSIONS C ON R."ga4_property_external_id" = C."property_id"
      AND R."utm_type" = C."utm_type"
    WHERE
      (
        (
          R."utm_rule" = 'contains'
          AND POSITION(R."utm_value" IN C."utm_value_from_reports") > 0
          AND R."ga4_event_name" = C."event_name"
        )
        OR (
          R."utm_rule" = 'equals'
          AND C."utm_value_from_reports" = R."utm_value"
          AND R."ga4_event_name" = C."event_name"
        )
      )
  ),
  -- Step #5: Validate rule match count and return final conversions
  FINAL_CONVERSIONS AS (
    SELECT
      *
    FROM
      (
        SELECT
          *,
          COUNT(DISTINCT "utm_parameter_id") OVER (
            PARTITION BY
              "date",
              "ga4_property_external_id",
              "ga4_event_name",
              "row_id",
              "ga4_journey_id",
              "utm_source",
              "utm_medium",
              "utm_campaign",
              "utm_term",
              "utm_content"
            ORDER BY
              "utm_parameter_id" ROWS BETWEEN UNBOUNDED PRECEDING
              AND UNBOUNDED FOLLOWING
          ) AS "rule_count_matched"
        FROM
          FILTERED_RULES
      )
    WHERE
      "rule_count" = "rule_count_matched"
  )
SELECT DISTINCT
  "ga4_property_external_id",
  "company_id",
  "ga4_integration_internal_id",
  "ga4_settings_id",
  "ga4_journey_id",
  "ga4_is_auto_tagging_enabled",
  "ga4_channel_name",
  "ga4_platform_name",
  "ga4_grouping_level",
  "ga4_tile_id",
  "ga4_platform_item_id",
  "ga4_platform_item_external_id",
  "ga4_event_name",
  "ga4_conversion_metric",
  "ga4_cost_based_metric",
  "date",
  "utm_source",
  "utm_medium",
  "utm_campaign",
  "utm_term",
  "utm_content",
  "conversions",
  "active_users"
FROM
  FINAL_CONVERSIONS