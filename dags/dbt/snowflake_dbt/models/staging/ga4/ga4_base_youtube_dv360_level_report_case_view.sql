
WITH
  DATINTELL_TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "media_plan_date_from",
      "media_plan_date_to",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  DV360_YOUTUBE_AD_LEVEL AS (
    SELECT DISTINCT
      "dv360_tile_id",
      CAST("dv360_internal_campaign_id" AS TEXT) AS "platform_campaign_internal_id",
      CAST("dv360_external_campaign_id" AS TEXT) AS "platform_campaign_external_id",
      CAST("dv360_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("dv360_internal_insertion_order_id" AS TEXT) AS "platform_campaign_internal_group_id",
      CAST("dv360_external_insertion_order_id" AS TEXT) AS "platform_campaign_external_group_id",
      CAST("dv360_insertion_order_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("dv360_internal_li_id" AS TEXT) AS "ad_asset_internal_id",
      CAST("dv360_external_li_id" AS TEXT) AS "ad_asset_external_id",
      CAST("dv360_li_name" AS TEXT) AS "ad_asset",
      CAST("dv360_internal_adgroup_id" AS TEXT) AS "dv360_internal_adgroup_id",
      CAST("dv360_external_adgroup_id" AS TEXT) AS "dv360_external_adgroup_id",
      CAST("dv360_internal_ad_id" AS TEXT) AS "dv360_internal_ad_id",
      CAST("dv360_external_ad_id" AS TEXT) AS "dv360_external_ad_id",
      CAST("dv360_currency_code" AS TEXT) AS "currency"
    FROM
      {{ ref('dv360_taxonomy_view') }}
    WHERE
      "dv360_external_li_id" IS NOT NULL
  ),
  INTEGRATED_TAXONOMY_DV360_YOUTUBE_AD_LEVEL AS (
    SELECT
      *
    FROM
      DATINTELL_TAXONOMY AS DT
      JOIN DV360_YOUTUBE_AD_LEVEL AS DLIL ON DT."tile_id" = DLIL."dv360_tile_id"
  )
SELECT
  M."company_id",
  M."journey_id",
  M."journey_name",
  M."media_plan_id",
  M."media_plan_name",
  M."channel_id",
  M."channel_name",
  M."platform_id",
  M."platform_name",
  M."tile_id",
  M."tile_name",
  M."media_row_id",
  M."media_row_name",
  M."platform_campaign_external_id" AS "platform_campaign_id",
  M."platform_campaign_name" AS "platform_campaign_name",
  M."platform_campaign_external_group_id" AS "platform_campaign_group_id",
  M."platform_campaign_group_name" AS "platform_campaign_group_name",
  M."ad_asset_external_id" AS "ad_asset_id",
  M."ad_asset",
  M."currency",
  R."utm_source",
  R."utm_medium",
  R."utm_campaign",
  R."utm_term",
  R."utm_content",
  'Google Analytics v4' AS "data_source",
  'reports_ga4_base_sessions_performance_v2' AS "data_source_details",
  R."date",
  SUM(R."sessions")  AS "ga_sessions",
  SUM(R."total_revenue") AS "total_revenue",
  SUM(R."transactions") AS "transactions",
  SUM(R."engaged_sessions") AS "engaged_sessions",
  SUM(R."gross_purchase_revenue") AS "gross_purchase_revenue"
FROM
  INTEGRATED_TAXONOMY_DV360_YOUTUBE_AD_LEVEL AS M
  JOIN {{ ref('ga4_mappings_reports_pre_merge_sessions_view') }} AS R ON M."journey_id" = R."ga4_journey_id"
  AND M."channel_name" = R."ga4_channel_name"
  AND M."platform_name" = R."ga4_platform_name"
  AND M."dv360_internal_ad_id" = R."ga4_platform_item_id"
  AND M."dv360_external_ad_id" = R."ga4_platform_item_external_id"
WHERE
  R."date" BETWEEN M."media_row_date_from" AND M."media_row_date_to"
GROUP BY
  M."company_id",
  M."journey_id",
  M."journey_name",
  M."media_plan_id",
  M."media_plan_name",
  M."channel_id",
  M."channel_name",
  M."platform_id",
  M."platform_name",
  M."tile_id",
  M."tile_name",
  M."media_row_id",
  M."media_row_name",
  M."platform_campaign_external_id",
  M."platform_campaign_name",
  M."platform_campaign_external_group_id",
  M."platform_campaign_group_name",
  M."ad_asset_external_id",
  M."ad_asset",
  M."currency",
  R."utm_source",
  R."utm_medium",
  R."utm_campaign",
  R."utm_term",
  R."utm_content",
  "data_source",
  "data_source_details",
  R."date"