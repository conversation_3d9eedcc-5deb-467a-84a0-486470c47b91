
WITH
  LATEST_SUB_PRICINGS AS (
    SELECT
      *
    FROM
      DATINTELL.CAMPAIGN_GHOSTCAMPAIGNPRICINGSUBITEM
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'campaign_ghostcampaignpricingsubitem'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  INTEGRATED_TAXONOMY_PRICING_ITEM_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "tile_id",
      "tile_name",
      "platform_campaign_internal_id",
      "platform_campaign_external_id",
      "platform_campaign_name",
      "platform_campaign_internal_group_id",
      "platform_campaign_external_group_id",
      "platform_campaign_group_name",
      "ad_asset_internal_id",
      "ad_asset_external_id",
      "ad_asset",
      "currency",
      "granularity_level",
      "pricing_item_type"
    FROM
      {{ ref('mint_unified_all_platforms_taxonomy_view') }}
    WHERE
      "granularity_level" IN ('pricing_item')
  ),
  TAXONOMY_PRICING_ITEMS_WITH_PRICE AS (
    SELECT
      TX.*,
      SB."date" AS "sub_pricing_date",
      SB."price" AS "price"
    FROM
      INTEGRATED_TAXONOMY_PRICING_ITEM_LEVEL AS TX
      LEFT JOIN LATEST_SUB_PRICINGS AS SB
      ON CAST(TX."platform_campaign_internal_id" AS TEXT) = CAST(SB."pricing_item_id" AS TEXT)
      AND CAST(TX."platform_campaign_external_id" AS TEXT) = CAST(SB."pricing_item_id" AS TEXT)
  )
SELECT
  M."company_id",
  M."journey_id",
  M."journey_name",
  M."media_plan_id",
  M."media_plan_name",
  M."channel_id",
  M."channel_name",
  M."platform_id",
  M."platform_name",
  M."tile_id",
  M."tile_name",
  M."media_row_id",
  M."media_row_name",
  CAST(M."platform_campaign_external_id" AS TEXT) AS "platform_campaign_id",
  CAST(M."platform_campaign_name" AS TEXT) AS "platform_campaign_name",
  CAST(M."platform_campaign_external_group_id" AS TEXT) AS "platform_campaign_group_id",
  CAST(M."platform_campaign_group_name" AS TEXT) AS "platform_campaign_group_name",
  CAST(M."ad_asset_external_id" AS TEXT) AS "ad_asset_id",
  CAST(M."ad_asset" AS TEXT) AS "ad_asset",
  M."currency",
  R."utm_source",
  R."utm_medium",
  R."utm_campaign",
  R."utm_term",
  R."utm_content",
  R."ga4_conversion_metric" AS "conversion_metric",
  R."ga4_cost_based_metric" AS "cost_based_metric",
  'Google Analytics v4' AS "data_source",
  'reports_ga4_base_performance_v2' AS "data_source_details",
  R."date",
  R."active_users",
  R."conversions"
FROM
  TAXONOMY_PRICING_ITEMS_WITH_PRICE AS M
  JOIN {{ ref('ga4_mappings_reports_pre_merge_events_view') }} AS R ON M."journey_id" = R."ga4_journey_id"
  AND M."channel_name" = R."ga4_channel_name"
  AND M."platform_name" = R."ga4_platform_name"
  AND M."granularity_level" = R."ga4_grouping_level"
  AND CAST(M."platform_campaign_internal_id" AS TEXT) = CAST(R."ga4_platform_item_id" AS TEXT)
  AND CAST(M."platform_campaign_external_id" AS TEXT) = CAST(R."ga4_platform_item_external_id" AS TEXT)
  AND M."sub_pricing_date" = R."date"
WHERE
  R."date" BETWEEN M."media_row_date_from" AND M."media_row_date_to"