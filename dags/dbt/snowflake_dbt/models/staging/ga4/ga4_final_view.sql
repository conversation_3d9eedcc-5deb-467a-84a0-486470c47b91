{{ union_models([
    'ga4_base_platform_level_report_case_view',
    'ga4_base_media_row_level_report_case_view',
    'ga4_base_campaign_level_integrated_report_case_view',
    'ga4_base_campaign_level_virtual_report_case_view',
    'ga4_base_adgroup_level_report_case_view',
    'ga4_base_ad_level_report_case_view',
    'ga4_base_youtube_dv360_level_report_case_view',
    'ga4_events_platform_level_report_case_view',
    'ga4_events_media_row_level_report_case_view',
    'ga4_events_campaign_level_integrated_report_case_view',
    'ga4_events_campaign_level_virtual_report_case_view',
    'ga4_events_adgroup_level_report_case_view',
    'ga4_events_ad_level_report_case_view',
    'ga4_events_youtube_dv360_level_report_case_view',
    'ga4_base_google_ads_autotagging_report_case_view',
    'ga4_events_google_ads_autotagging_report_case_view'
],
[
    'company_id',
    'journey_id',
    'journey_name',
    'media_plan_id',
    'media_plan_name',
    'channel_id',
    'channel_name',
    'platform_id',
    'platform_name',
    'tile_id',
    'tile_name',
    'media_row_id',
    'media_row_name',
    'platform_campaign_id',
    'platform_campaign_name',
    'platform_campaign_group_id',
    'platform_campaign_group_name',
    'ad_asset_id',
    'ad_asset',
    'utm_source',
    'utm_medium',
    'utm_campaign',
    'utm_term',
    'utm_content',
    'currency',
    'conversion_metric',
    'cost_based_metric',
    'data_source',
    'data_source_details',
    'date',
    'spend',
    'ga_sessions',
    'engaged_sessions',
    'gross_purchase_revenue',
    'total_revenue',
    'transactions',
    'active_users',
    'conversions'
]) }}
