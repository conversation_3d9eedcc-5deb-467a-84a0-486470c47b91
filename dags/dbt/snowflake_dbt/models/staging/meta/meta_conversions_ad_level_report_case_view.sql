
WITH LATEST_CONVERSION_META_REPORTS AS (
    SELECT * FROM  REPORTS.META_CONVERSIONS_REPORT
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'meta_conversions_report'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_META_DEFAULT_CONVERSION_LABELS AS (
    SELECT * FROM  REPORTS.META_DEFAULT_CONVERSIONS_MAPPING
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'meta_default_conversions_mapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_META_CUSTOM_CONVERSION_LABELS AS (
    SELECT * FROM  REPORTS.META_CONVERSIONS_MAPPING
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'meta_conversions_mapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  META_AD_LEVEL AS (
    SELECT DISTINCT
      "meta_tile_id",
      "meta_external_campaign_id",
      "meta_campaign_name",
      "meta_external_adset_id",
      "meta_adset_name",
      "meta_external_ad_id",
      "meta_ad_name",
      "meta_currency_code"
    FROM
      {{ ref('meta_taxonomy_view') }}
    WHERE
      "meta_external_ad_id" IS NOT NULL
  ),
CONVERSION_REPORT_WITH_LABELS AS (
    SELECT
      CR."date",
      CR."campaign_id",
      CR."ad_set_id",
      CR."ad_id",
      CR."conversion_currency",
      COALESCE(CCM."conversion_label", CR."conversion_name") AS "conversion_label",
      SUM(CR."value") AS "conversions",
      SUM(CR."revenue") AS "conversions_revenue"
    FROM
      LATEST_CONVERSION_META_REPORTS as CR
      LEFT JOIN LATEST_META_CUSTOM_CONVERSION_LABELS as CCM ON CR."account_id" = REPLACE(CCM."account_id", 'act_', '')
      AND REGEXP_SUBSTR(CR."conversion_name", '[^.]+$', 1) = CCM."conversion_id"  WHERE CR."conversion_name" LIKE '%.custom.%'
    GROUP BY
      CR."date",
      CR."campaign_id",
      CR."ad_set_id",
      CR."ad_id",
      CR."conversion_currency",
      COALESCE(CCM."conversion_label", CR."conversion_name")
    UNION ALL
    SELECT
      CR."date",
      CR."campaign_id",
      CR."ad_set_id",
      CR."ad_id",
      CR."conversion_currency",
      DCM."conversion_label",
      SUM(CR."value") AS "conversions",
      SUM(CR."revenue") AS "conversions_revenue"
    FROM
      LATEST_CONVERSION_META_REPORTS as CR
      JOIN LATEST_META_DEFAULT_CONVERSION_LABELS AS DCM ON CR."conversion_name" = DCM."conversion_name"
    WHERE
      DCM."is_needed" = True
    GROUP BY
      CR."date",
      CR."campaign_id",
      CR."ad_set_id",
      CR."ad_id",
      CR."conversion_currency",
      DCM."conversion_label"
  )

    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      TX."tile_id",
      "tile_name",
      CAST("meta_external_campaign_id" AS TEXT) AS "platform_campaign_id",
      CAST("meta_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("meta_external_adset_id" AS TEXT) AS "platform_campaign_group_id",
      CAST("meta_adset_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("meta_external_ad_id" AS TEXT) AS "ad_asset_id",
      CAST("meta_ad_name" AS TEXT) AS "ad_asset",
      COALESCE("meta_currency_code","mediarow_currency") AS "currency",
      'Meta' AS "data_source",
      'meta_conversions_report' AS "data_source_details",
      "date",
      "conversion_label" AS "conversion_metric",
      "conversions",
      "conversions_revenue"
    FROM
      TAXONOMY AS tx
      JOIN META_AD_LEVEL AS MT_AD ON TX."tile_id" = MT_AD."meta_tile_id"
      JOIN CONVERSION_REPORT_WITH_LABELS as MT_C_REP ON CAST(MT_AD."meta_external_campaign_id" AS TEXT) = CAST(MT_C_REP."campaign_id" AS TEXT)
      AND CAST(MT_AD."meta_external_adset_id" AS TEXT) = CAST(MT_C_REP."ad_set_id" AS TEXT)
      AND CAST(MT_AD."meta_external_ad_id" AS TEXT) = CAST(MT_C_REP."ad_id" AS TEXT)
    WHERE
      MT_C_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"