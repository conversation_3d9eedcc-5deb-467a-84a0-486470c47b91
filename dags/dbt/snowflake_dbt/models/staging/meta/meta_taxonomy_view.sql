
WITH LATEST_INTEGRATION AS (
    SELECT * FROM  FACEBOOK_INSTAGRAM.ACCOUNT_FACEBOOKEXTERNALDATA
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'FACEBOOK_INSTAGRAM'
              AND "TABLE_NAME" = 'account_facebookexternaldata'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_CAMPAIGN AS (
    SELECT * FROM  FACEBOOK_INSTAGRAM.CAMPAIGN_FACEBOOKCAMPAIGN
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'FACEBOOK_INSTAGRAM'
              AND "TABLE_NAME" = 'campaign_facebookcampaign'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_ADSET AS (
    SELECT * FROM  FACEBOOK_INSTAGRAM.CAMPAIGN_FACEBOOKADSETMODEL
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'FACEBOOK_INSTAGRAM'
              AND "TABLE_NAME" = 'campaign_facebookadsetmodel'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
LATEST_AD AS (
    SELECT * FROM  FACEBOOK_INSTAGRAM.CAMPAIGN_FACEBOOKAD
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'FACEBOOK_INSTAGRAM'
              AND "TABLE_NAME" = 'campaign_facebookad'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
)

SELECT
  CAMP."social_campaign_id" AS "meta_tile_id",
  INTGR."id" AS "meta_integration_id",
  INTGR."currency" AS "meta_currency_code",
  CAMP."id" AS "meta_internal_campaign_id",
  CAMP."external_campaign_id" AS "meta_external_campaign_id",
  CAMP."name" AS "meta_campaign_name",
  CAMP."is_budget_optimization" AS "meta_is_budget_optimization",
  CAMP."budget" AS "meta_campaign_lifetime_budget",
  CAMP."daily_budget" AS "meta_campaign_daily_budget",
  CAMP."status" AS "meta_campaign_status",
  ADSET."id" AS "meta_internal_adset_id",
  ADSET."external_ad_set_id" AS "meta_external_adset_id",
  ADSET."name" AS "meta_adset_name",
  ADSET."date_from" AS "meta_adset_date_from",
  ADSET."date_to" AS "meta_adset_date_to",
  ADSET."budget" AS "meta_adset_lifetime_budget",
  ADSET."daily_budget" AS "meta_adset_daily_budget",
  ADSET."status" AS "meta_adset_status",
  AD."id" AS "meta_internal_ad_id",
  NULLIF(AD."external_ad_id",'') AS "meta_external_ad_id",
  AD."name" AS "meta_ad_name",
  AD."status" AS "meta_ad_status"
FROM
  LATEST_INTEGRATION as INTGR
  JOIN LATEST_CAMPAIGN AS CAMP ON INTGR."id" = CAMP."credentials_id"
  LEFT JOIN LATEST_ADSET ADSET ON CAMP."id" = ADSET."facebook_campaign_id"
  LEFT JOIN LATEST_AD AD ON ADSET."id" = AD."ad_set_id"