
SELECT
	"company_id",
    "journey_id",
    "journey_name",
    "media_plan_id",
    "media_plan_name",
    "channel_id",
    "channel_name",
    "platform_id",
    "platform_name",
    "tile_id",
    "tile_name",
    "media_row_id",
    "media_row_name",
    "platform_campaign_id",
    "platform_campaign_name",
    "platform_campaign_group_id",
    "platform_campaign_group_name",
    "ad_asset",
    "ad_asset_id",
    "currency",
    "data_source",
    "data_source_details",
    "date",
    "impressions",
    "clicks",
    "clicks_all",
    "spend",
    "completed_views_first",
    "completed_views_mid",
    "completed_views_third",
    "completed_views_full",
    "thruplays",
    "video_start",
    "video_views",
    "landing_page_views",
    "page_likes",
    "platform_conversions",
    "post_comments",
    "post_engagement",
    "post_reaction",
    "post_saves",
    NULL AS "conversion_metric",
    NULL AS "conversions",
    NULL AS "conversions_revenue"
FROM
  {{ ref('meta_base_ad_level_report_case_view') }}
UNION ALL
SELECT
	"company_id",
    "journey_id",
    "journey_name",
    "media_plan_id",
    "media_plan_name",
    "channel_id",
    "channel_name",
    "platform_id",
    "platform_name",
    "tile_id",
    "tile_name",
    "media_row_id",
    "media_row_name",
    "platform_campaign_id",
    "platform_campaign_name",
    "platform_campaign_group_id",
    "platform_campaign_group_name",
    "ad_asset",
    "ad_asset_id",
    "currency",
    "data_source",
    "data_source_details",
    "date",
    NULL AS "impressions",
    NULL AS "clicks",
    NULL AS "clicks_all",
    NULL AS "spend",
    NULL AS "completed_views_first",
    NULL AS "completed_views_mid",
    NULL AS "completed_views_third",
    NULL AS "completed_views_full",
    NULL AS "thruplays",
    NULL AS "video_start",
    NULL AS "video_views",
    NULL AS "landing_page_views",
    NULL AS "page_likes",
    NULL AS "platform_conversions",
    NULL AS "post_comments",
    NULL AS "post_engagement",
    NULL AS "post_reaction",
    NULL AS "post_saves",
    "conversion_metric",
    "conversions",
    "conversions_revenue"
FROM
  {{ ref('meta_conversions_ad_level_report_case_view') }}