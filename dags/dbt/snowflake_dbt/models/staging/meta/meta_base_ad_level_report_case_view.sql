
WITH LATEST_BASE_META_REPORTS AS (
    SELECT * FROM  REPORTS.SOCIAL_CAMPAIGNS_REPORTS_V3
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'social_campaigns_reports_v3'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  META_AD_LEVEL AS (
    SELECT DISTINCT
      "meta_tile_id",
      "meta_external_campaign_id",
      "meta_campaign_name",
      "meta_external_adset_id",
      "meta_adset_name",
      "meta_external_ad_id",
      "meta_ad_name",
      "meta_currency_code"
    FROM
      {{ ref('meta_taxonomy_view') }}
    WHERE
      "meta_external_ad_id" IS NOT NULL
  ),
  META_BASE_REPORTS AS (
    SELECT
      "date",
      "campaign_id",
      "ad_set_id",
      "ad_id",
      "account_currency",
      SUM("impressions") AS "impressions",
      SUM("link_clicks") AS "clicks",
      SUM("clicks") AS "clicks_all",
      SUM("spend") As "spend",
      SUM("video_p25_watched_actions") AS "completed_views_first",
      SUM("video_p50_watched_actions") AS "completed_views_mid",
      SUM("video_p75_watched_actions") AS "completed_views_third",
      SUM("completed_views_full") AS "completed_views_full",
      SUM("thruplays") AS "thruplays",
      SUM("video_plays") AS "video_start",
      SUM("three_sec_video_views") AS "video_views",
      SUM("landing_page_views") AS "landing_page_views",
      SUM("like") AS "page_likes",
      SUM("onsite_conversion") AS "platform_conversions",
      SUM("post_comments") AS "post_comments",
      SUM("post_engagement") AS "post_engagement",
      SUM("post_reaction") AS "post_reaction",
      SUM("post_saves") AS "post_saves"
    FROM
      LATEST_BASE_META_REPORTS
    GROUP BY
      "date",
      "campaign_id",
      "ad_set_id",
      "ad_id",
      "account_currency"
  )

    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      TX."tile_id",
      "tile_name",
      CAST("meta_external_campaign_id" AS TEXT) AS "platform_campaign_id",
      CAST("meta_campaign_name" AS TEXT) AS "platform_campaign_name",
      CAST("meta_external_adset_id" AS TEXT) AS "platform_campaign_group_id",
      CAST("meta_adset_name" AS TEXT) AS "platform_campaign_group_name",
      CAST("meta_external_ad_id" AS TEXT) AS "ad_asset_id",
      CAST("meta_ad_name" AS TEXT) AS "ad_asset",
      COALESCE("account_currency","meta_currency_code","mediarow_currency") AS "currency",
      'Meta' AS "data_source",
      'social_campaigns_reports_v3' AS "data_source_details",
      "date",
      "impressions",
      "clicks",
      "clicks_all",
      "spend",
      "completed_views_first",
      "completed_views_mid",
      "completed_views_third",
      "completed_views_full",
      "thruplays",
      "video_start",
      "video_views",
      "landing_page_views",
      "page_likes",
      "platform_conversions",
      "post_comments",
      "post_engagement",
      "post_reaction",
      "post_saves"
    FROM
      TAXONOMY AS TX
      JOIN META_AD_LEVEL AS MT_AD ON TX."tile_id" = MT_AD."meta_tile_id"
      JOIN META_BASE_REPORTS as MT_REP ON CAST(MT_AD."meta_external_campaign_id" AS TEXT) = CAST(MT_REP."campaign_id" AS TEXT)
      AND CAST(MT_AD."meta_external_adset_id" AS TEXT) = CAST(MT_REP."ad_set_id" AS TEXT)
      AND CAST(MT_AD."meta_external_ad_id" AS TEXT) = CAST(MT_REP."ad_id" AS TEXT)
    WHERE
      MT_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"