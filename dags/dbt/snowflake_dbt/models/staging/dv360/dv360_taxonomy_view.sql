
WITH
  LATEST_DV360_CAMPAIGN AS (
    SELECT
      *
    FROM
      DV360.DV360_DV360DISPLAYCAMPAIGN
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DV360'
              AND "TABLE_NAME" = 'dv360_dv360displaycampaign'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_DV360_INTEGRATION AS (
    SELECT
      *
    FROM
      DV360.DV360_DV360INTEGRATION
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DV360'
              AND "TABLE_NAME" = 'dv360_dv360integration'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_DV360_DV360INSERTIONORDER AS (
    SELECT
      *
    FROM
      DV360.DV360_DV360INSERTIONORDER
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DV360'
              AND "TABLE_NAME" = 'dv360_dv360insertionorder'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_DV360_DV360LINEITEM AS (
    SELECT
      *
    FROM
      DV360.DV360_DV360LINEITEM
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DV360'
              AND "TABLE_NAME" = 'dv360_dv360lineitem'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_DV360_DV360INSERTIONORDER_BUDGET_SEGMENTS AS (
    SELECT
      *
    FROM
      DV360.DV360_DV360INSERTIONORDER_BUDGET_SEGMENTS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DV360'
              AND "TABLE_NAME" = 'dv360_dv360insertionorder_budget_segments'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_DV360_BUDGETSEGMENT AS (
    SELECT
      *
    FROM
      DV360.DV360_BUDGETSEGMENT
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DV360'
              AND "TABLE_NAME" = 'dv360_budgetsegment'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_DV360_ADGROUP AS (
    SELECT
      *
    FROM
      DV360.DV360_DV360ADGROUP
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DV360'
              AND "TABLE_NAME" = 'dv360_dv360adgroup'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_DV360_AD AS (
    SELECT
      *
    FROM
      DV360.DV360_DV360ADGROUPAD
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DV360'
              AND "TABLE_NAME" = 'dv360_dv360adgroupad'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  )
SELECT
  DVC."display_campaign" AS "dv360_tile_id",
  DVI."id" AS "dv360_integration_id",
  DVI."currency_code" AS "dv360_currency_code",
  DVC."id" AS "dv360_internal_campaign_id",
  DVC."external_campaign_id" AS "dv360_external_campaign_id",
  DVC."name" AS "dv360_campaign_name",
  DVC."budget" AS "dv360_campaign_budget",
  DVC."date_from" AS "dv360_campaign_date_from",
  DVC."date_to" AS "dv360_campaign_date_to",
  DVC."status" AS "dv360_campaign_status",
  DVIO."id" AS "dv360_internal_insertion_order_id",
  DVIO."external_io_id" AS "dv360_external_insertion_order_id",
  DVIO."name" AS "dv360_insertion_order_name",
  DVIO."status" AS "dv360_insertion_order_status",
  DVBS."budget" AS "dv360_segment_budget",
  DVBS."start_date" AS "dv360_segment_start_date",
  DVBS."end_date" AS "dv360_segment_end_date",
  DVLI."id" AS "dv360_internal_li_id",
  DVLI."external_li_id" AS "dv360_external_li_id",
  DVLI."name" AS "dv360_li_name",
  DVLI."status" AS "dv360_li_status",
  DVLI."date_from" AS "dv360_li_start_date",
  DVLI."date_to" AS "dv360_li_end_date",
  DVLI."budget" AS "dv360_li_lifetime_budget",
  DVLI."pacing" AS "dv360_li_pacing",
  DVLI."pacing_rate" AS "dv360_li_pacing_rate",
  DVLI."pacing_amount" AS "dv360_li_pacing_amount",
  DVGRP."id" AS "dv360_internal_adgroup_id",
  DVGRP."external_ad_group_id" AS "dv360_external_adgroup_id",
  DVGRP."name" AS "dv360_adgroup_name",
  DVGRP."status" AS "dv360_adgroup_status",
  DVAD."id" AS "dv360_internal_ad_id",
  DVAD."external_ad_group_ad_id" AS "dv360_external_ad_id",
  DVAD."name" AS "dv360_ad_name",
  DVAD."status" AS "dv360_ad_status"
FROM
  LATEST_DV360_INTEGRATION dvi
  JOIN LATEST_DV360_CAMPAIGN dvc ON DVI."id" = DVC."integration_id"
  LEFT JOIN LATEST_DV360_DV360INSERTIONORDER dvio ON DVC."id" = DVIO."dv360_display_campaign_id"
  LEFT JOIN LATEST_DV360_DV360LINEITEM dvli ON DVIO."id" = DVLI."insertion_order_id"
  LEFT JOIN LATEST_DV360_ADGROUP dvgrp ON DVGRP."line_item_id" = DVLI."id"
  LEFT JOIN LATEST_DV360_AD dvad ON DVAD."ad_group_id" = DVGRP."id"
  LEFT JOIN LATEST_DV360_DV360INSERTIONORDER_BUDGET_SEGMENTS dvio_bs ON DVIO."id" = DVIO_BS."dv360insertionorder_id"
  LEFT JOIN LATEST_DV360_BUDGETSEGMENT dvbs ON DVIO_BS."budgetsegment_id" = DVBS."id"