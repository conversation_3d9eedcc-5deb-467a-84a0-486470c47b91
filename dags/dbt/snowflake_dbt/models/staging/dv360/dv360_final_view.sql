
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset",
  "ad_asset_id",
  "platform_creative_id",
  "platform_creative_name",
  "currency",
  "data_source",
  "data_source_details",
  "date",
  "impressions",
  "clicks",
  "spend",
  "completed_views_first",
  "completed_views_full",
  "completed_views_mid",
  "completed_views_third",
  "trueviews",
  "video_start",
  "video_views",
  "platform_active_view_viewable_impressions",
  "platform_conversions"
FROM
  {{ ref('dv360_base_creative_level_report_case_view') }}
UNION ALL
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset",
  "ad_asset_id",
  "platform_creative_id",
  "platform_creative_name",
  "currency",
  "data_source",
  "data_source_details",
  "date",
  "impressions",
  "clicks",
  "spend",
  "completed_views_first",
  "completed_views_full",
  "completed_views_mid",
  "completed_views_third",
  "trueviews",
  NULL AS "video_start",
  NULL AS "video_views",
  "platform_active_view_viewable_impressions",
  NULL AS "platform_conversions"
FROM
  {{ ref('dv360_youtube_ad_level_report_case_view') }}