
WITH
  LATEST_BASE_DV360_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.DV360_BASE_REPORTS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'dv360_base_reports'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  DV360_LI_LEVEL AS (
    SELECT DISTINCT
      "dv360_tile_id",
      "dv360_external_campaign_id",
      "dv360_campaign_name",
      "dv360_external_insertion_order_id",
      "dv360_insertion_order_name",
      "dv360_external_li_id",
      "dv360_li_name",
      "dv360_currency_code"
    FROM
      {{ ref('dv360_taxonomy_view') }}
    WHERE
      "dv360_external_li_id" IS NOT NULL
  ),
  CREATIVE_NAME_META_TABLE AS (
    SELECT DISTINCT
      "creative_id",
      "creative_name",
      ROW_NUMBER() OVER (
        PARTITION BY
          "creative_id"
        ORDER BY
          "creative_id",
          "creative_name"
      ) AS row_num
    FROM
      LATEST_BASE_DV360_REPORTS QUALIFY row_num = 1
  ),
  DV360_BASE_REPORTS AS (
    SELECT
      LR."date",
      LR."campaign_id",
      LR."io_id",
      LR."line_item_id",
      LR."creative_id",
      CM."creative_name",
      LR."advertiser_currency",
      SUM("impressions") AS "impressions",
      SUM("clicks") AS "clicks",
      SUM("spent") AS "spend",
      SUM("quartile_1") As "completed_views_first",
      SUM("quartile_2") AS "completed_views_mid",
      SUM("quartile_3") AS "completed_views_third",
      SUM("quartile_4") AS "completed_views_full",
      SUM("true_view_views") AS "trueviews",
      SUM("starts_video") AS "video_start",
      SUM("starts_video") AS "video_views",
      SUM("viewable_impressions") AS "platform_active_view_viewable_impressions",
      SUM("conversions") AS "platform_conversions"
    FROM
      LATEST_BASE_DV360_REPORTS AS LR
      LEFT JOIN CREATIVE_NAME_META_TABLE AS CM ON LR."creative_id" = CM."creative_id"
      -- This filtering here ensures we're excluding youtube line items performance data to not take its duplicate alongside youtube reports
    WHERE
      LR."creative_id" != 'Unknown'
    GROUP BY
      LR."date",
      LR."campaign_id",
      LR."io_id",
      LR."line_item_id",
      LR."creative_id",
      CM."creative_name",
      LR."advertiser_currency"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  TX."tile_id",
  "tile_name",
  "dv360_external_campaign_id" AS "platform_campaign_id",
  "dv360_campaign_name" AS "platform_campaign_name",
  "dv360_external_insertion_order_id" AS "platform_campaign_group_id",
  "dv360_insertion_order_name" AS "platform_campaign_group_name",
  "dv360_external_li_id" AS "ad_asset_id",
  "dv360_li_name" AS "ad_asset",
  "creative_id" AS "platform_creative_id",
  "creative_name" AS "platform_creative_name",
  COALESCE(
    "advertiser_currency",
    "dv360_currency_code",
    "mediarow_currency"
  ) AS "currency",
  "date",
  "impressions",
  "clicks",
  "spend",
  "completed_views_first",
  "completed_views_mid",
  "completed_views_third",
  "completed_views_full",
  "trueviews",
  "video_start",
  "video_views",
  "platform_active_view_viewable_impressions",
  "platform_conversions",
  'DV360' AS "data_source",
  'dv360_base_reports' AS "data_source_details"
FROM
  TAXONOMY AS TX
  JOIN DV360_LI_LEVEL AS DV_LI ON TX."tile_id" = DV_LI."dv360_tile_id"
  JOIN DV360_BASE_REPORTS as DV_REP ON DV_LI."dv360_external_campaign_id" = DV_REP."campaign_id"
  AND DV_LI."dv360_external_insertion_order_id" = DV_REP."io_id"
  AND DV_LI."dv360_external_li_id" = DV_REP."line_item_id"
WHERE
  DV_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"