
SELECT
	"company_id",
    "journey_id",
    "journey_name",
    "media_plan_id",
    "media_plan_name",
    "channel_id",
    "channel_name",
    "platform_id",
    "platform_name",
    "tile_id",
    "tile_name",
    "media_row_id",
    "media_row_name",
    "platform_campaign_id",
    "platform_campaign_name",
    "platform_campaign_group_id",
    "platform_campaign_group_name",
    "ad_asset",
    "ad_asset_id",
    "currency",
    "data_source",
    "data_source_details",
    "date",
    "impressions",
    "clicks",
    "spend",
    "platform_conversions"
FROM
  {{ ref('microsoft_ads_base_ad_level_report_case_view') }}
UNION ALL
SELECT
	"company_id",
    "journey_id",
    "journey_name",
    "media_plan_id",
    "media_plan_name",
    "channel_id",
    "channel_name",
    "platform_id",
    "platform_name",
    "tile_id",
    "tile_name",
    "media_row_id",
    "media_row_name",
    "platform_campaign_id",
    "platform_campaign_name",
    NULL AS "platform_campaign_group_id",
    NULL AS "platform_campaign_group_name",
    NULL AS "ad_asset",
    NULL AS "ad_asset_id",
    "currency",
    "data_source",
    "data_source_details",
    "date",
    "impressions",
    "clicks",
    "spend",
    "platform_conversions"
FROM
  {{ ref('microsoft_ads_base_campaign_level_report_case_view') }}