
WITH
  LATEST_INTEGRATION AS (
    SELECT
      *
    FROM
      BING.INTEGRATIONS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'BING'
              AND "TABLE_NAME" = 'integrations'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_PLATFORM_SETTINGS AS (
    SELECT
      *
    FROM
      BING.PLATFORM_SETTINGS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'BING'
              AND "TABLE_NAME" = 'platform_settings'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CAMPAIGN AS (
    SELECT
      *
    FROM
      BING.CAMPAIGNS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'BING'
              AND "TABLE_NAME" = 'campaigns'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_ADGROUP AS (
    SELECT
      *
    FROM
      BING.AD_GROUPS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'BING'
              AND "TABLE_NAME" = 'ad_groups'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_AD AS (
    SELECT
      *
    FROM
      BING.ADS
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'BING'
              AND "TABLE_NAME" = 'ads'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  )
SELECT
  CAMP."platform_settings_id" AS "microsoft_ads_tile_id",
  INTGR."id" AS "microsoft_ads_integration_id",
  INTGR."currency_code" AS "microsoft_ads_currency_code",
  CAMP."id" AS "microsoft_ads_internal_campaign_id",
  CAMP."external_id" AS "microsoft_ads_external_campaign_id",
  CAMP."name" AS "microsoft_ads_campaign_name",
  CAMP."budget" AS "microsoft_ads_campaign_daily_budget",
  CAMP."status" AS "microsoft_ads_campaign_status",
  CAMP."type" AS "microsoft_ads_campaign_type",
  ADGRP."id" AS "microsoft_ads_internal_adgrp_id",
  ADGRP."external_id" AS "microsoft_ads_external_adgrp_id",
  ADGRP."name" AS "microsoft_ads_adgrp_name",
  ADGRP."start_date" AS "microsoft_ads_adgrp_date_from",
  ADGRP."end_date" AS "microsoft_ads_adgrp_date_to",
  ADGRP."status" AS "microsoft_ads_adgrp_status",
  ADGRP."type" AS "microsoft_ads_adgrp_type",
  AD."id" AS "microsoft_ads_internal_ad_id",
  AD."external_id" AS "microsoft_ads_external_ad_id",
  AD."name" AS "microsoft_ads_ad_name",
  AD."status" AS "microsoft_ads_ad_status",
  AD."type" AS "microsoft_ads_ad_type"
FROM
  LATEST_INTEGRATION AS INTGR
  JOIN LATEST_PLATFORM_SETTINGS AS PS ON INTGR."id" = PS."integration_id"
  JOIN LATEST_CAMPAIGN AS CAMP ON ps."tile_id" = CAMP."platform_settings_id"
  LEFT JOIN LATEST_ADGROUP ADGRP ON CAMP."id" = ADGRP."campaign_id"
  LEFT JOIN LATEST_AD AD ON ADGRP."id" = AD."ad_group_id"