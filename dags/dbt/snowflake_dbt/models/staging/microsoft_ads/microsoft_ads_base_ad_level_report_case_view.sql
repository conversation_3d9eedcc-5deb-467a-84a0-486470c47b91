
WITH
  LATEST_BASE_MICROSOFT_ADS_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.MICROSOFT_ADVERTISING_PERFORMANCE_AD_LEVEL
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'microsoft_advertising_performance_ad_level'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  MICROSOFT_ADS_AD_LEVEL AS (
    SELECT DISTINCT
      "microsoft_ads_tile_id",
      "microsoft_ads_external_campaign_id",
      "microsoft_ads_campaign_name",
      "microsoft_ads_external_adgrp_id",
      "microsoft_ads_adgrp_name",
      "microsoft_ads_external_ad_id",
      "microsoft_ads_ad_name",
      "microsoft_ads_currency_code"
    FROM
      {{ ref('microsoft_ads_taxonomy_view') }}
    WHERE
      "microsoft_ads_external_ad_id" IS NOT NULL
  ),
  MICROSOFT_ADS_BASE_REPORTS AS (
    SELECT
      "date",
      "campaign_id",
      "ad_group_id",
      "ad_id",
      "currency_code",
      SUM("impressions") AS "impressions",
      SUM("clicks") AS "clicks",
      SUM("spend") As "spend",
      SUM("conversions_qualified") AS "platform_conversions"
    FROM
      LATEST_BASE_MICROSOFT_ADS_REPORTS
    GROUP BY
      "date",
      "campaign_id",
      "ad_group_id",
      "ad_id",
      "currency_code"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  TX."tile_id",
  "tile_name",
  "microsoft_ads_external_campaign_id" AS "platform_campaign_id",
  "microsoft_ads_campaign_name" AS "platform_campaign_name",
  "microsoft_ads_external_adgrp_id" AS "platform_campaign_group_id",
  "microsoft_ads_adgrp_name" AS "platform_campaign_group_name",
  "microsoft_ads_external_ad_id" AS "ad_asset_id",
  "microsoft_ads_ad_name" AS "ad_asset",
  COALESCE(
    "currency_code",
    "microsoft_ads_currency_code",
    "mediarow_currency"
  ) AS "currency",
  'Microsoft Ads' AS "data_source",
  'microsoft_advertising_performance_ad_level' AS "data_source_details",
  "date",
  "impressions",
  "clicks",
  "spend",
  "platform_conversions"
FROM
  TAXONOMY AS TX
  JOIN MICROSOFT_ADS_AD_LEVEL AS MADS_AD ON TX."tile_id" = MADS_AD."microsoft_ads_tile_id"
  JOIN MICROSOFT_ADS_BASE_REPORTS as MADS_REP ON MADS_AD."microsoft_ads_external_campaign_id" = MADS_REP."campaign_id"
  AND MADS_AD."microsoft_ads_external_adgrp_id" = MADS_REP."ad_group_id"
  AND MADS_AD."microsoft_ads_external_ad_id" = MADS_REP."ad_id"
WHERE
  MADS_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"
