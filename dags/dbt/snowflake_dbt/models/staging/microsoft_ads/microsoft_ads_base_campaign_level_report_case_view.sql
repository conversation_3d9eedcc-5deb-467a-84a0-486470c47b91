
WITH
  LATEST_CAMPAIGN_MICROSOFT_ADS_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.MICROSOFT_ADVERTISING_ESTIMATED_CAMPAIGN_LEVEL_V2
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'microsoft_advertising_estimated_campaign_level_v2'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  TAXONOMY AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "mediarow_currency",
      "tile_id",
      "tile_name"
    FROM
      {{ ref('mint_taxonomy_view') }}
    WHERE
      "is_deleted" = FALSE
      AND "journey_is_for_test" = FALSE
  ),
  BING_CAMP_LEVEL AS (
    SELECT DISTINCT
      "microsoft_ads_tile_id",
      "microsoft_ads_external_campaign_id",
      "microsoft_ads_campaign_name",
      "microsoft_ads_currency_code"
    FROM
      {{ ref('microsoft_ads_taxonomy_view') }}
    WHERE
      "microsoft_ads_external_ad_id" IS NULL
      AND "microsoft_ads_external_adgrp_id" IS NULL
  ),
  BING_BASE_REPORTS AS (
    SELECT
      "date",
      "campaign_id",
      SUM("impressions") AS "impressions",
      SUM("clicks") AS "clicks",
      SUM("spend") AS "spend",
      SUM("conversions_qualified") AS "platform_conversions"
    FROM
      LATEST_CAMPAIGN_MICROSOFT_ADS_REPORTS
    GROUP BY
      "date",
      "campaign_id"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "media_row_id",
  "media_row_name",
  TX."tile_id",
  "tile_name",
  "microsoft_ads_external_campaign_id" AS "platform_campaign_id",
  "microsoft_ads_campaign_name" AS "platform_campaign_name",
  COALESCE(
    "microsoft_ads_currency_code",
    "mediarow_currency"
  ) AS "currency",
  "date",
  "impressions",
  "clicks",
  "spend",
  "platform_conversions",
  'Microsoft Ads' AS "data_source",
  'microsoft_advertising_estimated_campaign_level_v2' AS "data_source_details"
FROM
  TAXONOMY AS TX
  JOIN BING_CAMP_LEVEL AS BING_C ON TX."tile_id" = BING_C."microsoft_ads_tile_id"
  JOIN BING_BASE_REPORTS AS BING_REP ON BING_C."microsoft_ads_external_campaign_id" = BING_REP."campaign_id"
WHERE
  BING_REP."date" BETWEEN TX."media_row_date_from" AND TX."media_row_date_to"