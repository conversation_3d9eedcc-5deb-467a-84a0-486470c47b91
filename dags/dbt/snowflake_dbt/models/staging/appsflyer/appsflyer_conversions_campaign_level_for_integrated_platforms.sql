
WITH
  LATEST_APPSFLYER_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_APPSFLYER_PERFORMANCE_V2
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_appsflyer_performance_v2'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  INTEGRATED_TAXONOMY_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "tile_id",
      "tile_name",
      "platform_campaign_internal_id",
      "platform_campaign_external_id",
      "platform_campaign_name",
      "platform_campaign_internal_group_id",
      "platform_campaign_external_group_id",
      "platform_campaign_group_name",
      "ad_asset_internal_id",
      "ad_asset_external_id",
      "ad_asset",
      "currency",
      "granularity_level"
    FROM
      {{ ref('mint_unified_all_platforms_taxonomy_view') }}
    WHERE
      "granularity_level" IN ('campaign')
  ),
  APPSFLYER_MAPPINGS AS (
    SELECT DISTINCT
      "appsflyer_journey_id",
      "appsflyer_device_platform",
      "appsflyer_app_id",
      "appsflyer_media_source_name",
      "appsflyer_media_source_channel_name",
      "appsflyer_media_source_platform_name",
      "appsflyer_campaign_name",
      "appsflyer_platform_item_id",
      "appsflyer_platform_item_external_id",
      "appsflyer_activity_name",
      "appsflyer_activity_conversion_metric_label",
      "appsflyer_activity_conversion_cost_based_metric_label"
    FROM
      {{ ref('appsflyer_mappings_view') }}
    WHERE
      "appsflyer_is_mappings_complete" = TRUE
  ),
  BASE_APPSFLYER_REPORTS AS (
    SELECT
      "date",
      "media_source",
      "campaign",
      "platform",
      "app_id",
      "activity_name",
      SUM("unique_users_count") AS "conversions",
      SUM("unique_users_count") AS "unique_users_count",
      SUM("event_count") AS "event_count",
      SUM("event_revenue") AS "event_revenue"
    FROM
      LATEST_APPSFLYER_REPORTS
    WHERE
      "activity_name" != '0'
    GROUP BY
      "date",
      "media_source",
      "campaign",
      "platform",
      "app_id",
      "activity_name"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  "platform_campaign_external_id" AS "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_external_group_id" AS "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset_external_id" AS "ad_asset_id",
  "ad_asset",
  "appsflyer_device_platform" AS "operating_system",
  "appsflyer_media_source_name" AS "adserver_level_1_name",
  "appsflyer_campaign_name" AS "adserver_level_2_name",
  "appsflyer_activity_name" AS "adserver_level_3_name",
  "currency",
  "appsflyer_activity_conversion_metric_label" AS "conversion_metric",
  "appsflyer_activity_conversion_cost_based_metric_label" AS "cost_based_metric",
  'Appsflyer' AS "data_source",
  'reports_appsflyer_performance_v2' AS "data_source_details",
  "date",
  "conversions",
  "unique_users_count",
  "event_count",
  "event_revenue"
FROM
  INTEGRATED_TAXONOMY_CAMPAIGN_LEVEL AS T
  JOIN APPSFLYER_MAPPINGS AS M ON T."journey_id" = M."appsflyer_journey_id"
  AND T."channel_name" = M."appsflyer_media_source_channel_name"
  AND T."platform_name" = M."appsflyer_media_source_platform_name"
  AND T."platform_campaign_internal_id" = M."appsflyer_platform_item_id"
  AND T."platform_campaign_external_id" = M."appsflyer_platform_item_external_id"
  JOIN BASE_APPSFLYER_REPORTS AS R ON M."appsflyer_media_source_name" = R."media_source"
  AND M."appsflyer_campaign_name" = R."campaign"
  AND M."appsflyer_device_platform" = R."platform"
  AND M."appsflyer_app_id" = R."app_id"
  AND M."appsflyer_activity_name" = R."activity_name"
WHERE
  R."date" BETWEEN T."media_row_date_from" AND T."media_row_date_to"