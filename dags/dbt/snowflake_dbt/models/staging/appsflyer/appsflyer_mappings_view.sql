
WITH
  LATEST_APPSFLYER_INTEGRATIONS AS (
    SELECT
      *
    FROM
      APPSFLYER.INTEGRATIONS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'APPSFLYER'
              AND "TABLE_NAME" = 'integrations'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_APPSFLYER_SETTINGS AS (
    SELECT
      *
    FROM
      APPSFLYER.SETTINGS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMES<PERSON>MP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'APPSFLYER'
              AND "TABLE_NAME" = 'settings'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_APPSFLYER_CAMPAIGN_MAPPINGS AS (
    SELECT
      *
    FROM
      APPSFLYER.CAMPAIGNS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'APPSFLYER'
              AND "TABLE_NAME" = 'campaigns'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_APPSFLYER_MEDIA_SOURCE_MAPPINGS AS (
    SELECT
      *
    FROM
      APPSFLYER.MEDIA_SOURCES
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'APPSFLYER'
              AND "TABLE_NAME" = 'media_sources'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_APPSFLYER_ACTIVITY_MAPPINGS AS (
    SELECT
      *
    FROM
      APPSFLYER.CONVERSION_ACTIVITIES
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'APPSFLYER'
              AND "TABLE_NAME" = 'conversion_activities'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  UNPIVOT_INTEGRATIONS AS (
    SELECT
      "id",
      "name",
      "ios_id" AS "app_id",
      'ios' AS "platform",
      "company_id",
      "is_active"
    FROM
      LATEST_APPSFLYER_INTEGRATIONS
    WHERE
      "ios_id" IS NOT NULL
    UNION ALL
    SELECT
      "id",
      "name",
      "android_id" AS "app_id",
      'android' AS "platform",
      "company_id",
      "is_active"
    FROM
      LATEST_APPSFLYER_INTEGRATIONS
    WHERE
      "android_id" IS NOT NULL
  )
SELECT DISTINCT
  I."company_id" AS "company_id",
  I."id" AS "appsflyer_integration_id",
  I."name" AS "appsflyer_integration_name",
  I."app_id" AS "appsflyer_app_id",
  I."platform" AS "appsflyer_device_platform",
  I."is_active" AS "appsflyer_integration_is_active",
  S."id" AS "appsflyersettings_id",
  S."campaign_collection_id" AS "appsflyer_journey_id",
  MS."id" AS "appsflyer_media_source_internal_id",
  MS."name" AS "appsflyer_media_source_name",
  MS."device_platform" AS "appsflyer_media_source_device_platform",
  MS."platform" AS "appsflyer_media_source_platform_name",
  MS."channel" AS "appsflyer_media_source_channel_name",
  CM."id" AS "appsflyer_campaign_internal_id",
  CM."name" AS "appsflyer_campaign_name",
  CM."platform_item_id" AS "appsflyer_platform_item_id",
  CM."platform_item_external_id" AS "appsflyer_platform_item_external_id",
  AM."id" AS "appsflyer_activity_internal_id",
  AM."name" AS "appsflyer_activity_name",
  AM."conversion_metric" AS "appsflyer_activity_conversion_metric_label",
  AM."cost_based_metric" AS "appsflyer_activity_conversion_cost_based_metric_label",
  CASE
    WHEN CM."name" IS NOT NULL THEN true
    ELSE false
  END AS "appsflyer_is_mappings_complete"
FROM
  UNPIVOT_INTEGRATIONS AS I
  JOIN LATEST_APPSFLYER_SETTINGS AS S ON I."id" = S."integration_id"
  LEFT JOIN LATEST_APPSFLYER_MEDIA_SOURCE_MAPPINGS AS MS ON S."id" = MS."setting_id"
  AND MS."device_platform" = I."platform"
  LEFT JOIN LATEST_APPSFLYER_CAMPAIGN_MAPPINGS AS CM ON MS."id" = CM."media_source_id"
  LEFT JOIN LATEST_APPSFLYER_ACTIVITY_MAPPINGS AS AM ON S."id" = AM."setting_id"
  AND MS."device_platform" = AM."device_platform"