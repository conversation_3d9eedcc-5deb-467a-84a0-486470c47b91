{{ union_models([
    'appsflyer_base_campaign_level_for_virtual_platform_case',
    'appsflyer_conversions_campaign_level_for_virtual_platforms',
    'appsflyer_conversions_campaign_level_for_integrated_platforms',
    'appsflyer_base_campaign_level_for_integrated_platform_case'
],
[
    'company_id',
    'journey_id',
    'journey_name',
    'media_plan_id',
    'media_plan_name',
    'channel_id',
    'channel_name',
    'platform_id',
    'platform_name',
    'tile_id',
    'tile_name',
    'media_row_id',
    'media_row_name',
    'platform_campaign_id',
    'platform_campaign_name',
    'adserver_level_1_name',
    'adserver_level_2_name',
    'adserver_level_3_name',
    'operating_system',
    'currency',
    'conversion_metric',
    'cost_based_metric',
    'data_source',
    'data_source_details',
    'date',
    'impressions',
    'clicks',
    'spend',
    'installations',
    'sessions',
    'conversions',
    'unique_users_count',
    'event_count',
    'event_revenue'
]) }}
