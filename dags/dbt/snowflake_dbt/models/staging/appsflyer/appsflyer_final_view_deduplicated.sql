
{% set appsflyer_columns = [
    '"company_id"',
    '"journey_id"',
    '"journey_name"',
    '"media_plan_id"',
    '"media_plan_name"',
    '"channel_id"',
    '"channel_name"',
    '"platform_id"',
    '"platform_name"',
    '"tile_id"',
    '"tile_name"',
    '"media_row_id"',
    '"media_row_name"',
    '"platform_campaign_id"',
    '"platform_campaign_name"',
    '"adserver_level_1_name"',
    '"adserver_level_2_name"',
    '"adserver_level_3_name"',
    '"operating_system"',
    '"currency"',
    '"conversion_metric"',
    '"cost_based_metric"',
    '"data_source"',
    '"data_source_details"',
    '"date"',
    '"impressions"',
    '"clicks"',
    '"spend"',
    '"installations"',
    '"sessions"',
    '"conversions"',
    '"unique_users_count"',
    '"event_count"',
    '"event_revenue"'
] %}

WITH
  CAMPAIGN_LEVEL_APPSFLYER AS (
    SELECT
      {{ appsflyer_columns | join(',\n      ') }}
    FROM
      {{ ref('appsflyer_final_view') }}
    WHERE
      "platform_campaign_id" IS NOT NULL
  ),
  CAMPAIGN_LEVEL_DEDUPLICATION AS (
    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      CASE
        WHEN COUNT(DISTINCT "platform_campaign_id") = 1 THEN MAX("platform_campaign_id")
        ELSE NULL
      END AS "platform_campaign_id",
      CASE
        WHEN COUNT(DISTINCT "platform_campaign_name") = 1 THEN MAX("platform_campaign_name")
        ELSE NULL
      END AS "platform_campaign_name",
      "adserver_level_1_name",
      "adserver_level_2_name",
      "adserver_level_3_name",
      "operating_system",
      "currency",
      "conversion_metric",
      "cost_based_metric",
      "data_source",
      "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "installations",
      "sessions",
      "conversions",
      "unique_users_count",
      "event_count",
      "event_revenue"
    FROM
      CAMPAIGN_LEVEL_APPSFLYER
    GROUP BY
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "adserver_level_1_name",
      "adserver_level_2_name",
      "adserver_level_3_name",
      "operating_system",
      "currency",
      "conversion_metric",
      "cost_based_metric",
      "data_source",
      "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "installations",
      "sessions",
      "conversions",
      "unique_users_count",
      "event_count",
      "event_revenue"
  )
SELECT
  {{ appsflyer_columns | join(',\n      ') }}
FROM
  CAMPAIGN_LEVEL_DEDUPLICATION