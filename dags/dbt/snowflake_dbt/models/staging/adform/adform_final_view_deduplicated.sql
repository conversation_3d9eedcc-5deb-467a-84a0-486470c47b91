
{% set adform_columns = [
	  '"company_id"',
      '"journey_id"',
      '"journey_name"',
      '"media_plan_id"',
      '"media_plan_name"',
      '"channel_id"',
      '"channel_name"',
      '"platform_id"',
      '"platform_name"',
      '"tile_id"',
      '"tile_name"',
      '"media_row_id"',
      '"media_row_name"',
      '"platform_campaign_id"',
      '"platform_campaign_name"',
      '"platform_campaign_group_id"',
      '"platform_campaign_group_name"',
      '"ad_asset_id"',
      '"ad_asset"',
      '"adserver_advertiser_id"',
      '"adserver_level_1_id"',
      '"adserver_level_1_name"',
      '"adserver_level_2_id"',
      '"adserver_level_2_name"',
      '"adserver_level_3_id"',
      '"adserver_level_3_name"',
      '"adserver_level_4_id"',
      '"adserver_level_4_name"',
      '"adserver_level_5_id"',
      '"adserver_level_6_id"',
      '"adserver_level_6_name"',
      '"currency"',
      '"conversion_metric"',
      '"cost_based_metric"',
      '"data_source"',
      '"data_source_details"',
      '"date"',
      '"impressions"',
      '"clicks"',
      '"spend"',
      '"completed_views_first"',
      '"completed_views_mid"',
      '"completed_views_third"',
      '"completed_views_full"',
      '"video_start"',
      '"video_views"',
      '"view_through_conversions"',
      '"click_through_conversions"',
      '"conversions"'
] %}

WITH
  AD_LEVEL_ADFORM AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      {{ ref('adform_final_view') }}
    WHERE
      "ad_asset_id" IS NOT NULL
  ),
  ADGROUP_LEVEL_ADFORM AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      {{ ref('adform_final_view') }}
    WHERE
      "platform_campaign_group_id" IS NOT NULL
      AND "ad_asset_id" IS NULL
  ),
  CAMPAIGN_LEVEL_ADFORM AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      {{ ref('adform_final_view') }}
    WHERE
      "platform_campaign_id" IS NOT NULL
      AND "platform_campaign_group_id" IS NULL
  ),
  AD_LEVEL_DEDUPLICATION AS (
    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "platform_campaign_id",
      "platform_campaign_name",
      "platform_campaign_group_id",
      "platform_campaign_group_name",
      CASE
        WHEN COUNT(DISTINCT "ad_asset_id") = 1 THEN MAX("ad_asset_id")
        ELSE NULL
      END AS "ad_asset_id",
      CASE
        WHEN COUNT(DISTINCT "ad_asset") = 1 THEN MAX("ad_asset")
        ELSE NULL
      END AS "ad_asset",
      "adserver_advertiser_id",
      "adserver_level_1_id",
      "adserver_level_1_name",
      "adserver_level_2_id",
      "adserver_level_2_name",
      "adserver_level_3_id",
      "adserver_level_3_name",
      "adserver_level_4_id",
      "adserver_level_4_name",
      "adserver_level_5_id",
      "adserver_level_6_id",
      "adserver_level_6_name",
      "currency",
      "conversion_metric",
      "cost_based_metric",
      "data_source",
      "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "completed_views_first",
      "completed_views_mid",
      "completed_views_third",
      "completed_views_full",
      "video_start",
      "video_views",
      "view_through_conversions",
      "click_through_conversions",
      "conversions"
    FROM
      AD_LEVEL_ADFORM
    GROUP BY
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "platform_campaign_id",
      "platform_campaign_name",
      "platform_campaign_group_id",
      "platform_campaign_group_name",
      "adserver_advertiser_id",
      "adserver_level_1_id",
      "adserver_level_1_name",
      "adserver_level_2_id",
      "adserver_level_2_name",
      "adserver_level_3_id",
      "adserver_level_3_name",
      "adserver_level_4_id",
      "adserver_level_4_name",
      "adserver_level_5_id",
      "adserver_level_6_id",
      "adserver_level_6_name",
      "currency",
      "conversion_metric",
      "cost_based_metric",
      "data_source",
      "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "completed_views_first",
      "completed_views_mid",
      "completed_views_third",
      "completed_views_full",
      "video_start",
      "video_views",
      "view_through_conversions",
      "click_through_conversions",
      "conversions"
  ),
  DEDUPLICATED_AD_LEVEL_ONLY AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      AD_LEVEL_DEDUPLICATION
    WHERE
      "ad_asset_id" IS NOT NULL
  ),
  ADGROUP_LEFTOVERS_FROM_AD_LEVEL_DEDUPLICATION AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      AD_LEVEL_DEDUPLICATION
    WHERE
      "ad_asset_id" IS NULL
  ),
  ADGROUP_LEVEL_WITH_LEFTOVERS AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      ADGROUP_LEVEL_ADFORM
    UNION ALL
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      ADGROUP_LEFTOVERS_FROM_AD_LEVEL_DEDUPLICATION
  ),
  ADGROUP_LEVEL_DEDUPLICATION AS (
    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "platform_campaign_id",
      "platform_campaign_name",
      CASE
        WHEN COUNT(DISTINCT "platform_campaign_group_id") = 1 THEN MAX("platform_campaign_group_id")
        ELSE NULL
      END AS "platform_campaign_group_id",
      CASE
        WHEN COUNT(DISTINCT "platform_campaign_group_name") = 1 THEN MAX("platform_campaign_group_name")
        ELSE NULL
      END AS "platform_campaign_group_name",
      "ad_asset_id",
      "ad_asset",
      "adserver_advertiser_id",
      "adserver_level_1_id",
      "adserver_level_1_name",
      "adserver_level_2_id",
      "adserver_level_2_name",
      "adserver_level_3_id",
      "adserver_level_3_name",
      "adserver_level_4_id",
      "adserver_level_4_name",
      "adserver_level_5_id",
      "adserver_level_6_id",
      "adserver_level_6_name",
      "currency",
      "conversion_metric",
      "cost_based_metric",
      "data_source",
      "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "completed_views_first",
      "completed_views_mid",
      "completed_views_third",
      "completed_views_full",
      "video_start",
      "video_views",
      "view_through_conversions",
      "click_through_conversions",
      "conversions"
    FROM
      ADGROUP_LEVEL_WITH_LEFTOVERS
    GROUP BY
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "platform_campaign_id",
      "platform_campaign_name",
      "ad_asset_id",
      "ad_asset",
      "adserver_advertiser_id",
      "adserver_level_1_id",
      "adserver_level_1_name",
      "adserver_level_2_id",
      "adserver_level_2_name",
      "adserver_level_3_id",
      "adserver_level_3_name",
      "adserver_level_4_id",
      "adserver_level_4_name",
      "adserver_level_5_id",
      "adserver_level_6_id",
      "adserver_level_6_name",
      "currency",
      "conversion_metric",
      "cost_based_metric",
      "data_source",
      "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "completed_views_first",
      "completed_views_mid",
      "completed_views_third",
      "completed_views_full",
      "video_start",
      "video_views",
      "view_through_conversions",
      "click_through_conversions",
      "conversions"
  ),
  DEDUPLICATED_ADGROUP_LEVEL_ONLY AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      ADGROUP_LEVEL_DEDUPLICATION
    WHERE
      "platform_campaign_group_id" IS NOT NULL
  ),
  CAMPAIGN_LEFTOVERS_FROM_ADGROUP_LEVEL_DEDUPLICATION AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      ADGROUP_LEVEL_DEDUPLICATION
    WHERE
      "platform_campaign_group_id" IS NULL
  ),
  CAMPAIGN_LEVEL_WITH_LEFTOVERS AS (
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      CAMPAIGN_LEVEL_ADFORM
    UNION ALL
    SELECT
      {{ adform_columns | join(',\n      ') }}
    FROM
      CAMPAIGN_LEFTOVERS_FROM_ADGROUP_LEVEL_DEDUPLICATION
  ),
  CAMPAIGN_LEVEL_DEDUPLICATION AS (
    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      CASE
        WHEN COUNT(DISTINCT "platform_campaign_id") = 1 THEN MAX("platform_campaign_id")
        ELSE NULL
      END AS "platform_campaign_id",
      CASE
        WHEN COUNT(DISTINCT "platform_campaign_name") = 1 THEN MAX("platform_campaign_name")
        ELSE NULL
      END AS "platform_campaign_name",
      "platform_campaign_group_id",
      "platform_campaign_group_name",
      "ad_asset_id",
      "ad_asset",
      "adserver_advertiser_id",
      "adserver_level_1_id",
      "adserver_level_1_name",
      "adserver_level_2_id",
      "adserver_level_2_name",
      "adserver_level_3_id",
      "adserver_level_3_name",
      "adserver_level_4_id",
      "adserver_level_4_name",
      "adserver_level_5_id",
      "adserver_level_6_id",
      "adserver_level_6_name",
      "currency",
      "conversion_metric",
      "cost_based_metric",
      "data_source",
      "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "completed_views_first",
      "completed_views_mid",
      "completed_views_third",
      "completed_views_full",
      "video_start",
      "video_views",
      "view_through_conversions",
      "click_through_conversions",
      "conversions"
    FROM
      CAMPAIGN_LEVEL_WITH_LEFTOVERS
    GROUP BY
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "platform_campaign_group_id",
      "platform_campaign_group_name",
      "ad_asset_id",
      "ad_asset",
      "adserver_advertiser_id",
      "adserver_level_1_id",
      "adserver_level_1_name",
      "adserver_level_2_id",
      "adserver_level_2_name",
      "adserver_level_3_id",
      "adserver_level_3_name",
      "adserver_level_4_id",
      "adserver_level_4_name",
      "adserver_level_5_id",
      "adserver_level_6_id",
      "adserver_level_6_name",
      "currency",
      "conversion_metric",
      "cost_based_metric",
      "data_source",
      "data_source_details",
      "date",
      "impressions",
      "clicks",
      "spend",
      "completed_views_first",
      "completed_views_mid",
      "completed_views_third",
      "completed_views_full",
      "video_start",
      "video_views",
      "view_through_conversions",
      "click_through_conversions",
      "conversions"
  )
SELECT
  {{ adform_columns | join(',\n      ') }}
FROM
  DEDUPLICATED_AD_LEVEL_ONLY
UNION ALL
SELECT
  {{ adform_columns | join(',\n      ') }}
FROM
  DEDUPLICATED_ADGROUP_LEVEL_ONLY
UNION ALL
SELECT
  {{ adform_columns | join(',\n      ') }}
FROM
  CAMPAIGN_LEVEL_DEDUPLICATION