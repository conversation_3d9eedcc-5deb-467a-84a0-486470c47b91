
WITH
  LATEST_ADFORM_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.REPORTS_ADFORMCONVERSION_V2
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'reports_adformconversion_v2'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  INTEGRATED_TAXONOMY_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "tile_id",
      "tile_name",
      "platform_campaign_internal_id",
      "platform_campaign_external_id",
      "platform_campaign_name",
      "platform_campaign_internal_group_id",
      "platform_campaign_external_group_id",
      "platform_campaign_group_name",
      "ad_asset_internal_id",
      "ad_asset_external_id",
      "ad_asset",
      "currency",
      "granularity_level"
    FROM
      {{ ref('mint_unified_all_platforms_taxonomy_view') }}
    WHERE
      "granularity_level" IN ('ad')
  ),
  ADFORM_MAPPINGS AS (
    SELECT DISTINCT
      "adform_advertiser_id",
      "adform_journey_id",
      "adform_campaign_external_id",
      "adform_campaign_channel_name",
      "adform_campaign_name",
      "adform_media_external_id",
      "adform_media_platform_name",
      "adform_media_name",
      "adform_line_item_external_id",
      "adform_line_item_name",
      "adform_banner_external_id",
      "adform_banner_name",
      "adform_tag_external_id",
      "adform_tracker_external_id",
      "adform_tracker_name",
      "adform_tracker_conversion_metric_label",
      "adform_tracker_conversion_cost_based_metric_label",
      "adform_platform_item_id",
      "adform_platform_item_external_id"
    FROM
      {{ ref('adform_mappings_view') }}
    WHERE
      "adfrom_is_mappings_complete" = TRUE
      AND "adform_is_direct_campaign_join" = FALSE
  ),
  BASE_ADFORM_REPORTS AS (
    SELECT
      "date",
      "advertiser_id",
      "campaign_id",
      "tag_id",
      "tracker_id",
      SUM("conversions_post_imp") AS "view_through_conversions",
      SUM("conversions_post_click") AS "click_through_conversions",
      SUM("conversions") AS "conversions"
    FROM
      LATEST_ADFORM_REPORTS
    WHERE
      "tracker_id" != 0
    GROUP BY
      "date",
      "advertiser_id",
      "campaign_id",
      "tag_id",
      "tracker_id"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  "platform_campaign_external_id" AS "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_external_group_id" AS "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset_external_id" AS "ad_asset_id",
  "ad_asset",
  "advertiser_id" AS "adserver_advertiser_id",
  "adform_campaign_external_id" AS "adserver_level_1_id",
  "adform_campaign_name" AS "adserver_level_1_name",
  "adform_media_external_id" AS "adserver_level_2_id",
  "adform_media_name" AS "adserver_level_2_name",
  "adform_line_item_external_id" AS "adserver_level_3_id",
  "adform_line_item_name" AS "adserver_level_3_name",
  "adform_banner_external_id" AS "adserver_level_4_id",
  "adform_banner_name" AS "adserver_level_4_name",
  "adform_tag_external_id" AS "adserver_level_5_id",
  "adform_tracker_external_id" AS "adserver_level_6_id",
  "adform_tracker_name" AS "adserver_level_6_name",
  "currency",
  "adform_tracker_conversion_metric_label" AS "conversion_metric",
  "adform_tracker_conversion_cost_based_metric_label" AS "cost_based_metric",
  'Adform' AS "data_source",
  'reports_adformconversion_v2' AS "data_source_details",
  "date",
  "view_through_conversions",
  "click_through_conversions",
  "conversions"
FROM
  INTEGRATED_TAXONOMY_CAMPAIGN_LEVEL AS T
  JOIN ADFORM_MAPPINGS AS M ON T."journey_id" = M."adform_journey_id"
  AND T."channel_name" = M."adform_campaign_channel_name"
  AND T."platform_name" = M."adform_media_platform_name"
  AND T."ad_asset_internal_id" = M."adform_platform_item_id"
  AND T."ad_asset_external_id" = M."adform_platform_item_external_id"
  JOIN BASE_ADFORM_REPORTS AS R ON M."adform_tag_external_id" = R."tag_id"
  AND M."adform_campaign_external_id" = r."campaign_id"
  AND M."adform_advertiser_id" = R."advertiser_id"
  AND M."adform_tracker_external_id" = R."tracker_id"
WHERE
  R."date" BETWEEN T."media_row_date_from" AND T."media_row_date_to"