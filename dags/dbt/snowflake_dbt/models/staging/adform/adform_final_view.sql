{{ union_models([
    'adform_base_campaign_level_for_virtual_platform_case',
    'adform_conversions_campaign_level_for_integrated_platforms',
    'adform_conversions_campaign_level_for_virtual_platforms',
    'adform_conversions_adgrpoup_level_for_integrated_platform_case',
    'adform_conversions_ad_level_for_integrated_platform_case',
    'adform_conversions_dv360_youtube_level_case',
    'adform_conversions_direct_campaign_to_campaign_mappings_case'
],
[
    'company_id',
    'journey_id',
    'journey_name',
    'media_plan_id',
    'media_plan_name',
    'channel_id',
    'channel_name',
    'platform_id',
    'platform_name',
    'tile_id',
    'tile_name',
    'media_row_id',
    'media_row_name',
    'platform_campaign_id',
    'platform_campaign_name',
    'platform_campaign_group_id',
    'platform_campaign_group_name',
    'ad_asset_id',
    'ad_asset',
    'adserver_advertiser_id',
    'adserver_level_1_id',
    'adserver_level_1_name',
    'adserver_level_2_id',
    'adserver_level_2_name',
    'adserver_level_3_id',
    'adserver_level_3_name',
    'adserver_level_4_id',
    'adserver_level_4_name',
    'adserver_level_5_id',
    'adserver_level_6_id',
    'adserver_level_6_name',
    'currency',
    'conversion_metric',
    'cost_based_metric',
    'data_source',
    'data_source_details',
    'date',
    'impressions',
    'clicks',
    'spend',
    'completed_views_first',
    'completed_views_mid',
    'completed_views_third',
    'completed_views_full',
    'video_start',
    'video_views',
    'view_through_conversions',
    'click_through_conversions',
    'conversions'
]) }}
