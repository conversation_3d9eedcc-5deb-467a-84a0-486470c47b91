
WITH
  LATEST_ADFORM_INTEGRATIONS AS (
    SELECT
      *
    FROM
      ADFORM.ADFORM_ADFORMINTEGRATION
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADFORM'
              AND "TABLE_NAME" = 'adform_adformintegration'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_ADFORM_SETTINGS AS (
    SELECT
      *
    FROM
      ADFORM.ADFORM_ADFORMSETTINGS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADFORM'
              AND "TABLE_NAME" = 'adform_adformsettings'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_ADFORM_CAMPAIGN_MAPPINGS AS (
    SELECT
      *
    FROM
      ADFORM.ADFORM_ADFORMCAMPAIGNMAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADFORM'
              AND "TABLE_NAME" = 'adform_adformcampaignmapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_ADFORM_MEDIA_MAPPINGS AS (
    SELECT
      *
    FROM
      ADFORM.ADFORM_ADFORMMEDIAMAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADFORM'
              AND "TABLE_NAME" = 'adform_adformmediamapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_LINE_ITEM_MAPPINGS AS (
    SELECT
      *
    FROM
      ADFORM.ADFORM_ADFORMLINEITEMMAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADFORM'
              AND "TABLE_NAME" = 'adform_adformlineitemmapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_ADFORM_BANNER_MAPPINGS AS (
    SELECT
      *
    FROM
      ADFORM.ADFORM_ADFORMBANNERMAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADFORM'
              AND "TABLE_NAME" = 'adform_adformbannermapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_ADFORM_MEDIA_CAMPAIGN_ASSIGNMENT AS (
    SELECT
      *
    FROM
      ADFORM.ADFORM_ADFORMMEDIAMAPPING_CAMPAIGN_MAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADFORM'
              AND "TABLE_NAME" = 'adform_adformmediamapping_campaign_mapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_ADFORM_TRACKER_MAPPINGS AS (
    SELECT
      *
    FROM
      ADFORM.ADFORM_ADFORMTRACKINGFILTERMAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'ADFORM'
              AND "TABLE_NAME" = 'adform_adformtrackingfiltermapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  )
SELECT DISTINCT
  I."company_id" AS "company_id",
  I."id" AS "adform_integration_id",
  I."name" AS "adform_integration_name",
  I."advertiser" AS "adform_advertiser_id",
  I."is_active" AS "adform_integration_is_active",
  S."id" AS "adform_settings_id",
  S."campaign_collection" AS "adform_journey_id",
  CM."id" AS "adform_campaign_internal_id",
  CM."campaign" AS "adform_campaign_external_id",
  CM."channel" AS "adform_campaign_channel_name",
  CM."name" AS "adform_campaign_name",
  CM."taxonomy_internal_id" AS "campaign_level_adform_platform_item_id",
  CM."platform" AS "adform_campaign_platform_name",
  CASE
    WHEN CM."platform" IS NOT NULL THEN true
    ELSE false
  END AS "adform_is_direct_campaign_join",
  MM."id" AS "adform_media_internal_id",
  MM."media" AS "adform_media_external_id",
  MM."platform" AS "adform_media_platform_name",
  MM."name" AS "adform_media_name",
  LIM."id" AS "adform_line_item_internal_id",
  LIM."line_item" AS "adform_line_item_external_id",
  LIM."name" AS "adform_line_item_name",
  LIM."tile_id" AS "adform_tile_id",
  BM."id" AS "adform_banner_internal_id",
  BM."external_ad" AS "adform_banner_external_id",
  BM."title" AS "adform_banner_name",
  BM."external_tag_id" AS "adform_tag_external_id",
  CAST(BM."platform_item_id" AS TEXT) AS "adform_platform_item_id",
  CAST(BM."platform_item_external_id" AS TEXT) AS "adform_platform_item_external_id",
  TM."id" AS "adform_tracker_internal_id",
  TM."tracking_filter" AS "adform_tracker_external_id",
  TM."name" AS "adform_tracker_name",
  TM."conversion_metric" AS "adform_tracker_conversion_metric_label",
  TM."cost_based_metric" AS "adform_tracker_conversion_cost_based_metric_label",
  CASE
    WHEN BM."external_tag_id" IS NOT NULL
    OR CM."platform" IS NOT NULL THEN true
    ELSE false
  END AS "adfrom_is_mappings_complete"
FROM
  LATEST_ADFORM_INTEGRATIONS AS I
  JOIN LATEST_ADFORM_SETTINGS AS S ON I."id" = S."integration_id"
  LEFT JOIN LATEST_ADFORM_TRACKER_MAPPINGS AS TM ON S."id" = TM."settings_id"
  LEFT JOIN LATEST_ADFORM_CAMPAIGN_MAPPINGS AS CM ON S."id" = CM."settings_id"
  LEFT JOIN LATEST_ADFORM_MEDIA_CAMPAIGN_ASSIGNMENT AS MCA ON CM."id" = MCA."adformcampaignmapping_id"
  LEFT JOIN LATEST_ADFORM_MEDIA_MAPPINGS AS MM ON MM."id" = MCA."adformmediamapping_id"
  LEFT JOIN LATEST_LINE_ITEM_MAPPINGS AS LIM ON LIM."media_mapping_id" = MM."id"
  AND LIM."campaign_mapping_id" = CM."id"
  LEFT JOIN LATEST_ADFORM_BANNER_MAPPINGS AS BM ON LIM."id" = BM."line_item_mapping_id"