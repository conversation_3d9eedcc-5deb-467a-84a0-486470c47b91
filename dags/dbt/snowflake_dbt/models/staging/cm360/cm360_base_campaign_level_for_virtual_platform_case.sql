
WITH
  LATEST_BASE_CM360_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.CAMPAIGN_MANAGER_BASE_REPORT
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'campaign_manager_base_report'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_SUB_PRICINGS AS (
    SELECT
      *
    FROM
      DATINTELL.CAMPAIGN_GHOSTCAMPAIGNPRICINGSUBITEM
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'DATINTELL'
              AND "TABLE_NAME" = 'campaign_ghostcampaignpricingsubitem'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  INTEGRATED_TAXONOMY_PRICING_ITEM_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "tile_id",
      "tile_name",
      "platform_campaign_internal_id",
      "platform_campaign_external_id",
      "platform_campaign_name",
      "platform_campaign_internal_group_id",
      "platform_campaign_external_group_id",
      "platform_campaign_group_name",
      "ad_asset_internal_id",
      "ad_asset_external_id",
      "ad_asset",
      "currency",
      "granularity_level",
      "pricing_item_type"
    FROM
      {{ ref('mint_unified_all_platforms_taxonomy_view') }}
    WHERE
      "granularity_level" IN ('pricing_item')
  ),
  TAXONOMY_PRICING_ITEMS_WITH_PRICE AS (
    SELECT
      TX.*,
      SB."date" AS "sub_pricing_date",
      SB."price" AS "price"
    FROM
      INTEGRATED_TAXONOMY_PRICING_ITEM_LEVEL AS TX
      LEFT JOIN LATEST_SUB_PRICINGS AS SB
      ON CAST(TX."platform_campaign_internal_id" AS TEXT) = CAST(SB."pricing_item_id" AS TEXT)
      AND CAST(TX."platform_campaign_external_id" AS TEXT) = CAST(SB."pricing_item_id" AS TEXT)
  ),
  CM360_MAPPINGS AS (
    SELECT DISTINCT
      "cm360_is_dv360_linkage_join",
      "cm360_is_paid_search_join",
      "cm360_journey_id",
      "cm360_campaign_external_id",
      "cm360_campaign_channel_name",
      "cm360_campaign_name",
      "cm360_site_external_id",
      "cm360_site_platform_name",
      "cm360_site_name",
      "cm360_placement_external_id",
      "cm360_placement_name",
      "cm360_platform_item_id",
      "cm360_platform_item_external_id"
    FROM
      {{ ref('cm360_mappings_view') }}
  ),
  BASE_CM360_REPORTS AS (
    SELECT
      "date",
      "advertiser_id",
      "placement_id",
      SUM("impressions") AS "impressions",
      SUM("clicks") AS "clicks",
      SUM("rich_media_video_first_quartile_completes") AS "completed_views_first",
      SUM("rich_media_video_midpoints") AS "completed_views_mid",
      SUM("rich_media_video_third_quartile_completes") AS "completed_views_third",
      SUM("rich_media_video_completions") AS "completed_views_full",
      SUM("rich_media_video_plays") AS "video_start",
      SUM("rich_media_video_plays") AS "video_views",
      SUM("active_view_viewable_impressions") AS "active_view_viewable_impressions",
      SUM("active_view_measurable_impressions") AS "active_view_measurable_impressions"
    FROM
      LATEST_BASE_CM360_REPORTS
    WHERE
      "activity_id" = 0
    GROUP BY
      "date",
      "advertiser_id",
      "placement_id"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  CAST("platform_campaign_external_id" AS TEXT) AS "platform_campaign_id",
  CAST("platform_campaign_name" AS TEXT) AS "platform_campaign_name",
  CAST("platform_campaign_external_group_id" AS TEXT) AS "platform_campaign_group_id",
  CAST("platform_campaign_group_name" AS TEXT) AS "platform_campaign_group_name",
  CAST("ad_asset_external_id" AS TEXT) AS "ad_asset_id",
  CAST("ad_asset" AS TEXT) AS "ad_asset",
  "advertiser_id" AS "adserver_advertiser_id",
  "cm360_campaign_external_id" AS "adserver_level_1_id",
  "cm360_campaign_name" AS "adserver_level_1_name",
  "cm360_site_external_id" AS "adserver_level_2_id",
  "cm360_site_name" AS "adserver_level_2_name",
  "cm360_placement_external_id" AS "adserver_level_3_id",
  "cm360_placement_name" AS "adserver_level_3_name",
  "currency",
  'CM360' AS "data_source",
  'campaign_manager_base_report' AS "data_source_details",
  "date",
  "impressions",
  "clicks",
  CAST(
    CASE
      WHEN "pricing_item_type" = 'cpc' THEN "clicks" * "price"
      WHEN "pricing_item_type" = 'cpm' THEN ("impressions" / 1000.0) * "price"
      WHEN "pricing_item_type" = 'cpv' THEN "completed_views_full" * "price"
      ELSE NULL
    END AS NUMERIC(38, 10)
  ) AS "spend",
  "completed_views_first",
  "completed_views_mid",
  "completed_views_third",
  "completed_views_full",
  "video_start",
  "video_views",
  "active_view_viewable_impressions",
  "active_view_measurable_impressions"
FROM
  TAXONOMY_PRICING_ITEMS_WITH_PRICE AS T
  JOIN CM360_MAPPINGS AS M ON T."journey_id" = M."cm360_journey_id"
  AND T."channel_name" = M."cm360_campaign_channel_name"
  AND T."platform_name" = M."cm360_site_platform_name"
  AND CAST(T."platform_campaign_internal_id" AS TEXT) = CAST(M."cm360_platform_item_id" AS TEXT)
  AND CAST(T."platform_campaign_external_id" AS TEXT) = CAST(M."cm360_platform_item_external_id" AS TEXT)
  JOIN BASE_CM360_REPORTS AS R ON M."cm360_placement_external_id" = R."placement_id"
  AND T."sub_pricing_date" = R."date"
WHERE
  R."date" BETWEEN T."media_row_date_from" AND T."media_row_date_to"