
WITH
  LATEST_CM360_INTEGRATIONS AS (
    SELECT
      *
    FROM
      CM360.CAMPAIGN_MANAGER_CAMPAIGNMANAGERINTEGRATION
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'CM360'
              AND "TABLE_NAME" = 'campaign_manager_campaignmanagerintegration'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CM360_SETTINGS AS (
    SELECT
      *
    FROM
      CM360.CAMPAIGN_MANAGER_CAMPAIGNMANAGERSETTINGS
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'CM360'
              AND "TABLE_NAME" = 'campaign_manager_campaignmanagersettings'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CM360_CAMPAIGN_MAPPINGS AS (
    SELECT
      *
    FROM
      CM360.CAMPAIGN_MANAGER_CAMPAIGNMANAGERCAMPAIGNMAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'CM360'
              AND "TABLE_NAME" = 'campaign_manager_campaignmanagercampaignmapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CM360_SITE_MAPPINGS AS (
    SELECT
      *
    FROM
      CM360.SITE_MAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'CM360'
              AND "TABLE_NAME" = 'site_mapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CM360_PLACEMENT_MAPPINGS AS (
    SELECT
      *
    FROM
      CM360.CAMPAIGN_MANAGER_PLACEMENT
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'CM360'
              AND "TABLE_NAME" = 'campaign_manager_placement'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CM360_SITE_CAMPAIGN_ASSIGNMENT AS (
    SELECT
      *
    FROM
      CM360.SITE_MAPPING_ASSIGNMENT
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'CM360'
              AND "TABLE_NAME" = 'site_mapping_assignment'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CM360_ACTIVITY_MAPPINGS AS (
    SELECT
      *
    FROM
      CM360.CAMPAIGN_MANAGER_CAMPAIGNMANAGERFLOODLIGHTACTIVITYMAPPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'CM360'
              AND "TABLE_NAME" = 'campaign_manager_campaignmanagerfloodlightactivitymapping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_CM360_PLATFORM_GROUPING AS (
    SELECT
      *
    FROM
      CM360.PLATFORM_GROUPING
    WHERE
      run_id = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'CM360'
              AND "TABLE_NAME" = 'platform_grouping'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  )
SELECT DISTINCT
  I."company_id" AS "company_id",
  I."id" AS "cm360_integration_id",
  I."name" AS "cm360_integration_name",
  I."account" AS "cm360_account_id",
  I."profile_id" AS "cm360_profile_id",
  I."advertiser_ids" AS "cm360_advertiser_ids",
  I."dv360_linkage" AS "cm360_is_dv360_linkage_join",
  I."is_paid_search_account" AS "cm360_is_paid_search_account",
  I."is_floodlight_report_eligible" AS "cm360_floodlight_report_eligible",
  I."is_active" AS "cm360_integration_is_active",
  S."id" AS "cm360_settings_id",
  S."campaign_collection" AS "cm360_journey_id",
  S."floodlight_activity_config_ids" AS "cm360_floodlight_activity_config_ids",
  CM."id" AS "cm360_campaign_internal_id",
  CM."campaign" AS "cm360_campaign_external_id",
  CM."channel" AS "cm360_campaign_channel_name",
  CM."name" AS "cm360_campaign_name",
  CM."default_landing_page_url" AS "cm360_default_landing_page_url",
  SM."id" AS "cm360_site_internal_id",
  SM."external_id" AS "cm360_site_external_id",
  SM."platform" AS "cm360_site_platform_name",
  SM."name" AS "cm360_site_name",
  CASE
    WHEN SM."name" LIKE 'DART Search :%' THEN TRUE
    ELSE FALSE
  END AS "cm360_is_paid_search_join",
  CASE
    WHEN PG."type" IS NULL THEN 'pricing_item'
    ELSE PG."type"
  END AS "cm360_grouping_type",
  PM."id" AS "cm360_placement_internal_id",
  PM."external_id" AS "cm360_placement_external_id",
  PM."name" AS "cm360_placement_name",
  PM."tile" AS "cm360_tile_id",
  CAST(PM."platform_item_id" AS TEXT) AS "cm360_platform_item_id",
  CAST(PM."platform_item_external_id" AS TEXT) AS "cm360_platform_item_external_id",
  FM."id" AS "cm360_activity_internal_id",
  FM."name" AS "cm360_activity_name",
  FM."floodlight_activity" AS "cm360_activity_external_id",
  FM."conversion_metric" AS "cm360_activity_conversion_metric_label",
  FM."cost_based_metric" AS "cm360_activity_conversion_cost_based_metric_label",
FROM
  LATEST_CM360_INTEGRATIONS AS I
  JOIN LATEST_CM360_SETTINGS AS S ON I."id" = S."integration_id"
  LEFT JOIN LATEST_CM360_ACTIVITY_MAPPINGS AS FM ON S."id" = FM."settings_id"
  LEFT JOIN LATEST_CM360_CAMPAIGN_MAPPINGS AS CM ON S."id" = CM."settings_id"
  LEFT JOIN LATEST_CM360_SITE_CAMPAIGN_ASSIGNMENT AS SMA ON CM."id" = SMA."campaign_mapping_id"
  LEFT JOIN LATEST_CM360_SITE_MAPPINGS AS SM ON SM."id" = SMA."site_mapping_id"
  LEFT JOIN LATEST_CM360_PLACEMENT_MAPPINGS AS PM ON SMA."id" = PM."site_assignment_id"
  LEFT JOIN LATEST_CM360_PLATFORM_GROUPING AS PG ON PG."settings_id" = S."id"
  AND CAST(PG."channel" AS TEXT) = CAST(CM."channel" AS TEXT)
  AND CAST(PG."platform" AS TEXT) = CAST(SM."platform" AS TEXT)