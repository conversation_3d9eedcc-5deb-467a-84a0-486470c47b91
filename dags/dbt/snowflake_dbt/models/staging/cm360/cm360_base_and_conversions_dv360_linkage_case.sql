
WITH
  LATEST_DV360_CM360_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.CAMPAIGN_MANAGER_DV360_REPORT
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'campaign_manager_dv360_report'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  INTEGRATED_TAXONOMY_AD_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "tile_id",
      "tile_name",
      "platform_campaign_internal_id",
      "platform_campaign_external_id",
      "platform_campaign_name",
      "platform_campaign_internal_group_id",
      "platform_campaign_external_group_id",
      "platform_campaign_group_name",
      "ad_asset_internal_id",
      "ad_asset_external_id",
      "ad_asset",
      "currency",
      "granularity_level"
    FROM
      {{ ref('mint_unified_all_platforms_taxonomy_view') }}
    WHERE
      "granularity_level" IN ('ad')
  ),
  CM360_BASE_MAPPINGS AS (
    SELECT DISTINCT
      "cm360_is_dv360_linkage_join",
      "cm360_journey_id"
    FROM
      {{ ref('cm360_mappings_view') }}
    WHERE
      (
        "cm360_is_dv360_linkage_join" = TRUE
        AND "cm360_site_platform_name" IN ('dv360_integrated', 'youtube_dv360_integrated')
      )
  ),
  CM360_CONVERSIONS_MAPPINGS AS (
    SELECT DISTINCT
      "cm360_is_dv360_linkage_join",
      "cm360_journey_id",
      "cm360_activity_external_id",
      "cm360_activity_name",
      "cm360_activity_conversion_metric_label",
      "cm360_activity_conversion_cost_based_metric_label"
    FROM
      {{ ref('cm360_mappings_view') }}
    WHERE
      (
        "cm360_is_dv360_linkage_join" = TRUE
        AND "cm360_site_platform_name" IN ('dv360_integrated', 'youtube_dv360_integrated')
      )
  ),
  BASE_CM360_REPORTS AS (
    SELECT
      "date",
      "advertiser_id",
      "dv360_campaign_id",
      "dv360_insertion_order_id",
      "dv360_line_item_id",
      SUM("active_view_viewable_impressions") AS "active_view_viewable_impressions",
      SUM("active_view_measurable_impressions") AS "active_view_measurable_impressions"
    FROM
      LATEST_DV360_CM360_REPORTS
    WHERE
      "activity_id" = 0
    GROUP BY
      "date",
      "advertiser_id",
      "dv360_campaign_id",
      "dv360_insertion_order_id",
      "dv360_line_item_id"
  ),
  CONVERSIONS_CM360_REPORTS AS (
    SELECT
      "date",
      "advertiser_id",
      "dv360_campaign_id",
      "dv360_insertion_order_id",
      "dv360_line_item_id",
      "activity_id",
      SUM("total_conversions") AS "conversions",
      SUM("view_through_conversions") AS "view_through_conversions",
      SUM("click_through_conversions") AS "click_through_conversions"
    FROM
      LATEST_DV360_CM360_REPORTS
    WHERE
      "activity_id" != 0
    GROUP BY
      "date",
      "advertiser_id",
      "dv360_campaign_id",
      "dv360_insertion_order_id",
      "dv360_line_item_id",
      "activity_id"
  ),
  BASE_CM360_DV360_PART AS (
    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "platform_campaign_external_id" AS "platform_campaign_id",
      "platform_campaign_name",
      "platform_campaign_external_group_id" AS "platform_campaign_group_id",
      "platform_campaign_group_name",
      "ad_asset_external_id" AS "ad_asset_id",
      "ad_asset",
      "advertiser_id" AS "adserver_advertiser_id",
      "currency",
      'CM360' AS "data_source",
      'campaign_manager_dv360_report' AS "data_source_details",
      "date",
      "active_view_measurable_impressions",
      "active_view_viewable_impressions"
    FROM
      INTEGRATED_TAXONOMY_AD_LEVEL AS T
      JOIN CM360_BASE_MAPPINGS AS M ON T."journey_id" = M."cm360_journey_id"
      JOIN BASE_CM360_REPORTS AS R ON T."platform_campaign_external_id" = CAST(R."dv360_campaign_id" AS TEXT)
      AND T."platform_campaign_external_group_id" = CAST(R."dv360_insertion_order_id" AS TEXT)
      AND T."ad_asset_external_id" = CAST(R."dv360_line_item_id" AS TEXT)
    WHERE
      R."date" BETWEEN T."media_row_date_from" AND T."media_row_date_to"
  ),
  CONVERIONS_CM360_DV360_PART AS (
    SELECT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "tile_id",
      "tile_name",
      "media_row_id",
      "media_row_name",
      "platform_campaign_external_id" AS "platform_campaign_id",
      "platform_campaign_name",
      "platform_campaign_external_group_id" AS "platform_campaign_group_id",
      "platform_campaign_group_name",
      "ad_asset_external_id" AS "ad_asset_id",
      "ad_asset",
      "advertiser_id" AS "adserver_advertiser_id",
      "cm360_activity_external_id" AS "adserver_level_4_id",
      "cm360_activity_name" AS "adserver_level_4_name",
      "currency",
      "cm360_activity_conversion_metric_label" AS "conversion_metric",
      "cm360_activity_conversion_cost_based_metric_label" AS "cost_based_metric",
      'CM360' AS "data_source",
      'campaign_manager_dv360_report' AS "data_source_details",
      "date",
      "view_through_conversions",
      "click_through_conversions",
      "conversions"
    FROM
      INTEGRATED_TAXONOMY_AD_LEVEL AS T
      JOIN CM360_CONVERSIONS_MAPPINGS AS M ON T."journey_id" = M."cm360_journey_id"
      LEFT JOIN CONVERSIONS_CM360_REPORTS AS R ON T."platform_campaign_external_id" = CAST(R."dv360_campaign_id" AS TEXT)
      AND T."platform_campaign_external_group_id" = CAST(R."dv360_insertion_order_id" AS TEXT)
      AND T."ad_asset_external_id" = CAST(R."dv360_line_item_id" AS TEXT)
      AND M."cm360_activity_external_id" = R."activity_id"
    WHERE
      R."date" BETWEEN T."media_row_date_from" AND T."media_row_date_to"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset_id",
  "ad_asset",
  "adserver_advertiser_id",
  NULL AS "adserver_level_4_id",
  NULL AS "adserver_level_4_name",
  "currency",
  "data_source",
  "data_source_details",
  "date",
  "active_view_measurable_impressions",
  "active_view_viewable_impressions",
  NULL AS "conversion_metric",
  NULL AS "cost_based_metric",
  NULL AS "view_through_conversions",
  NULL AS "click_through_conversions",
  NULL AS "conversions"
FROM
  BASE_CM360_DV360_PART
UNION ALL
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset_id",
  "ad_asset",
  "adserver_advertiser_id",
  "adserver_level_4_id",
  "adserver_level_4_name",
  "currency",
  "data_source",
  "data_source_details",
  "date",
  NULL AS "active_view_measurable_impressions",
  NULL AS "active_view_viewable_impressions",
  "conversion_metric",
  "cost_based_metric",
  "view_through_conversions",
  "click_through_conversions",
  "conversions"
FROM
  CONVERIONS_CM360_DV360_PART