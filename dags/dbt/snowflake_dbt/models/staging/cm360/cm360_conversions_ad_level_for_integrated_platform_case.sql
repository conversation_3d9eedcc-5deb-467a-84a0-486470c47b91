
WITH
  LATEST_CONVERSIONS_CM360_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.CAMPAIGN_MANAGER_BASE_REPORT
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'campaign_manager_base_report'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  LATEST_FLOODLIGHT_CM360_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.CAMPAIGN_MANAGER_FLOODLIGHT_BASE_REPORT
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'campaign_manager_floodlight_base_report'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  INTEGRATED_TAXONOMY_CAMPAIGN_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "tile_id",
      "tile_name",
      "platform_campaign_internal_id",
      "platform_campaign_external_id",
      "platform_campaign_name",
      "platform_campaign_internal_group_id",
      "platform_campaign_external_group_id",
      "platform_campaign_group_name",
      "ad_asset_internal_id",
      "ad_asset_external_id",
      "ad_asset",
      "currency",
      "granularity_level"
    FROM
      {{ ref('mint_unified_all_platforms_taxonomy_view') }}
    WHERE
      "granularity_level" IN ('ad')
  ),
  CM360_MAPPINGS AS (
    SELECT DISTINCT
      "cm360_is_dv360_linkage_join",
      "cm360_is_paid_search_join",
      "cm360_journey_id",
      "cm360_campaign_external_id",
      "cm360_campaign_channel_name",
      "cm360_campaign_name",
      "cm360_site_external_id",
      "cm360_site_platform_name",
      "cm360_site_name",
      "cm360_placement_external_id",
      "cm360_placement_name",
      "cm360_activity_external_id",
      "cm360_activity_name",
      "cm360_activity_conversion_metric_label",
      "cm360_activity_conversion_cost_based_metric_label",
      "cm360_platform_item_id",
      "cm360_platform_item_external_id"
    FROM
      {{ ref('cm360_mappings_view') }}
    WHERE
      NOT ((
        "cm360_is_dv360_linkage_join" = TRUE
        AND "cm360_site_platform_name" IN ('dv360_integrated', 'youtube_dv360_integrated')
      )
      OR (
        "cm360_is_paid_search_join" = TRUE
        AND "cm360_site_platform_name" IN ('adwords', 'youtube_google_ads_integrated', 'bing_integrated')
      ))
  ),
  CONVERSIONS_CM360_REPORTS AS (
    SELECT
      "date",
      "advertiser_id",
      "placement_id",
      "activity_id",
      SUM("total_conversions") AS "conversions",
      SUM("view_through_conversions") AS "view_through_conversions",
      SUM("click_through_conversions") AS "click_through_conversions"
    FROM
      LATEST_CONVERSIONS_CM360_REPORTS
    WHERE
      "activity_id" != 0
    GROUP BY
      "date",
      "advertiser_id",
      "placement_id",
      "activity_id"
  ),
  FLOODLIGHT_CM360_REPORTS AS (
    SELECT
      "date",
      "advertiser_id",
      "placement_id",
      "activity_id",
      SUM("total_conversions_revenue") AS "total_conversions_revenue",
      SUM("transaction_count") AS "transaction_count"
    FROM
      LATEST_FLOODLIGHT_CM360_REPORTS
    WHERE
      "activity_id" != 0
    GROUP BY
      "date",
      "advertiser_id",
      "placement_id",
      "activity_id"
  ),
  COMBINED_CM360_REPORTS AS (
    SELECT
      CR."date",
      CR."advertiser_id",
      CR."placement_id",
      CR."activity_id",
      CR."conversions",
      CR."view_through_conversions",
      CR."click_through_conversions",
      FR."total_conversions_revenue",
      FR."transaction_count"
    FROM
      CONVERSIONS_CM360_REPORTS AS CR
      LEFT JOIN FLOODLIGHT_CM360_REPORTS AS FR ON CR."date" = FR."date"
      AND CR."advertiser_id" = FR."advertiser_id"
      AND CR."placement_id" = FR."placement_id"
      AND CR."activity_id" = FR."activity_id"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  "platform_campaign_external_id" AS "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_external_group_id" AS "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset_external_id" AS "ad_asset_id",
  "ad_asset",
  "advertiser_id" AS "adserver_advertiser_id",
  "cm360_campaign_external_id" AS "adserver_level_1_id",
  "cm360_campaign_name" AS "adserver_level_1_name",
  "cm360_site_external_id" AS "adserver_level_2_id",
  "cm360_site_name" AS "adserver_level_2_name",
  "cm360_placement_external_id" AS "adserver_level_3_id",
  "cm360_placement_name" AS "adserver_level_3_name",
  "cm360_activity_external_id" AS "adserver_level_4_id",
  "cm360_activity_name" AS "adserver_level_4_name",
  "currency",
  "cm360_activity_conversion_metric_label" AS "conversion_metric",
  "cm360_activity_conversion_cost_based_metric_label" AS "cost_based_metric",
  'CM360' AS "data_source",
  'campaign_manager_base_report, campaign_manager_floodlight_base_report' AS "data_source_details",
  "date",
  "total_conversions_revenue",
  "transaction_count",
  "view_through_conversions",
  "click_through_conversions",
  "conversions"
FROM
  INTEGRATED_TAXONOMY_CAMPAIGN_LEVEL AS T
  JOIN CM360_MAPPINGS AS M ON T."journey_id" = M."cm360_journey_id"
  AND T."channel_name" = M."cm360_campaign_channel_name"
  AND T."platform_name" = M."cm360_site_platform_name"
  AND T."ad_asset_internal_id" = M."cm360_platform_item_id"
  AND T."ad_asset_external_id" = M."cm360_platform_item_external_id"
  LEFT JOIN COMBINED_CM360_REPORTS AS R ON M."cm360_placement_external_id" = R."placement_id"
  AND M."cm360_activity_external_id" = R."activity_id"
WHERE
  R."date" BETWEEN T."media_row_date_from" AND T."media_row_date_to"