
WITH
  LATEST_CONVERSIONS_PAID_SEARCH_REPORTS AS (
    SELECT
      *
    FROM
      REPORTS.CAMPAIGN_MANAGER_PAID_SEARCH_EXTERNAL_REPORT
    WHERE
      RUN_ID = (
        SELECT
          "RUN_ID"
        FROM
          (
            SELECT
              "RUN_ID",
              "TIMESTAMP",
              ROW_NUMBER() OVER (
                PARTITION BY
                  "DB_NAME",
                  "TABLE_NAME"
                ORDER BY
                  "TIMESTAMP" DESC
              ) AS "row_num"
            FROM
              LOAD_METADATA.LOAD_TABLE
            WHERE
              "DB_NAME" = 'REPORTS'
              AND "TABLE_NAME" = 'campaign_manager_paid_search_external_report'
              AND "STATUS" = 'FINISHED'
              AND "ENTRY_NAME" = 'DATA_UPLOAD'
          )
        WHERE
          "row_num" = 1
      )
  ),
  INTEGRATED_TAXONOMY_AD_LEVEL AS (
    SELECT DISTINCT
      "company_id",
      "journey_id",
      "journey_name",
      "media_plan_id",
      "media_plan_name",
      "channel_id",
      "channel_name",
      "platform_id",
      "platform_name",
      "media_row_id",
      "media_row_name",
      "media_row_budget",
      "media_row_date_to",
      "media_row_date_from",
      "tile_id",
      "tile_name",
      "platform_campaign_internal_id",
      "platform_campaign_external_id",
      "platform_campaign_name",
      "platform_campaign_internal_group_id",
      "platform_campaign_external_group_id",
      "platform_campaign_group_name",
      "ad_asset_internal_id",
      "ad_asset_external_id",
      "ad_asset",
      "currency",
      "granularity_level"
    FROM
      {{ ref('mint_unified_all_platforms_taxonomy_view') }}
    WHERE
      "granularity_level" IN ('ad')
  ),
  CM360_MAPPINGS AS (
    SELECT DISTINCT
      "cm360_is_dv360_linkage_join",
      "cm360_is_paid_search_join",
      "cm360_journey_id",
      "cm360_campaign_external_id",
      "cm360_campaign_channel_name",
      "cm360_campaign_name",
      "cm360_site_external_id",
      "cm360_site_platform_name",
      "cm360_site_name",
      "cm360_activity_external_id",
      "cm360_activity_name",
      "cm360_activity_conversion_metric_label",
      "cm360_activity_conversion_cost_based_metric_label",
    FROM
      {{ ref('cm360_mappings_view') }}
    WHERE
      (
        "cm360_is_paid_search_join" = TRUE
        AND "cm360_site_platform_name" IN (
          'adwords',
          'youtube_google_ads_integrated',
          'bing_integrated'
        )
      )
  ),
  CONVERSIONS_PAID_SEARCH_REPORTS AS (
    SELECT
      "date",
      "paid_search_external_campaign_id",
      "paid_search_external_ad_group_id",
      "paid_search_external_ad_id",
      "activity_id",
      SUM("paid_search_actions") + SUM("paid_search_transactions") AS "conversions"
    FROM
      LATEST_CONVERSIONS_PAID_SEARCH_REPORTS
    WHERE
      "activity_id" != 0
    GROUP BY
      "date",
      "paid_search_external_campaign_id",
      "paid_search_external_ad_group_id",
      "paid_search_external_ad_id",
      "activity_id"
  )
SELECT
  "company_id",
  "journey_id",
  "journey_name",
  "media_plan_id",
  "media_plan_name",
  "channel_id",
  "channel_name",
  "platform_id",
  "platform_name",
  "tile_id",
  "tile_name",
  "media_row_id",
  "media_row_name",
  "platform_campaign_external_id" AS "platform_campaign_id",
  "platform_campaign_name",
  "platform_campaign_external_group_id" AS "platform_campaign_group_id",
  "platform_campaign_group_name",
  "ad_asset_external_id" AS "ad_asset_id",
  "ad_asset",
  "cm360_campaign_external_id" AS "adserver_level_1_id",
  "cm360_campaign_name" AS "adserver_level_1_name",
  "cm360_site_external_id" AS "adserver_level_2_id",
  "cm360_site_name" AS "adserver_level_2_name",
  "cm360_activity_external_id" AS "adserver_level_4_id",
  "cm360_activity_name" AS "adserver_level_4_name",
  "currency",
  "cm360_activity_conversion_metric_label" AS "conversion_metric",
  "cm360_activity_conversion_cost_based_metric_label" AS "cost_based_metric",
  'CM360' AS "data_source",
  'campaign_manager_paid_search_external_report' AS "data_source_details",
  "date",
  "conversions"
FROM
  INTEGRATED_TAXONOMY_AD_LEVEL AS T
  JOIN CM360_MAPPINGS AS M ON T."journey_id" = M."cm360_journey_id"
  AND T."channel_name" = M."cm360_campaign_channel_name"
  AND T."platform_name" = M."cm360_site_platform_name"
  JOIN CONVERSIONS_PAID_SEARCH_REPORTS AS R ON T."platform_campaign_external_id" = R."paid_search_external_campaign_id"
  AND T."platform_campaign_external_group_id" = R."paid_search_external_ad_group_id"
  AND T."ad_asset_external_id" = R."paid_search_external_ad_id"
  AND M."cm360_activity_external_id" = R."activity_id"
WHERE
  R."date" BETWEEN T."media_row_date_from" AND T."media_row_date_to"