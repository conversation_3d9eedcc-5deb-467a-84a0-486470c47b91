WITH

    adform_reports AS (
        {{
            group_performance_data(
                filter_active_records('fact_performance_data_adform_core'),
                group_columns=[
                    "fact_date",
                    "external_campaign_id",
                    "advertiser_id",
                    "external_tag_id"
                ],
                fact_columns=[
                    "IMPRESSIONS",
                    "CLICKS",
                    "COMPLETED_VIEWS_FULL",
                    "COMPLETED_VIEWS_FIRST",
                    "COMPLETED_VIEWS_MID",
                    "COMPLETED_VIEWS_THIRD",
                    "VIDEO_VIEWS",
                    "VIDEO_START"
                ],
                where_clause='WHERE external_tracking_filter_id = 0'
            )
        }}
    ),

    adform_mappings AS (
        {{ adform_mappings() }}
    ),

    virtual_mappings as (
        {{ mint_virtual_taxonomy_query() }}
    ),

    adform_campaign_base_reports as (
        SELECT
            vm.company_id,
            vm.journey_id,
            vm.journey_name,
            vm.media_plan_id,
            vm.media_plan_name,
            vm.channel_id,
            vm.channel_name,
            vm.platform_id,
            vm.platform_name,
            vm.media_row_id,
            vm.media_row_name,
            vm.tile_id,
            vm.tile_name,
            vm.pricing_item_id AS platform_campaign_id,
            vm.pricing_item_name AS platform_campaign_name,
            NULL AS platform_campaign_internal_group_id,
            NULL AS platform_campaign_external_group_id,
            NULL AS platform_campaign_group_name,
            NULL AS ad_asset_internal_id,
            NULL AS ad_asset_external_id,
            NULL AS ad_asset,
            amap.adform_advertiser_id AS adserver_advertiser_id,
            amap.adform_campaign_external_id AS adserver_level_1_id,
            amap.adform_campaign_name AS adserver_level_1_name,
            amap.adform_media_external_id AS adserver_level_2_id,
            amap.adform_media_name AS adserver_level_2_name,
            amap.adform_line_item_external_id AS adserver_level_3_id,
            amap.adform_line_item_name AS adserver_level_3_name,
            amap.adform_banner_external_id AS adserver_level_4_id,
            amap.adform_banner_name AS adserver_level_4_name,
            amap.adform_tag_external_id AS adserver_level_5_id,
            arep.fact_date AS date,
            arep.IMPRESSIONS AS impressions,
            arep.CLICKS AS clicks,
            CAST(
            CASE
            WHEN vm.pricing_item_type = 'cpc' THEN arep.CLICKS * vm.price
            WHEN vm.pricing_item_type = 'cpm' THEN (arep.IMPRESSIONS / 1000.0) * vm.price
            WHEN vm.pricing_item_type = 'cpv' THEN arep.COMPLETED_VIEWS_FULL * vm.price
            ELSE NULL
            END AS NUMERIC(38, 10)
            ) AS spend,
            arep.COMPLETED_VIEWS_FULL AS completed_views_full,
            arep.COMPLETED_VIEWS_FIRST AS completed_views_first,
            arep.COMPLETED_VIEWS_MID AS completed_views_mid,
            arep.COMPLETED_VIEWS_THIRD AS completed_views_third,
            arep.VIDEO_VIEWS AS video_views,
            arep.VIDEO_START AS video_start
        FROM
        virtual_mappings vm
            JOIN adform_mappings amap 
            ON vm.pricing_item_id = amap.adform_platform_item_id
            AND vm.pricing_item_id = amap.adform_platform_item_external_id
            JOIN adform_reports arep ON amap.adform_advertiser_id = arep.advertiser_id
                AND amap.adform_campaign_external_id = arep.external_campaign_id
                AND amap.adform_tag_external_id = arep.external_tag_id
        WHERE arep.fact_date BETWEEN vm.media_row_start_date AND vm.media_row_end_date
    )

    select * from adform_campaign_base_reports