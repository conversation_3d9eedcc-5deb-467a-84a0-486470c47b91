with
mint_taxonomy as (
    {{ mint_taxonomy_query() }}
),

dv360_taxonomy as
(
    select * from
    ({{ package_taxonomy_query(constants("DV360")) }})
    where external_ad_id is not null
),

adform_reports AS (
        {{
            group_performance_data(
                filter_active_records('fact_performance_data_adform_core'),
                group_columns=[
                    "fact_date",
                    "external_campaign_id",
                    "advertiser_id",
                    "external_tracking_filter_id",
                    "external_tag_id"
                ],
                fact_columns=[
                    "CONVERSIONS",
                    "POST_IMPRESSIONS",
                    "POST_CLICKS"
                ], 
                where_clause='WHERE external_tracking_filter_id != 0'
            )
        }}
    ),

adform_mappings AS (
        {{ adform_mappings() }}
    ),

adform_dv360_youtube_report as (

    select
        tx.company_id,
        tx.journey_id,
        tx.journey_name,
        tx.media_plan_id,
        tx.media_plan_name,
        tx.channel_id,
        tx.channel_name,
        tx.platform_id,
        tx.platform_name,
        tx.media_row_id,
        tx.media_row_name,
        tx.tile_id,
        tx.tile_name,
        amap.adform_advertiser_id AS adserver_advertiser_id,
        amap.adform_campaign_external_id AS adserver_level_1_id,
        amap.adform_campaign_name AS adserver_level_1_name,
        amap.adform_media_external_id AS adserver_level_2_id,
        amap.adform_media_name AS adserver_level_2_name,
        amap.adform_line_item_external_id AS adserver_level_3_id,
        amap.adform_line_item_name AS adserver_level_3_name,
        amap.adform_banner_external_id AS adserver_level_4_id,
        amap.adform_banner_name AS adserver_level_4_name,
        amap.adform_tag_external_id AS adserver_level_5_id,
        amap.adform_tracker_external_id AS adserver_level_6_id,
        amap.adform_tracker_name AS adserver_level_6_name,
        tax.campaign_currency as currency,
        amap.adform_tracker_conversion_metric_label AS conversion_metric,
        amap.adform_tracker_conversion_cost_based_metric_label AS cost_based_metric,
        '{{ constants("ADFORM") }}' AS data_source,
        '{{ constants("ADFORM_PERFORMANCE_REPORTS_TABLE") }}' AS data_source_details,
        arep.fact_date AS date,
        arep.CONVERSIONS AS conversions,
        arep.POST_IMPRESSIONS AS view_through_conversions,
        arep.POST_CLICKS AS click_through_conversions
        FROM mint_taxonomy tx
        JOIN dv360_taxonomy tax
          ON tx.tile_id = tax.tile_id
        JOIN adform_mappings amap
          ON tax.internal_ad_id = amap.adform_platform_item_id
          and tax.external_ad_id = amap.adform_platform_item_external_id
        JOIN adform_reports arep
          ON amap.adform_advertiser_id = arep.advertiser_id
         AND amap.adform_campaign_external_id = arep.external_campaign_id
         AND amap.adform_tracker_external_id = arep.external_tracking_filter_id
        WHERE arep.fact_date BETWEEN tx.media_row_start_date AND tx.media_row_end_date
)

select * from adform_dv360_youtube_report