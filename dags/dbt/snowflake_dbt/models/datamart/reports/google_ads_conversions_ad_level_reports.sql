{{ config(materialized='view') }}

with
  -- Reuse your unified taxonomy; this macro returns the full hierarchy (campaign, media row, etc.)
  mint_taxonomy as (
    {{ mint_taxonomy_query() }}
  ),
  -- Split the Google Ads taxonomy into two branches:
  adwords_ad_level as (
    select DISTINCT
        tile_id,
        external_campaign_id,
        campaign_name,
        external_ad_group_id,
        ad_group_name,
        external_ad_id,
        ad_name,
        campaign_currency
    from ({{ package_taxonomy_query( constants("GOOGLE_ADS") ) }})
    where external_ad_id is not null
  ),
  -- Unpivot and aggregate ad-level conversion performance from the flat fact table
  conversion_report_ad_level_raw as (
    select
      conv.*,
      dconv.conversion_label as conversion_metric,
    from {{ filter_active_records( 'fact_performance_data_core', 'reports_adwords_ad_conversion_performance') }} conv
    join {{ filter_active_records( 'dim_conversion_core', constants("GOOGLE_ADS")) }} dconv
        on  conv.conversion_identifier = dconv.conversion_identifier
  ),
    conversion_report_ad_level as ({{ group_performance_data('conversion_report_ad_level_raw',
                                  group_columns=[
                                    'fact_date',
                                    'external_campaign_id',
                                    'external_ad_group_id',
                                    'external_ad_id',
                                    'conversion_metric',
                                    ],
                                  fact_columns=[
                                    'ALL_CONVERSIONS',
                                    ])
        }}),
  -- Build the ad-level conversions branch by joining taxonomy, ad-level taxonomy, and ad-level conversion performance
  adwords_conversions_ad_level as (
    select
      tx.company_id,
      tx.journey_id,
      tx.journey_name,
      tx.media_plan_id,
      tx.media_plan_name,
      tx.channel_id,
      tx.channel_name,
      tx.platform_id,
      tx.platform_name,
      tx.media_row_id,
      tx.media_row_name,
      tx.tile_id,
      tx.tile_name,
      adw_ad.external_campaign_id as platform_campaign_id,
      adw_ad.campaign_name as platform_campaign_name,
      adw_ad.external_ad_group_id as platform_campaign_group_id,
      adw_ad.ad_group_name as platform_campaign_group_name,
      adw_ad.external_ad_id as ad_asset_id,
      adw_ad.ad_name as ad_asset,
      coalesce(adw_ad.campaign_currency, tx.mediarow_currency) as currency,
      conv.fact_date as date,
      conv.conversion_metric,
      conv.all_conversions as conversions,
      '{{ constants("GOOGLE_ADS") }}' as data_source,
      'reports_adwords_ad_conversion_performance,reports_adwords_action_mapping' as data_source_details
    from mint_taxonomy tx
    join adwords_ad_level adw_ad
      on tx.tile_id = adw_ad.tile_id
    join conversion_report_ad_level conv
      on adw_ad.external_campaign_id = conv.external_campaign_id
         and adw_ad.external_ad_group_id = conv.external_ad_group_id
         and adw_ad.external_ad_id = conv.external_ad_id
    where conv.fact_date between tx.media_row_start_date and tx.media_row_end_date
  )

select * from adwords_conversions_ad_level