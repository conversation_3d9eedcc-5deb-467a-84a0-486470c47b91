{{ config(materialized='view') }}

with
    -- Reuse your unified taxonomy; this macro returns the full hierarchy (campaign, media row, etc.)
    mint_taxonomy as (
        {{ mint_taxonomy_query() }}
    ),
    -- Split the Meta taxonomy into two branches:
    meta_ad_level as (
        select DISTINCT
            tile_id,
            external_campaign_id,
            campaign_name,
            external_ad_group_id,
            ad_group_name,
            external_ad_id,
            ad_name,
            campaign_currency
        from ({{ package_taxonomy_query( constants("META") ) }})
        where external_ad_id is not null
    ),
    -- Unpivot and aggregate ad-level conversion performance from the flat fact table
    meta_base_reports as (
        {{ group_performance_data( filter_active_records('fact_performance_data_core', constants("META")),
            group_columns=['fact_date',
                        'external_campaign_id',
                        'external_ad_group_id',
                        'external_ad_id',
                        'currency_code'],
            fact_columns=[
                "IMPRESSIONS",
                "CLICKS",
                "CLICKS_ALL",
                "SPEND",
                "COMPLETED_VIEWS_FIRST",
                "COMPLETED_VIEWS_MID",
                "COMPLETED_VIEWS_THIRD",
                "COMPLETED_VIEWS_FULL",
                "THRUPLAYS",
                "VIDEO_START",
                "VIDEO_VIEWS",
                "LANDING_PAGE_VIEWS",
                "PAGE_LIKES",
                "PLATFORM_CONVERSIONS",
                "POST_COMMENTS",
                "POST_ENGAGEMENT",
                "POST_REACTION",
                "POST_SAVES"
            ],
            where_clause='WHERE conversion_identifier IS NULL'
        )
        }}
    ),
    -- Build the ad-level conversions branch by joining taxonomy, ad-level taxonomy, and ad-level conversion performance
    meta_base_ad_level_report as (
        select
            tx.company_id,
            tx.journey_id,
            tx.journey_name,
            tx.media_plan_id,
            tx.media_plan_name,
            tx.channel_id,
            tx.channel_name,
            tx.platform_id,
            tx.platform_name,
            tx.media_row_id,
            tx.media_row_name,
            tx.tile_id,
            tx.tile_name,
            mt_ad.external_campaign_id as platform_campaign_id,
            mt_ad.campaign_name as platform_campaign_name,
            mt_ad.external_ad_group_id as platform_campaign_group_id,
            mt_ad.ad_group_name as platform_campaign_group_name,
            mt_ad.external_ad_id as ad_asset_id,
            mt_ad.ad_name as ad_asset,
            COALESCE(mt_rep.currency_code, mt_ad.campaign_currency, tx.mediarow_currency) as currency,
            '{{ constants("META") }}' as data_source,
            'social_campaigns_reports_v3' as data_source_details,
            mt_rep.fact_date as date,
            mt_rep.impressions,
            mt_rep.clicks,
            mt_rep.clicks_all,
            mt_rep.spend,
            mt_rep.completed_views_first,
            mt_rep.completed_views_mid,
            mt_rep.completed_views_third,
            mt_rep.completed_views_full,
            mt_rep.thruplays,
            mt_rep.video_start,
            mt_rep.video_views,
            mt_rep.landing_page_views,
            mt_rep.page_likes,
            mt_rep.platform_conversions,
            mt_rep.post_comments,
            mt_rep.post_engagement,
            mt_rep.post_reaction,
            mt_rep.post_saves
        from mint_taxonomy tx
        join meta_ad_level as mt_ad
            on tx.tile_id = mt_ad.tile_id
        join meta_base_reports as mt_rep
            on mt_ad.external_campaign_id = mt_rep.external_campaign_id
                and mt_ad.external_ad_group_id = mt_rep.external_ad_group_id
                and mt_ad.external_ad_id = mt_rep.external_ad_id
        where mt_rep.fact_date between tx.media_row_start_date and tx.media_row_end_date
    )

select * from meta_base_ad_level_report