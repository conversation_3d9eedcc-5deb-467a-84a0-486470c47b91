{{ config(materialized='view') }}

WITH
    -- Reuse your unified taxonomy; this macro returns the full hierarchy (campaign, media row, etc.)
    mint_taxonomy AS (
        {{ mint_taxonomy_query() }}
    ),
    -- Split the Microsoft Ads taxonomy into two branches:
    microsoft_ads_ad_level AS (
        SELECT DISTINCT
            tile_id,
            campaign_currency,
            external_campaign_id,
            campaign_name,
            external_ad_group_id,
            ad_group_name,
            external_ad_id,
            ad_name
        FROM ({{ package_taxonomy_query( constants("MICROSOFT_ADS") ) }})
        WHERE external_ad_id IS NOT NULL
    ),
    -- Unpivot and aggregate ad-level conversion performance from the flat fact table
    microsoft_ads_base_reports AS (
        {{ group_performance_data(filter_active_records('fact_performance_data_core', constants("MICROSOFT_ADS")) ,
        group_columns=['fact_date',
                        'external_campaign_id',
                        'external_ad_group_id',
                        'external_ad_id',
                        'currency_code'],
            fact_columns=[
                "IMPRESSIONS",
                "CLICKS",
                "SPEND",
                "PLATFORM_CONVERSIONS",
            ]
        )
        }}
    ),
    -- Build the ad-level conversions branch by joining taxonomy, ad-level taxonomy, and ad-level conversion performance
    microsoft_ads_ad_level_report AS (
        SELECT
            tx.company_id,
            tx.journey_id,
            tx.journey_name,
            tx.media_plan_id,
            tx.media_plan_name,
            tx.channel_id,
            tx.channel_name,
            tx.platform_id,
            tx.platform_name,
            tx.media_row_id,
            tx.media_row_name,
            tx.tile_id,
            tx.tile_name,
            ms_ad.external_campaign_id AS platform_campaign_id,
            ms_ad.campaign_name AS platform_campaign_name,
            ms_ad.external_ad_group_id AS platform_campaign_group_id,
            ms_ad.ad_group_name AS platform_campaign_group_name,
            ms_ad.external_ad_id AS ad_asset_id,
            ms_ad.ad_name AS ad_asset,
            COALESCE(ms_rep.currency_code, ms_ad.campaign_currency, tx.mediarow_currency) AS currency,
            '{{ constants("MICROSOFT_ADS") }}' AS data_source,
            'microsoft_advertising_performance_ad_level' AS data_source_details,
            ms_rep.fact_date AS date,
            ms_rep.impressions,
            ms_rep.clicks,
            ms_rep.spend,
            ms_rep.platform_conversions
        FROM mint_taxonomy tx
        JOIN microsoft_ads_ad_level AS ms_ad
            ON tx.tile_id = ms_ad.tile_id
        JOIN microsoft_ads_base_reports AS ms_rep
            ON ms_ad.external_campaign_id = ms_rep.external_campaign_id
                AND ms_ad.external_ad_group_id = ms_rep.external_ad_group_id
                AND ms_ad.external_ad_id = ms_rep.external_ad_id
        WHERE ms_rep.fact_date BETWEEN tx.media_row_start_date AND tx.media_row_end_date
    )

SELECT  * FROM microsoft_ads_ad_level_report