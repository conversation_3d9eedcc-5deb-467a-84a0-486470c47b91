{{ config(materialized='view') }}

with
  mint_taxonomy as ({{ mint_taxonomy_query() }}
  ),
  linkedin_ad_level as (
    select distinct
      tile_id,
      external_campaign_id,
      external_ad_group_id,
      external_ad_id,
      internal_ad_id,
      campaign_name,
      ad_group_name,
      ad_name,
      campaign_currency
    from ({{ package_taxonomy_query(constants("LINKEDIN")) }})
  ),
  linkedin_base_reports as (
    (
        {{ group_performance_data(filter_active_records('fact_performance_data_core',constants("LINKEDIN")) ,
        group_columns=['fact_date',
                        'external_campaign_id',
                        'external_ad_group_id',
                        'external_ad_id',
                        'currency_code'],
            fact_columns=[
                  'IMPRESSIONS',
                  'CLICKS',
                  'SPEND',
                  'VIDEO_START',
                  'VIDEO_VIEWS',
                  'COMPLETED_VIEWS_FIRST',
                  'COMPLETED_VIEWS_MID',
                  'COMPLETED_VIEWS_THIRD',
                  'COMPLETED_VIEWS_FULL',
                  'PLATFORM_CONVERSIONS',
            ]
        )
        }}
    )
  )

select
  tx.company_id,
  tx.journey_id,
  tx.journey_name,
  tx.media_plan_id,
  tx.media_plan_name,
  tx.channel_id,
  tx.channel_name,
  tx.platform_id,
  tx.platform_name, 
  tx.media_row_id,
  tx.media_row_name,
  tx.tile_id,
  tx.tile_name,
  am_ad.external_campaign_id as platform_campaign_id,
  am_ad.campaign_name as platform_campaign_name,
  am_ad.external_ad_group_id as platform_campaign_group_id,
  am_ad.ad_group_name as platform_campaign_group_name,
  am_ad.external_ad_id as ad_asset_id,
  am_ad.ad_name as ad_asset,
  COALESCE(campaign_currency, mediarow_currency) as currency,
  am_rep.fact_date as date,
  impressions,
  clicks,
  spend,
  video_start,
  video_views,
  completed_views_first,
  completed_views_mid,
  completed_views_third,
  completed_views_full,
  platform_conversions,
  '{{ constants("LINKEDIN") }}' as data_source,
  '{{ constants("LINKEDIN_AD_LEVEL_PERFORMANCE_REPORTS_TABLE") }}' as data_source_details
from mint_taxonomy tx
join linkedin_ad_level am_ad
  on tx.tile_id = am_ad.tile_id
join linkedin_base_reports am_rep
  on am_ad.external_campaign_id = am_rep.external_campaign_id
    and am_ad.external_ad_group_id = am_rep.external_ad_group_id
    and am_ad.external_ad_id = am_rep.external_ad_id
where am_rep.fact_date between tx.media_row_start_date and tx.media_row_end_date
