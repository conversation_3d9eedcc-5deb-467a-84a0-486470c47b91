{{ config(materialized='view') }}

WITH
    -- Reuse your unified taxonomy; this macro returns the full hierarchy (campaign, media row, etc.)
    mint_taxonomy AS (
        {{ mint_taxonomy_query() }}
    ),
    -- Split the Microsoft Ads taxonomy into two branches:
    microsoft_ads_campaign_level AS (
        SELECT DISTINCT
            tile_id,
            campaign_currency,
            external_campaign_id,
            campaign_name
        FROM ({{ package_taxonomy_query( constants("MICROSOFT_ADS") ) }})
        WHERE external_ad_id IS NULL
            AND external_ad_group_id IS NULL
    ),
    microsoft_ads_base_reports AS (
        SELECT
            fact_date,
            external_campaign_id,
            SUM(CASE WHEN fact_name = 'IMPRESSIONS' THEN fact_value ELSE NULL END) AS impressions,
            SUM(CASE WHEN fact_name = 'CLICKS' THEN fact_value ELSE NULL END) AS clicks,
            SUM(CASE WHEN fact_name = 'SPEND' THEN fact_value ELSE NULL END) AS spend,
            SUM(CASE WHEN fact_name = 'PLATFORM_CONVERSIONS' THEN fact_value ELSE NULL END) AS platform_conversions
        FROM {{ filter_active_records('fact_performance_data_core', constants("MICROSOFT_ADS")) }} conv
        WHERE external_ad_id IS NULL
            AND external_ad_group_id IS NULL
        GROUP BY conv.fact_date, conv.external_campaign_id
    ),
    microsoft_ads_campaign_level_reports AS (
        SELECT
            tx.company_id,
            tx.journey_id,
            tx.journey_name,
            tx.media_plan_id,
            tx.media_plan_name,
            tx.channel_id,
            tx.channel_name,
            tx.platform_id,
            tx.platform_name,
            tx.media_row_id,
            tx.media_row_name,
            tx.tile_id,
            tx.tile_name,
            ms_camp.external_campaign_id AS platform_campaign_id,
            ms_camp.campaign_name AS platform_campaign_name,
            COALESCE(ms_camp.campaign_currency, tx.mediarow_currency) AS currency,
            ms_rep.fact_date AS date,
            ms_rep.impressions,
            ms_rep.clicks,
            ms_rep.spend,
            ms_rep.platform_conversions,
            '{{ constants("MICROSOFT_ADS") }}' AS data_source,
            'microsoft_advertising_estimated_campaign_level_v2' AS data_source_details
        FROM mint_taxonomy tx
        JOIN microsoft_ads_campaign_level AS ms_camp
            ON tx.tile_id = ms_camp.tile_id
        JOIN microsoft_ads_base_reports AS ms_rep
            ON ms_camp.external_campaign_id = ms_rep.external_campaign_id
        WHERE ms_rep.fact_date BETWEEN tx.media_row_start_date AND tx.media_row_end_date
    )

SELECT * FROM microsoft_ads_campaign_level_reports