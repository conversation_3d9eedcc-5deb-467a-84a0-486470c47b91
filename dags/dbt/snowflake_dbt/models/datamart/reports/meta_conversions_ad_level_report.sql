{{ config(materialized='view') }}

with
    -- Reuse your unified taxonomy; this macro returns the full hierarchy (campaign, media row, etc.)
    mint_taxonomy as (
        {{ mint_taxonomy_query() }}
    ),
    -- Split the Meta taxonomy into two branches:
    meta_ad_level as (
        select DISTINCT
            tile_id,
            external_campaign_id,
            campaign_name,
            external_ad_group_id,
            ad_group_name,
            external_ad_id,
            ad_name,
            campaign_currency
        from ({{ package_taxonomy_query( constants("META") ) }})
        where external_ad_id is not null
    ),

    meta_custom_conversions as (
        SELECT
            fact_date,
            external_campaign_id,
            external_ad_group_id,
            external_ad_id,
            currency_code,
            conversion_label,
            SUM(CASE WHEN fact_name = 'CONVERSIONS' THEN fact_value ELSE NULL END) AS conversions,
            SUM(CASE WHEN fact_name = 'REVENUE' THEN fact_value ELSE NULL END) AS conversions_revenue,
        FROM {{ filter_active_records('fact_performance_data_core', constants("META") ) }} fact
        JOIN {{ filter_active_records('dim_conversion_core', 'meta_conversions_report') }} conv
            ON fact.conversion_identifier = conv.conversion_identifier
        WHERE fact.conversion_identifier LIKE '%.custom.%'
        GROUP BY
            fact_date,
            external_campaign_id,
            external_ad_group_id,
            external_ad_id,
            currency_code,
            conversion_label
    ),

    meta_default_conversions as (
        SELECT
            fact_date,
            external_campaign_id,
            external_ad_group_id,
            external_ad_id,
            currency_code,
            conv.conversion_label,
            SUM(CASE WHEN fact_name = 'CONVERSIONS' THEN fact_value ELSE NULL END) AS conversions,
            SUM(CASE WHEN fact_name = 'REVENUE' THEN fact_value ELSE NULL END) AS conversions_revenue,
        FROM {{ filter_active_records('fact_performance_data_core', constants("META") ) }} fact
        JOIN {{ filter_active_records('dim_conversion_core', 'meta_default_conversions_mapping') }} conv
            ON fact.conversion_identifier = conv.conversion_identifier
        GROUP BY
            fact_date,
            external_campaign_id,
            external_ad_group_id,
            external_ad_id,
            currency_code,
            conv.conversion_label
    ),

    -- Unpivot and aggregate ad-level conversion performance from the flat fact table
    meta_conversions_reports as (
        SELECT * FROM meta_custom_conversions
        UNION ALL
        SELECT * FROM meta_default_conversions
    ),

    meta_conversions_ad_level_report as (
        select
            tx.company_id,
            tx.journey_id,
            tx.journey_name,
            tx.media_plan_id,
            tx.media_plan_name,
            tx.channel_id,
            tx.channel_name,
            tx.platform_id,
            tx.platform_name,
            tx.media_row_id,
            tx.media_row_name,
            tx.tile_id,
            tx.tile_name,
            mt_ad.external_campaign_id as platform_campaign_id,
            mt_ad.campaign_name as platform_campaign_name,
            mt_ad.external_ad_group_id as platform_campaign_group_id,
            mt_ad.ad_group_name as platform_campaign_group_name,
            mt_ad.external_ad_id as ad_asset_id,
            mt_ad.ad_name as ad_asset,
            COALESCE(mt_ad.campaign_currency, tx.mediarow_currency) as currency,
            '{{ constants("META") }}' as data_source,
            'meta_conversions_report' as data_source_details,
            mt_c_rep.fact_date as date,
            conversion_label as conversion_metric,
            conversions,
            conversions_revenue
        from
            mint_taxonomy as tx
        join meta_ad_level AS mt_ad
            on tx.tile_id = mt_ad.tile_id
        join meta_conversions_reports as mt_c_rep
            on mt_ad.external_campaign_id = mt_c_rep.external_campaign_id
            and mt_ad.external_ad_group_id = mt_c_rep.external_ad_group_id
            and mt_ad.external_ad_id = mt_c_rep.external_ad_id
        where
            mt_c_rep.fact_date between tx.media_row_start_date and tx.media_row_end_date
    )

select * from meta_conversions_ad_level_report