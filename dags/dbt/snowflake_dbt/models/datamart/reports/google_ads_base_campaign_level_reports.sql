{{ config(materialized='view') }}

with
  mint_taxonomy as ({{ mint_taxonomy_query() }}
  ),
  adwords_camp_level as (
    select distinct
      tile_id,
      external_campaign_id,
      campaign_name,
      campaign_currency
    from ({{ package_taxonomy_query( constants("GOOGLE_ADS") ) }})
    where external_ad_id is null
      and external_ad_group_id is null
  ),
  adwords_base_reports as (
        {{ group_performance_data(filter_active_records('fact_performance_data_core', 'search_campaign_performance'),
                                  group_columns=[
                                    'fact_date',
                                    'external_campaign_id',
                                    ],
                                  fact_columns=[
                                    'IMPRESSIONS',
                                    'CLICKS',
                                    'BUDGET_SPENT',
                                    'VIEWS_25P',
                                    'VIEWS_50P',
                                    'VIEWS_75P',
                                    'COMPLETE_VIEWS',
                                    'VIDEO_VIEWS',
                                    'ACTIVE_VIEW_IMPRESSIONS',
                                    'ALL_CONVERSIONS',
                                    ])
        }}
    )

select
  tx.company_id,
  tx.journey_id,
  tx.journey_name,
  tx.media_plan_id,
  tx.media_plan_name,
  tx.channel_id,
  tx.channel_name,
  tx.platform_id,
  tx.platform_name,
  tx.media_row_id,
  tx.media_row_name,
  tx.tile_id,
  tx.tile_name,
  adw_c.external_campaign_id as platform_campaign_id,
  adw_c.campaign_name as platform_campaign_name,
  COALESCE(campaign_currency, mediarow_currency) as currency,
  adw_rep.fact_date as date,
  impressions,
  clicks,
  budget_spent as spend,
  all_conversions as platform_conversions,
  active_view_impressions as video_start,
  video_views,
  views_25p as completed_views_first,
  views_50p as completed_views_mid,
  views_75p as completed_views_third,
  complete_views as completed_views_full,
  '{{ constants("GOOGLE_ADS") }}' as data_source,
  'search_campaign_performance' as data_source_details
from mint_taxonomy tx
join adwords_camp_level adw_c
  on tx.tile_id = adw_c.tile_id
join adwords_base_reports adw_rep
  on adw_c.external_campaign_id = adw_rep.external_campaign_id
where adw_rep.fact_date between tx.media_row_start_date and tx.media_row_end_date