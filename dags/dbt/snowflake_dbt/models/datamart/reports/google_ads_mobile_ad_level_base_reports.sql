{{ config(materialized='view') }}

with
  -- Unified taxonomy from your Core transform layer
  mint_taxonomy as (
    {{ mint_taxonomy_query() }}
  ),
  -- Ad-level Google Ads taxonomy (only rows with an external ad)
  adwords_ad_level as (
    select DISTINCT
        tile_id,
        external_campaign_id,
        campaign_name,
        external_ad_group_id,
        ad_group_name,
        external_ad_id,
        ad_name,
        campaign_currency
    from ({{ package_taxonomy_query( constants("GOOGLE_ADS") ) }})
    where external_ad_id is not null
  ),
  -- Aggregate base performance measures from the flat fact table
{#  adwords_base_reports_raw as (select * from ),#}
  adwords_base_reports as (
        {{ group_performance_data(filter_active_records('fact_performance_data_core', 'reports_adwords_ad_performance'),
                                  group_columns=[
                                    'fact_date',
                                    'external_campaign_id',
                                    'external_ad_group_id',
                                    'external_ad_id',
                                    'currency_code',
                                    'platform_name'
                                    ],
                                  fact_columns=[
                                    'IMPRESSIONS',
                                    'CLICKS',
                                    'BUDGET_SPENT',
                                    'VIEWS_25P',
                                    'VIEWS_50P',
                                    'VIEWS_75P',
                                    'COMPLETE_VIEWS',
                                    'VIDEO_VIEWS',
                                    'ACTIVE_VIEW_IMPRESSIONS',
                                    'ALL_CONVERSIONS',
                                    ])
        }}
  ),
  adwords_mobile_reports as (
        {{ group_performance_data(filter_active_records('fact_performance_data_core', 'search_campaign_performance_mobile'),
                                  group_columns=[
                                    'fact_date',
                                    'external_campaign_id',
                                    'external_ad_group_id',
                                    'external_ad_id',
                                    ],
                                  fact_columns=[
                                    'INSTALLATIONS',
                                    'IN_APP_ACTIONS'
                                    ])
        }}
  ),
  -- Combine the base and mobile measures into one performance report
  adwords_reports as (
    select
      abr.fact_date,
      abr.external_campaign_id,
      abr.external_ad_group_id,
      abr.external_ad_id,
      abr.currency_code,
      abr.impressions,
      abr.clicks,
      abr.budget_spent as spend,
      abr.all_conversions as platform_conversions,
      abr.active_view_impressions as video_start,
      abr.video_views,
      abr.views_25p as completed_views_first,
      abr.views_50p as completed_views_mid,
      abr.views_75p completed_views_third,
      abr.complete_views as completed_views_full,
      amr.installations as platform_installations,
      amr.in_app_actions,
      abr.platform_name
    from adwords_base_reports abr
    left join adwords_mobile_reports amr
      on abr.external_campaign_id = amr.external_campaign_id
      and abr.external_ad_group_id = amr.external_ad_group_id
      and abr.external_ad_id = amr.external_ad_id
      and abr.fact_date = amr.fact_date
  )

select
  tx.company_id,
  tx.journey_id,
  tx.journey_name,
  tx.media_plan_id,
  tx.media_plan_name,
  tx.channel_id,
  tx.channel_name,
  tx.platform_id,
  tx.platform_name,
  tx.media_row_id,
  tx.media_row_name,
  tx.tile_id,
  tx.tile_name,
  adw_ad.external_campaign_id as platform_campaign_id,
  adw_ad.campaign_name as platform_campaign_name,
  adw_ad.external_ad_group_id as platform_campaign_group_id,
  adw_ad.ad_group_name as platform_campaign_group_name,
  adw_ad.external_ad_id as ad_asset_id,
  adw_ad.ad_name as ad_asset,
  COALESCE(reports.currency_code, adw_ad.campaign_currency, tx.mediarow_currency) as currency,
  reports.fact_date as date,
  reports.impressions,
  reports.clicks,
  reports.spend,
  reports.platform_conversions,
  reports.video_start,
  reports.video_views,
  reports.completed_views_first,
  reports.completed_views_mid,
  reports.completed_views_third,
  reports.completed_views_full,
  reports.platform_installations,
  reports.in_app_actions,
    '{{ constants("GOOGLE_ADS") }}' as data_source,

  'reports_adwords_ad_performance, search_campaign_performance_mobile' as data_source_details
from mint_taxonomy tx
join adwords_ad_level adw_ad
  on tx.tile_id = adw_ad.tile_id
join adwords_reports reports
  on adw_ad.external_campaign_id = reports.external_campaign_id
     and adw_ad.external_ad_group_id = reports.external_ad_group_id
     and adw_ad.external_ad_id = reports.external_ad_id
where reports.fact_date between tx.media_row_start_date and tx.media_row_end_date