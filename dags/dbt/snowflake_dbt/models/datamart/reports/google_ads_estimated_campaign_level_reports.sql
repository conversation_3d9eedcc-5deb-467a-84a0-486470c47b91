{{ config(materialized='view') }}

with
  mint_taxonomy as (
    {{ mint_taxonomy_query() }}
  ),
  adwords_campaign_level as (
    select distinct
        tile_id,
        external_campaign_id,
        campaign_name,
        campaign_currency
    from ({{ package_taxonomy_query( constants("GOOGLE_ADS") ) }})
  ),
adwords_estimated_reports as (
        {{ group_performance_data(filter_active_records('fact_performance_data_core', 'search_campaign_performance'),
                                  group_columns=[
                                    'fact_date',
                                    'external_campaign_id',
                                    ],
                                  fact_columns=[
                                    'SEARCH_IMPRESSION_SHARE',
                                    'TOTAL_ELIGIBLE_IMPRESSIONS'
                                    ])
        }}
  ),
  google_ads_estimated_campaign_level_reports as (
    select
      tx.company_id,
      tx.journey_id,
      tx.journey_name,
      tx.media_plan_id,
      tx.media_plan_name,
      tx.channel_id,
      tx.channel_name,
      tx.platform_id,
      tx.platform_name,
      tx.media_row_id,
      tx.media_row_name,
      tx.tile_id,
      tx.tile_name,
      adw_c.external_campaign_id as platform_campaign_id,
      adw_c.campaign_name as platform_campaign_name,
      adw_est_rep.fact_date as date,
      coalesce(adw_c.campaign_currency, tx.mediarow_currency) as currency,
      adw_est_rep.search_impression_share,
      adw_est_rep.total_eligible_impressions,
          '{{ constants("GOOGLE_ADS") }}' as data_source,

      'search_campaign_performance' as data_source_details
    from mint_taxonomy tx
    join adwords_campaign_level adw_c
      on tx.tile_id = adw_c.tile_id
    join adwords_estimated_reports adw_est_rep
      on adw_c.external_campaign_id = adw_est_rep.external_campaign_id
    where adw_est_rep.fact_date between tx.media_row_start_date and tx.media_row_end_date
  )

select *
from google_ads_estimated_campaign_level_reports