{{ config(materialized='view') }}

WITH dv360_li_level AS (
    SELECT DISTINCT
      tile_id as dv360_tile_id,
      external_campaign_id as dv360_external_campaign_id,
      campaign_name as dv360_campaign_name,
      external_ad_group_id as dv360_external_adgroup_id,
      ad_group_name as dv360_adgroup_name,
      external_ad_id as dv360_external_ad_id,
      ad_name as dv360_ad_name,
      campaign_currency as dv360_currency_code
    FROM
      ({{ package_taxonomy_query(constants("DV360")) }})
    WHERE
      dv360_external_ad_id IS NOT NULL
  ),

dv360_base_reports AS (
    SELECT
        f.fact_date,
        f.external_campaign_id,
        f.external_ad_group_id,
        f.external_ad_id,
        f.external_creative_id,
        c.creative_name,
        f.currency_code,
        SUM(case when fact_name = 'IMPRESSIONS' then cast(fact_value as numeric(38,10)) else 0 end) AS impressions,
        SUM(case when fact_name = 'CLICKS' then cast(fact_value as numeric(38,10)) else 0 end) AS clicks,
        SUM(case when fact_name = 'BUDGET_SPENT' then cast(fact_value as numeric(38,10)) else 0 end) AS spend,
        SUM(case when fact_name = 'VIEWS_25P' then cast(fact_value as numeric(38,10)) else 0 end) AS completed_views_first,
        SUM(case when fact_name = 'VIEWS_50P' then cast(fact_value as numeric(38,10)) else 0 end) AS completed_views_mid,
        SUM(case when fact_name = 'VIEWS_75P' then cast(fact_value as numeric(38,10)) else 0 end) AS completed_views_third,
        SUM(case when fact_name = 'COMPLETE_VIEWS' then cast(fact_value as numeric(38,10)) else 0 end) AS completed_views_full,
        SUM(case when fact_name = 'TRUE_VIEWS' then cast(fact_value as numeric(38,10)) else 0 end) AS trueviews,
        SUM(case when fact_name = 'VIDEO_START' then cast(fact_value as numeric(38,10)) else 0 end) AS video_start,
        SUM(case when fact_name = 'VIDEO_VIEWS' then cast(fact_value as numeric(38,10)) else 0 end) AS video_views,
        SUM(case when fact_name = 'VIEWABLE_IMPRESSIONS' then cast(fact_value as numeric(38,10)) else 0 end) AS platform_active_view_viewable_impressions,
        SUM(case when fact_name = 'CONVERSIONS' then cast(fact_value as numeric(38,10)) else 0 end) AS platform_conversions

    FROM {{ filter_active_records('fact_performance_data_core', 'DV360') }} f
    left join {{ filter_active_records('dim_creative_core', 'DV360') }} c
        on c.external_creative_id = f.external_creative_id
        and c.external_ad_group_id = f.external_ad_group_id
        and c.external_ad_id = f.external_ad_id
        and c.external_campaign_id = f.external_campaign_id
    -- This filtering here ensures we're excluding youtube line items performance data to not take its duplicate alongside youtube reports
    WHERE f.external_creative_id != 'Unknown'
    GROUP BY
        f.fact_date,
        f.external_campaign_id,
        f.external_ad_group_id,
        f.external_ad_id,
        f.external_creative_id,
        c.creative_name,
        f.currency_code
)


SELECT
  tx.company_id,
  tx.journey_id,
  tx.journey_name,
  tx.media_plan_id,
  tx.media_plan_name,
  tx.channel_id,
  tx.channel_name,
  tx.platform_id,
  tx.platform_name,
  tx.media_row_id,
  tx.media_row_name,
  tx.tile_id,
  tx.tile_name,
  dv360_external_campaign_id AS platform_campaign_id,
  dv360_campaign_name AS platform_campaign_name,
  dv360_external_adgroup_id AS platform_campaign_group_id,
  dv360_adgroup_name AS platform_campaign_group_name,
  dv360_external_ad_id AS ad_asset_id,
  dv360_ad_name AS ad_asset,
  external_creative_id AS platform_creative_id,
  creative_name AS platform_creative_name,
  COALESCE(dv_rep.currency_code, dv_li.dv360_currency_code, tx.mediarow_currency) AS currency,
  fact_date as date,
  impressions,
  clicks,
  spend,
  completed_views_first,
  completed_views_mid,
  completed_views_third,
  completed_views_full,
  trueviews,
  video_start,
  video_views,
  platform_active_view_viewable_impressions,
  platform_conversions,
  '{{ constants("DV360") }}'  AS data_source,
  'dv360_base_reports' AS data_source_details
FROM
  ({{ mint_taxonomy_query() }}) AS tx
  JOIN dv360_li_level AS dv_li ON tx.tile_id = dv_li.dv360_tile_id
  JOIN dv360_base_reports as dv_rep ON dv_li.dv360_external_campaign_id = dv_rep.external_campaign_id
      AND dv_li.dv360_external_adgroup_id = dv_rep.external_ad_group_id
      AND dv_li.dv360_external_ad_id = dv_rep.external_ad_id
WHERE
  dv_rep.fact_date between tx.media_row_start_date and tx.media_row_end_date