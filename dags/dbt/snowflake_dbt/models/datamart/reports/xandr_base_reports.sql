{{ config(materialized='view') }}
{% set PACKAGE_NAME = constants('XANDR') %}
{% set PACKAGE_REPORTS_NAME = 'reports_xandr_performance_v3' %}
with
mint_taxonomy as (
    {{ mint_taxonomy_query() }}
  ),
xandr_ad_level as (
    select distinct
      tile_id,
      external_campaign_id,
      campaign_name,
      external_ad_group_id,
      ad_group_name,
      campaign_currency
    from ({{ package_taxonomy_query(constants("XANDR")) }})
    where external_ad_group_id is not null
  ),
xandr_base_reports_raw as (
    select
        facts.*,
        dcr.creative_name
    from {{ filter_active_records( 'fact_performance_data_core', PACKAGE_NAME) }} as facts
    left join {{ filter_active_records( 'dim_creative_core', PACKAGE_NAME) }} as dcr
        on dcr.external_creative_id = facts.external_creative_id
        and dcr.external_ad_group_id = facts.external_ad_group_id
        and dcr.external_ad_id = facts.external_ad_id
        and dcr.external_campaign_id = facts.external_campaign_id
),
        xandr_base_reports as (
        {{ group_performance_data('xandr_base_reports_raw',
                                  group_columns=[
                                    'fact_date',
                                    'external_campaign_id',
                                    'external_ad_group_id',
                                    'external_ad_id',
                                    'external_creative_id',
                                    'creative_name',
                                    'currency_code',
                                    'conversion_identifier'
                                    ],
                                  fact_columns=[
                                    'IMPRESSIONS',
                                    'CLICKS',
                                    'BUDGET_SPENT',
                                    'VIEWS_25P',
                                    'VIEWS_50P',
                                    'VIEWS_75P',
                                    'COMPLETE_VIEWS',
                                    'VIDEO_VIEWS',
                                    'ACTIVE_VIEW_IMPRESSIONS',
                                    'ALL_CONVERSIONS',
                                    ])
        }}

    )



SELECT
      tx.company_id,
      tx.journey_id,
      tx.journey_name,
      tx.media_plan_id,
      tx.media_plan_name,
      tx.channel_id,
      tx.channel_name,
      tx.platform_id,
      tx.platform_name,
      tx.media_row_id,
      tx.media_row_name,
      tx.tile_id,
      tx.tile_name,
      xan_ad.external_campaign_id AS platform_campaign_id,
      xan_ad.campaign_name AS platform_campaign_name,
      xan_ad.external_ad_group_id AS platform_campaign_group_id,
      xan_ad.ad_group_name AS platform_campaign_group_name,
      xan_rep.external_creative_id AS ad_asset_id,
      xan_rep.creative_name AS ad_asset,
      COALESCE(xan_rep.currency_code, xan_ad.campaign_currency, tx.mediarow_currency) AS currency,
      '{{ PACKAGE_NAME }}' AS data_source,
      '{{ PACKAGE_REPORTS_NAME }}' AS data_source_details,
      xan_rep.fact_date as date,
      xan_rep.impressions,
      xan_rep.clicks,
      xan_rep.budget_spent as spend,
      xan_rep.views_25p as completed_views_first,
      xan_rep.views_50p as completed_views_mid,
      xan_rep.views_75p as completed_views_third,
      xan_rep.complete_views as completed_views_full,
      xan_rep.active_view_impressions as video_start,
      xan_rep.all_conversions conversions,
      xan_rep.conversion_identifier AS conversion_metric
    FROM
      mint_taxonomy AS tx
      JOIN xandr_ad_level AS xan_ad ON tx.tile_id = xan_ad.tile_id
      JOIN xandr_base_reports as xan_rep ON xan_ad.external_campaign_id = xan_rep.external_ad_group_id
      AND xan_ad.external_ad_group_id = xan_rep.external_ad_id
    WHERE
      xan_rep.fact_date BETWEEN tx.media_row_start_date AND tx.media_row_end_date