{{ config(materialized='view') }}
{% set PACKAGE_REPORTS_NAME = 'reports_zemanta_performance_v2' %}

WITH
    -- Reuse your unified taxonomy; this macro returns the full hierarchy (campaign, media row, etc.)
    mint_taxonomy AS (
        {{ mint_taxonomy_query() }}
    ),
    -- Zemanta taxonomy ad_level
    zemanta_ad_level AS (
        SELECT DISTINCT
            tile_id,
            external_campaign_id,
            campaign_name,
            external_ad_group_id,
            ad_group_name,
            external_ad_id,
            ad_name,
            campaign_currency
        FROM ({{ package_taxonomy_query( constants("ZEMANTA") ) }})
        WHERE external_ad_id IS NOT NULL
    ),
    -- Zemanta reports table grouping
    zemanta_base_reports AS (
        {{ group_performance_data(filter_active_records('fact_performance_data_core', constants("ZEMANTA")),
        group_columns=['fact_date',
                        'external_campaign_id',
                        'external_ad_group_id',
                        'external_ad_id',
                        'currency_code'],
                                  fact_columns=[
                                    'IMPRESSIONS',
                                    'CLICKS',
                                    'SPEND',
                                    'COMPLETED_VIEWS_FIRST',
                                    'COMPLETED_VIEWS_MID',
                                    'COMPLETED_VIEWS_THIRD',
                                    'COMPLETED_VIEWS_FULL',
                                    'VIDEO_START'])
        }}

    ),
    -- Build the ad-level performance data joining taxonomy, ad-level taxonomy
    zemanta_base_ad_level_report AS (
        SELECT
            tx.company_id,
            tx.journey_id,
            tx.journey_name,
            tx.media_plan_id,
            tx.media_plan_name,
            tx.channel_id,
            tx.channel_name,
            tx.platform_id,
            tx.platform_name,
            tx.media_row_id,
            tx.media_row_name,
            tx.tile_id,
            tx.tile_name,
            zm_ad.external_campaign_id::bigint AS platform_campaign_id,
            zm_ad.campaign_name AS platform_campaign_name,
            zm_ad.external_ad_group_id::bigint AS platform_campaign_group_id,
            zm_ad.ad_group_name AS platform_campaign_group_name,
            zm_ad.external_ad_id::bigint AS ad_asset_id,
            zm_ad.ad_name AS ad_asset,
            COALESCE(zm_rep.currency_code, zm_ad.campaign_currency, tx.mediarow_currency) AS currency,
            '{{ constants("ZEMANTA") }}' AS data_source,
            '{{ PACKAGE_REPORTS_NAME }}' AS data_source_details,
            zm_rep.fact_date AS date,
            zm_rep.impressions,
            zm_rep.clicks,
            zm_rep.spend,
            zm_rep.completed_views_first,
            zm_rep.completed_views_mid,
            zm_rep.completed_views_third,
            zm_rep.completed_views_full,
            zm_rep.video_start
        FROM mint_taxonomy tx
        JOIN zemanta_ad_level AS zm_ad
            on tx.tile_id = zm_ad.tile_id
        JOIN zemanta_base_reports AS zm_rep
            ON zm_ad.external_campaign_id = zm_rep.external_campaign_id
                AND zm_ad.external_ad_group_id = zm_rep.external_ad_group_id
                AND zm_ad.external_ad_id = zm_rep.external_ad_id
        WHERE zm_rep.fact_date BETWEEN tx.media_row_start_date AND tx.media_row_end_date
    )

SELECT * FROM zemanta_base_ad_level_report
