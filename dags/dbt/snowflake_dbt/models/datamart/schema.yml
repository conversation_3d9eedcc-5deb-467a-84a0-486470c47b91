version: 2

models:
  - name: druid_v2_datamart
    description: "Datamart model combining all reports"
    tests:
      - compare_staging_datamart:
          compare_with: google_ads_final_view
          source_filter: "data_source = '{{ constants('GOOGLE_ADS') }}'"

      - compare_staging_datamart:
          compare_with: meta_final_view
          source_filter: "data_source = '{{ constants('META') }}'"

      - compare_staging_datamart:
          compare_with: dv360_final_view
          source_filter: "data_source in ('{{ constants('DV360') }}', 'Youtube (DV360)')"

      - compare_staging_datamart:
          compare_with: microsoft_ads_final_view
          source_filter: "data_source = '{{ constants('MICROSOFT_ADS') }}'"

      - compare_staging_datamart:
          compare_with: amazon_final_view
          source_filter: "data_source = '{{ constants('AMAZON') }}'"

      - compare_staging_datamart:
          compare_with: zemanta_final_view
          source_filter: "data_source = '{{ constants('ZEMANTA') }}'"

      - compare_staging_datamart:
          compare_with: xandr_final_view
          source_filter: "data_source = '{{ constants('XANDR') }}'"

      - compare_staging_datamart:
          compare_with: linkedin_final_view
          source_filter: "data_source = '{{ constants('LINKEDIN') }}'"