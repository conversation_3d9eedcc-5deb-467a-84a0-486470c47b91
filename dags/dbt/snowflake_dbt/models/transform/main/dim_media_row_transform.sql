SELECT DISTINCT
        mpi."id" AS media_row_id,
        mpi."name" AS media_row_name,
        mpi."campaign_id" as tile_id,
        mpi."fields":date_from::text AS media_row_start_date,
        mpi."fields":date_to::text AS media_row_end_date,
        mpi."overridden_currency" AS media_row_currency,
        mpi.run_id as load_id
       FROM {{ filtered_source('datintell', 'builder_mode_mediaplanitem') }} mpi
       WHERE mpi."type"='campaign'