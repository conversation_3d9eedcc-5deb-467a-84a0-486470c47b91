SELECT DISTINCT
        pi."id" AS "pricing_item_id",
        pi."date_from" AS "pricing_item_date_from",
        pi."date_to" AS "pricing_item_date_to",
        pi."pricing_type" AS "pricing_item_type",
        pi."name" AS "pricing_item_name",
        pi."user_defined_id" AS "pricing_item_user_defined_id",
        pi."campaign_id" AS "pricing_item_campaign_id",
        pi.run_id AS load_id
    FROM {{ filtered_source('DATINTELL', 'campaign_ghostcampaignpricingitem')}} pi