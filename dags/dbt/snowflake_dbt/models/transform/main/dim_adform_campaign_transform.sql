SELECT
    camp."id" as "internal_campaign_id",
    camp."campaign" as "external_campaign_id",
    camp."channel" as "channel_name",
    camp."settings_id" as "settings_id",
    camp."name" as "campaign_name",
    camp."taxonomy_internal_id" AS "platform_item_id",
    camp."platform" as "campaign_platform_name",
    camp.run_id as load_id,
    '{{ constants("ADFORM") }}' as "platform_name"
    FROM {{ filtered_source('ADFORM', 'adform_adformcampaignmapping') }} camp