WITH bridge_ad_ad_group AS (
    {% set ref_models = [
        'google_ads_bridge_ad_ad_group_transform',
        'xandr_bridge_ad_ad_group_transform',
        'meta_bridge_ad_ad_group_transform',
        'bing_bridge_ad_ad_group_transform',
        'zemanta_bridge_ad_ad_group_transform',
        'dv360_bridge_ad_ad_group_transform',
        'amazon_bridge_ad_ad_group_transform',
        'linkedin_bridge_ad_ad_group_transform',
    ] %}

    {% for table in ref_models %}
        SELECT * FROM {{ ref(table) }}
        {% if not loop.last %}
            UNION ALL
        {% endif %}
    {% endfor %}
)

SELECT * FROM bridge_ad_ad_group
