SELECT
    s."campaign_collection" AS "journey_id",
    s."integration_id",
    s."id" as "settings_id",
    i."advertiser" AS "advertiser_id",
    i."name",
    i."company_id",
    s.run_id as load_id,
    '{{ constants("ADFORM") }}' as "platform_name"
    FROM {{ filtered_source('ADFORM', 'adform_adformsettings') }} s
    JOIN {{ filtered_source('ADFORM', 'adform_adformintegration') }} i
        ON s."integration_id" = i."id"