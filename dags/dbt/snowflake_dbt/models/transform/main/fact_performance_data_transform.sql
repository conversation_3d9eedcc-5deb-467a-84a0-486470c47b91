WITH fact_performance_data AS (
    {% set ref_models = [
        'google_ads_fact_performance_data_transform',
        'xandr_fact_performance_data_transform',
        'meta_fact_performance_data_transform',
        'bing_fact_performance_data_transform',
        'zemanta_fact_performance_data_transform',
        'dv360_fact_performance_data_transform',
        'amazon_fact_performance_data_transform',
        'linkedin_fact_performance_data_transform',
    ] %}

    {% for table in ref_models %}
        SELECT * FROM {{ ref(table) }}
        {% if not loop.last %}
            UNION ALL
        {% endif %}
    {% endfor %}
)

SELECT * FROM fact_performance_data
