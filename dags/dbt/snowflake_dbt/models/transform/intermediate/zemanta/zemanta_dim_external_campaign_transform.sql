SELECT DISTINCT
        campaign."external_id"::bigint AS external_campaign_id,
        campaign."id" AS internal_campaign_id,
        campaign."name" AS campaign_name,
        intgr."currency_code" AS campaign_currency,
        campaign.run_id AS load_id,
        '{{ constants("ZEMANTA") }}' AS platform_name
    FROM {{ filtered_source('ZEMANTA', 'integrations')}} intgr
    JOIN {{ filtered_source('ZEMANTA', 'platform_settings')}} ps
        ON  ps."integration_id" = intgr."id"
    JOIN {{ filtered_source('ZEMANTA', 'campaigns')}} campaign
        ON campaign."platform_settings_id" = ps."tile_id"