{% set required_columns = get_druid_required_colunms() %}

WITH zemanta_performance_report AS (
    SELECT
        "date" AS fact_date,
        "campaign_id" AS external_campaign_id,
        "ad_group_id" AS external_ad_group_id,
        "content_ad_id" AS external_ad_id,
        CAST("impressions" AS NUMERIC(38, 10)) AS impressions,
        CAST("clicks" AS NUMERIC(38, 10)) AS clicks,
        CAST("total_spend" AS NUMERIC(38, 10)) AS spend,
        CAST("video_first_quartile" AS NUMERIC(38, 10)) AS completed_views_first,
        CAST("video_midpoint" AS NUMERIC(38, 10)) AS completed_views_mid,
        CAST("video_third_quartile" AS NUMERIC(38, 10)) AS completed_views_third,
        CAST("video_complete" AS NUMERIC(38, 10)) AS completed_views_full,
        CAST("video_start" AS NUMERIC(38, 10)) AS video_start,
        "currency" AS currency_code,
        zpr.run_id AS load_id,
        '{{ constants("ZEMANTA") }}' AS platform_name
    FROM
        {{ filtered_source('REPORTS', 'reports_zemanta_performance_v2') }} zpr
),

unpivoted_zemanta_performance_report AS (
{% set table_name = "zemanta_performance_report" %}
{{ unpivot_table(source_table_name=table_name,
    metric_names=["impressions", "clicks", "spend", "completed_views_first", "completed_views_mid",
    "completed_views_third", "completed_views_full", "video_start"],
    existing_columns=["fact_date", "external_campaign_id","external_ad_group_id", "external_ad_id", "load_id",
     "currency_code", "platform_name"],
    required_columns=required_columns) }}
)
Select * from unpivoted_zemanta_performance_report