SELECT DISTINCT
      asrc."id" AS internal_campaign_id,
      casc."external_core_campaign_id" AS tile_id,
      casc.run_id as load_id,
      '{{ constants("GOOGLE_ADS") }}' AS platform_name
    FROM {{ filtered_source('adwords', 'campaign_adwordssearchrelatedcampaign') }} as asrc
    JOIN {{ filtered_source('adwords', 'campaign_adwordssearchcampaign') }} as casc
      ON casc."id" = asrc."parent_campaign_id"
    JOIN {{ filtered_source('adwords', 'external_services_adwordsexternaldata') }} as gi
      ON gi."id" = casc."credentials_id"