{% set required_columns = get_druid_required_colunms() %}

WITH
    conversion_mapping AS (SELECT DISTINCT CAST("conversion_action_id" AS VARCHAR) as conversion_action_id,
                                           CAST("conversion_action_name" AS VARCHAR) as conversion_action_name

                           FROM
                               {{ filtered_source('reports', 'reports_adwords_action_mapping') }}
                           -- filtering that is applied here is to reduce the volume of data by taking only portion of conversions:
                           -- client can have thousands of conversion_actions and in reality needs 5-10 of them for reporting
                           -- in the future this filtering must become dynamic based on user input via metric manager
                           WHERE "include_in_conversions_metric" = True
                              OR "conversion_action_name" IN ('TYP - Start', 'TYP - Next', 'TYP - Premium')),

fact_adwords_campaign_performance AS (
    SELECT
        "date" as fact_date,
        "campaign_id" as external_campaign_id,
        CAST("search_impression_share" AS NUMERIC(38, 10)) AS search_impression_share,
        CAST("total_eligible_impressions" AS NUMERIC(38, 10)) AS total_eligible_impressions,
        CAST("impression" AS NUMERIC(38, 10)) AS impressions,
        CAST("clicks" AS NUMERIC(38, 10)) AS clicks,
        CAST("budget_spent" AS NUMERIC(38, 10)) AS budget_spent,
        CAST("views_25p" AS NUMERIC(38, 10)) AS views_25p,
        CAST("views_50p" AS NUMERIC(38, 10)) AS views_50p,
        CAST("views_75p" AS NUMERIC(38, 10)) AS views_75p,
        CAST("complete_views" AS NUMERIC(38, 10)) AS complete_views,
        CAST("video_views" AS NUMERIC(38, 10)) AS video_views,
        CAST("active_view_impressions" AS NUMERIC(38, 10)) AS active_view_impressions,
        CAST("all_conversions" AS NUMERIC(38, 10)) AS all_conversions,
        scp.run_id as load_id,
        'search_campaign_performance' as platform_name
    FROM
        {{ filtered_source('reports', 'search_campaign_performance') }} scp
),

unpivoted_adwords_campaign_performance AS (
    {% set table_name = "fact_adwords_campaign_performance" %}
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["search_impression_share", "total_eligible_impressions", "impressions",
            "clicks", "budget_spent", "views_25p", "views_50p", "views_75p", "complete_views",
            "video_views", "active_view_impressions", "all_conversions"],
        existing_columns=["fact_date", "external_campaign_id", "load_id", "platform_name"],
        required_columns=required_columns) }}
),
fact_adwords_ad_mobile_performance AS (
    SELECT
        "date" as fact_date,
        "campaign_id" as external_campaign_id,
        "ad_group_id" as external_ad_group_id,
        "ad_id" as external_ad_id,
        CAST("installations" AS NUMERIC(38, 10)) AS installations,
        CAST("in_app_actions" AS NUMERIC(38, 10)) AS in_app_actions,
        scpm.run_id as load_id,
        'search_campaign_performance_mobile' as platform_name
    FROM
        {{ filtered_source('reports', 'search_campaign_performance_mobile') }} scpm
),

unpivoted_adwords_ad_mobile_performance AS (
    {% set table_name = "fact_adwords_ad_mobile_performance" %}
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["installations", "in_app_actions"],
        existing_columns=["fact_date", "external_campaign_id","external_ad_group_id", "external_ad_id", "load_id", "platform_name"],
        required_columns=required_columns) }}
),
fact_adwords_ad_level_performance AS (
    SELECT
        "date" as fact_date,
        "campaign_id" as external_campaign_id,
        "adgroup_id" as external_ad_group_id,
        "ad_id" as external_ad_id,
        "currency_code" as currency_code,
        CAST("impression" AS NUMERIC(38, 10)) AS impressions,
        CAST("clicks" AS NUMERIC(38, 10)) AS clicks,
        CAST("budget_spent" AS NUMERIC(38, 10)) AS budget_spent,
        CAST("views_25p" AS NUMERIC(38, 10)) AS views_25p,
        CAST("views_50p" AS NUMERIC(38, 10)) AS views_50p,
        CAST("views_75p" AS NUMERIC(38, 10)) AS views_75p,
        CAST("complete_views" AS NUMERIC(38, 10)) AS complete_views,
        CAST("video_views" AS NUMERIC(38, 10)) AS video_views,
        CAST("active_view_impressions" AS NUMERIC(38, 10)) AS active_view_impressions,
        CAST("all_conversions" AS NUMERIC(38, 10)) AS all_conversions,
        raap.run_id as load_id,
        'reports_adwords_ad_performance' as platform_name
    FROM
        {{ filtered_source('reports', 'reports_adwords_ad_performance') }} raap
),

unpivoted_adwords_ad_level_performance AS (
    {% set table_name = "fact_adwords_ad_level_performance" %}
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["impressions", "clicks", "budget_spent", "views_25p", "views_50p", "views_75p", "complete_views", "video_views", "active_view_impressions", "all_conversions"],
        existing_columns=["fact_date", "external_campaign_id","external_ad_group_id", "external_ad_id", "load_id", "platform_name", "currency_code"],
        required_columns=required_columns) }}
),
fact_adwords_ad_conversion_performance AS (
    SELECT
        conv."date" as fact_date,
        conv."campaign_id" as external_campaign_id,
        conv."adgroup_id" as external_ad_group_id,
        conv."ad_id" as external_ad_id,
        CAST(conv."all_conversions" AS NUMERIC(38, 10)) AS all_conversions,
        conv.run_id as load_id,
        c_map.conversion_action_id::text as conversion_identifier,
        'reports_adwords_ad_conversion_performance' as platform_name
    FROM
        {{ filtered_source('reports', 'reports_adwords_ad_conversion_performance') }} conv
        JOIN conversion_mapping c_map
    ON conv."conversion_action_id" = c_map.conversion_action_id
),

        
unpivoted_adwords_ad_conversion_performance AS (
    {% set table_name = "fact_adwords_ad_conversion_performance" %}
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["all_conversions"],
        existing_columns=["fact_date", "external_campaign_id","external_ad_group_id", "external_ad_id","conversion_identifier", "load_id", "platform_name"],
        required_columns=required_columns) }}
),

fact_adwords_campaign_conversion_performance AS (
    SELECT
        conv."date" as fact_date,
        conv."campaign_id" as external_campaign_id,
        CAST(conv."all_conversions" AS NUMERIC(38, 10)) AS all_conversions,
        conv.run_id as load_id,
        c_map.conversion_action_id::text as conversion_identifier,
        'search_campaign_conversion_performance' as platform_name
    FROM
        {{ filtered_source('reports', 'search_campaign_conversion_performance') }} conv
        JOIN conversion_mapping c_map
    ON conv."conversion_action_id" = c_map.conversion_action_id
),

unpivoted_adwords_campaign_conversion_performance AS (
    {% set table_name = "fact_adwords_campaign_conversion_performance" %}
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["all_conversions"],
        existing_columns=["fact_date", "external_campaign_id","conversion_identifier", "load_id", "platform_name"],
        required_columns=required_columns) }}
)

SELECT * FROM unpivoted_adwords_campaign_performance
UNION ALL
SELECT * FROM unpivoted_adwords_ad_mobile_performance
UNION ALL
SELECT * FROM unpivoted_adwords_ad_level_performance
UNION ALL
SELECT * FROM unpivoted_adwords_ad_conversion_performance
UNION ALL
SELECT * FROM unpivoted_adwords_campaign_conversion_performance
