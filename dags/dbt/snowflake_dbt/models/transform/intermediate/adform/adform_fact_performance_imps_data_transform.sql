with
adform_imps_performance_report as (
    select
        "date" as fact_date,
        "campaign_id" as external_campaign_id,
        "media_id" as external_media_id,
        "advertiser_id" as advertiser_id,
        "tracker_id" as external_tracking_filter_id,
        "tag_id" as external_tag_id,
        cast(sum("impressions") as <PERSON>UMERIC(38, 10)) as impressions,
        cast(sum("clicks") as <PERSON>UMERIC(38, 10)) as clicks,
        cast(sum("video_complete_count") as NUMERIC(38, 10)) as completed_views_full,
        cast(sum("video_events_play_time_percent_25") * sum("video_play_start_count") as NUMERIC(38, 10)) as completed_views_first,
        cast(sum("video_events_play_time_percent_50") * sum("video_play_start_count") as NUMERIC(38, 10)) as completed_views_mid,
        cast(sum("video_events_play_time_percent_75") * sum("video_play_start_count") as NUMERIC(38, 10)) as completed_views_third,
        cast(sum("video_play_start_count") as NUMERIC(38, 10)) as video_views,
        cast(sum("video_play_start_count") as NUMERIC(38, 10)) as video_start,
        run_id as load_id
    from {{ source('REPORTS', 'reports_adformconversion_v2') }}
    where "tracker_id" = 0
    group by "date", "campaign_id", "advertiser_id", "media_id", "tag_id", run_id, "tracker_id"
),

unpivoted_adform_imps_report AS (
{% set table_name = "adform_imps_performance_report" %}
{{ unpivot_table(source_table_name=table_name,
    metric_names=["impressions", "clicks", "completed_views_full", "completed_views_first",
                  "completed_views_mid", "completed_views_third", "video_views", "video_start"],
    existing_columns=["fact_date", "external_campaign_id","external_media_id", "advertiser_id", "external_tracking_filter_id",
     "external_tag_id", "load_id", impressions, clicks, completed_views_full, completed_views_first, completed_views_mid, completed_views_third, video_views, video_start],
    required_columns=["fact_date", "external_campaign_id","external_media_id", "advertiser_id", "external_tracking_filter_id",
     "external_tag_id", "load_id"]) }})


select *
from unpivoted_adform_imps_report