with
adform_conv_performance_report as (
    select
        "date" as fact_date,
        "campaign_id" as external_campaign_id,
        "media_id" as external_media_id,
        "advertiser_id" as advertiser_id,
        "tracker_id" as external_tracking_filter_id,
        "tag_id" as external_tag_id,
        cast(sum("conversions") as NUMERIC(38, 10)) as conversions,
        cast(sum("conversions_post_imp") as NUMERIC(38, 10)) as post_impressions,
        cast(sum("conversions_post_click") as NUMERIC(38, 10)) as post_clicks,
        run_id as load_id
    from {{ filtered_source('REPORTS', 'reports_adformconversion_v2') }}
    where "tracker_id" != 0
    group by "date", run_id, "campaign_id", "advertiser_id", "media_id", "tag_id", "tracker_id"
),

unpivoted_adform_conversion_report AS (
{% set table_name = "adform_conv_performance_report" %}
{{ unpivot_table(source_table_name=table_name,
    metric_names=["conversions", "post_impressions", "post_clicks"],
    existing_columns=["fact_date", "external_campaign_id","external_media_id", "advertiser_id", "external_tracking_filter_id",
     "external_tag_id", "load_id"],
    required_columns=["fact_date", "external_campaign_id","external_media_id", "advertiser_id", "external_tracking_filter_id",
     "external_tag_id", "load_id"]) }}
)

select *
from unpivoted_adform_conversion_report