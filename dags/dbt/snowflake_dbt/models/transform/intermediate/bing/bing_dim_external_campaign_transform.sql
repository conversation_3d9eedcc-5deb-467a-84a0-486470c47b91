SELECT DISTINCT
        camp."external_id" AS external_campaign_id,
        camp."id" AS internal_campaign_id,
        camp."name" AS campaign_name,
        intgr."currency_code" AS campaign_currency,
        camp.run_id AS load_id,
        '{{ constants("MICROSOFT_ADS") }}' AS platform_name
    FROM {{ filtered_source('BING', 'integrations')}} intgr
    JOIN {{ filtered_source('BING', 'platform_settings')}} ps
        ON ps."integration_id" = intgr."id"
    JOIN {{ filtered_source('BING', 'campaigns')}} camp
        ON camp."platform_settings_id" = ps."tile_id"