{% set required_columns = get_druid_required_colunms() %}

WITH fact_bing_ad_report AS (
    SELECT
        "date" AS fact_date,
        "campaign_id" AS external_campaign_id,
        "ad_group_id" AS external_ad_group_id,
        "ad_id" AS external_ad_id,
        "currency_code" AS currency_code,
        CAST("impressions" AS NUMERIC(38, 10)) AS impressions,
        CAST("clicks" AS NUMERIC(38, 10)) AS clicks,
        CAST("spend" AS NUMERIC(38, 10)) As spend,
        CAST("conversions_qualified" AS NUMERIC(38, 10)) AS platform_conversions,
        run_id AS load_id,
        '{{ constants("MICROSOFT_ADS") }}' AS platform_name
    FROM
        {{ filtered_source('REPORTS', 'microsoft_advertising_performance_ad_level') }}
),

unpivot_fact_bing_ad_report AS (
{% set table_name = "fact_bing_ad_report" %}
{{ unpivot_table(source_table_name=table_name,
    metric_names=["impressions", "clicks", "spend", "platform_conversions"],
    existing_columns=["fact_date", "external_campaign_id","external_ad_group_id", "external_ad_id", "currency_code", "load_id", "platform_name"],
    required_columns=required_columns) }}
),

fact_bing_campaign_report AS (
    SELECT
        "date" AS fact_date,
        "campaign_id" AS external_campaign_id,
        "currency_code" AS currency_code,
        CAST("impressions" AS NUMERIC(38, 10)) AS impressions,
        CAST("clicks" AS NUMERIC(38, 10)) AS clicks,
        CAST("spend" AS NUMERIC(38, 10)) As spend,
        CAST("conversions_qualified" AS NUMERIC(38, 10)) AS platform_conversions,
        run_id AS load_id,
        '{{ constants("MICROSOFT_ADS") }}' AS platform_name
    FROM
        {{ filtered_source('REPORTS', 'microsoft_advertising_estimated_campaign_level_v2') }}
),

unpivot_fact_bing_campaign_report AS (
{% set table_name = "fact_bing_campaign_report" %}
{{ unpivot_table(source_table_name=table_name,
    metric_names=["impressions", "clicks", "spend", "platform_conversions"],
    existing_columns=["fact_date", "external_campaign_id", "currency_code", "load_id", "platform_name"],
    required_columns=required_columns) }}
)
Select * from unpivot_fact_bing_ad_report
UNION ALL
Select * from unpivot_fact_bing_campaign_report
