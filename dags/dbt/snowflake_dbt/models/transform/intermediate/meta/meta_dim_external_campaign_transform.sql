SELECT DISTINCT
        campaign."external_campaign_id" AS external_campaign_id,
        campaign."id" AS internal_campaign_id,
        campaign."name" AS campaign_name,
        intgr."currency" AS campaign_currency,
        campaign.run_id AS load_id,
        '{{ constants("META") }}' AS platform_name
    FROM {{ filtered_source('FACEBOOK_INSTAGRAM', 'campaign_facebookcampaign')}} campaign
    JOIN {{ filtered_source('FACEBOOK_INSTAGRAM', 'account_facebookexternaldata')}} intgr
        ON campaign."credentials_id" = intgr."id"