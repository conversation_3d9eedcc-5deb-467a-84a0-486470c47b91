{% set required_columns = get_druid_required_colunms() %}

WITH face_meta_campaign_report AS (
    SELECT
        "date" as fact_date,
        "campaign_id" as external_campaign_id,
        "ad_set_id" as external_ad_group_id,
        "ad_id" as external_ad_id,
        CAST("landing_page_views" AS NUMERIC(38, 10)) AS landing_page_views,
        CAST("onsite_conversion" AS NUMERIC(38, 10)) AS platform_conversions,
        CAST("like" AS NUMERIC(38, 10)) AS page_likes,
        CAST("post_engagement" AS NUMERIC(38, 10)) AS post_engagement,
        CAST("link_clicks" AS NUMERIC(38, 10)) AS clicks,
        CAST("impressions" AS NUMERIC(38, 10)) AS impressions,
        CAST("clicks" AS NUMERIC(38, 10)) AS clicks_all,
        CAST("spend" AS NUMERIC(38, 10)) AS spend,
        CAST("completed_views_full" AS NUMERIC(38, 10)) AS completed_views_full,
        CAST("three_sec_video_views" AS NUMERIC(38, 10)) AS video_views,
        CAST("thruplays" AS NUMERIC(38, 10)) AS thruplays,
        CAST("post_reaction" AS NUMERIC(38, 10)) AS post_reaction,
        CAST("post_comments" AS NUMERIC(38, 10)) AS post_comments,
        CAST("post_saves" AS NUMERIC(38, 10)) AS post_saves,
        CAST("video_plays" AS NUMERIC(38, 10)) AS video_start,
        CAST("video_p25_watched_actions" AS NUMERIC(38, 10)) AS completed_views_first,
        CAST("video_p50_watched_actions" AS NUMERIC(38, 10)) AS completed_views_mid,
        CAST("video_p75_watched_actions" AS NUMERIC(38, 10)) AS completed_views_third,
        "account_currency" AS currency_code,
        run_id AS load_id,
        '{{ constants("META") }}' AS platform_name
    FROM
        {{ filtered_source('REPORTS', 'social_campaigns_reports_v3') }}
),

unpivoted_face_meta_campaign_report AS (
{% set table_name = "face_meta_campaign_report" %}
{{ unpivot_table(source_table_name=table_name,
    metric_names=["landing_page_views", "platform_conversions", "page_likes", "post_engagement", "clicks", "impressions", "clicks_all",
            "spend", "completed_views_full", "video_views", "thruplays", "post_reaction", "post_comments", "post_saves", "video_start",
            "completed_views_first", "completed_views_mid", "completed_views_third"],
    existing_columns=["fact_date", "external_campaign_id", "external_ad_group_id", "external_ad_id", "load_id", "currency_code", "platform_name"],
    required_columns=required_columns) }}
),

fact_meta_conversions_report AS (
    SELECT
        "date" AS fact_date,
        "campaign_id" AS external_campaign_id,
        "ad_set_id" AS external_ad_group_id,
        "ad_id" AS external_ad_id,
        CAST("conversion_name" AS VARCHAR) AS conversion_identifier,
        "conversion_currency" AS currency_code,
        CAST("value" AS NUMERIC(38, 10)) AS conversions,
        CAST("revenue" AS NUMERIC(38, 10)) AS revenue,
        run_id AS load_id,
        '{{ constants("META") }}' AS platform_name
    FROM
        {{ filtered_source('REPORTS', 'meta_conversions_report') }}
),

unpivoted_fact_meta_conversions_report AS (
{% set table_name = "fact_meta_conversions_report" %}
{{ unpivot_table(source_table_name=table_name,
    metric_names=["conversions", "revenue"],
    existing_columns=["fact_date", "external_campaign_id", "external_ad_group_id", "external_ad_id", "conversion_identifier", "currency_code", "load_id", "platform_name"],
    required_columns=required_columns) }}
)
Select * from unpivoted_face_meta_campaign_report
UNION ALL
Select * from unpivoted_fact_meta_conversions_report