SELECT DISTINCT
        ad_group."id" AS internal_ad_group_id,
        CASE WHEN ad_group."external_ad_set_id" = '' THEN NULL ELSE ad_group."external_ad_set_id" END AS external_ad_group_id,
        ad_group."name" AS ad_group_name,
        ad_group.run_id AS load_id,
        '{{ constants("META") }}' AS platform_name
    FROM {{ filtered_source('FACEBOOK_INSTAGRAM', 'campaign_facebookadsetmodel') }} ad_group