with
creative_names as (
    SELECT DISTINCT
        "creative_id",
        "creative_name",
        ROW_NUMBER() OVER (
            PARTITION BY
                "creative_id"
            ORDER BY
                "creative_id",
                "creative_name"
          ) AS row_num
        FROM {{ filtered_source('reports', 'dv360_base_reports') }} QUALIFY row_num = 1
),
dv360 as (
    SELECT DISTINCT
        br."campaign_id" as external_campaign_id,
        br."io_id" as external_ad_group_id,
        br."line_item_id" as external_ad_id,
        br."creative_id" as external_creative_id,
        cn."creative_name" as creative_name,
        br.run_id as load_id,
        '{{ constants("DV360") }}'  AS platform_name,
    FROM {{ filtered_source('reports', 'dv360_base_reports') }} br
    LEFT JOIN creative_names cn
        on br."creative_id" = cn."creative_id"
),
creative_names_youtube as (
    SELECT DISTINCT "youtube_ad_id",
           "youtube_ad_name",
            ROW_NUMBER() OVER (
                PARTITION BY
                    "youtube_ad_id"
                ORDER BY
                    "youtube_ad_id",
                    "youtube_ad_name"
              ) AS row_num
        FROM {{ filtered_source('reports', 'dv360_base_reports_youtube') }} QUALIFY row_num = 1
),
dv360_youtube as (
    SELECT DISTINCT
        NULL as external_campaign_id,
        bry."io_id" as external_ad_group_id,
        bry."line_item_id" as external_ad_id,
        bry."youtube_ad_id" as external_creative_id,
        cny."youtube_ad_name" as creative_name,
        bry.run_id as load_id,
        'Youtube (DV360)' AS platform_name,
    FROM {{ filtered_source('reports', 'dv360_base_reports_youtube') }}  bry
    LEFT JOIN creative_names_youtube cny
        on bry."youtube_ad_id" = cny."youtube_ad_id"
)

SELECT *
FROM dv360
union all
SELECT *
FROM dv360_youtube
