SELECT DISTINCT
        dc."external_campaign_id" AS external_campaign_id,
        dc."id" AS internal_campaign_id,
        dc."name" AS campaign_name,
        di."currency_code" AS campaign_currency,
        dc.run_id as load_id,
        '{{ constants("DV360") }}'  AS platform_name
    FROM {{ filtered_source('DV360', 'dv360_dv360displaycampaign') }} as dc
    JOIN {{ filtered_source('DV360', 'dv360_dv360integration') }} as di
      ON dc."integration_id" = di."id"