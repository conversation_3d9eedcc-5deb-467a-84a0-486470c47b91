{% set required_columns = get_druid_required_colunms() %}

WITH fact_dv360_base_reports_performance AS (
    SELECT
        "campaign_id" as external_campaign_id,
        "io_id" as external_ad_group_id,
        "line_item_id" as external_ad_id,
        "creative_id" as external_creative_id,
        "date" as fact_date,
        "advertiser_currency" as currency_code,
        CAST("impressions" AS numeric(38,10)) AS impressions,
        CAST("clicks" AS numeric(38,10)) AS clicks,
        CAST("spent" AS numeric(38,10)) AS budget_spent,
        CAST("quartile_1" AS numeric(38,10)) AS views_25p,
        CAST("quartile_2" AS numeric(38,10)) AS views_50p,
        CAST("quartile_3" AS numeric(38,10)) AS views_75p,
        CAST("quartile_4" AS numeric(38,10)) AS complete_views,
        CAST("true_view_views" AS numeric(38,10)) AS true_views,
        CAST("starts_video" AS numeric(38,10)) AS video_start,
        CAST("starts_video" AS numeric(38,10)) AS video_views,
        CAST("viewable_impressions" AS numeric(38,10)) AS viewable_impressions,
        CAST("conversions" AS numeric(38,10)) AS conversions,
        br.run_id as load_id,
        '{{ constants("DV360") }}'  as platform_name
    FROM
        {{ filtered_source('reports', 'dv360_base_reports') }} br
),

unpivoted_dv360_base_reports_performance AS (
    {% set table_name = "fact_dv360_base_reports_performance" %}
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["impressions", "clicks", "budget_spent", "views_25p", "views_50p", "views_75p", "complete_views",
        "true_views", "video_start", "video_views", "viewable_impressions", "conversions"],
        existing_columns=["fact_date", "external_campaign_id","external_ad_group_id",
        "external_ad_id", "external_creative_id",
        "platform_name", "currency_code", "load_id", "platform_name"],
        required_columns=required_columns) }}
),

dv360_base_reports_youtube_performance  AS (
    SELECT
        "io_id" as external_ad_group_id,
        "line_item_id" as external_ad_id,
        "youtube_ad_id" as external_creative_id,
        "date" as fact_date,
        "advertiser_currency" as currency_code,
        CAST("impressions" AS numeric(38,10)) AS impressions,
        CAST("clicks" AS numeric(38,10)) AS clicks,
        CAST("spent" AS numeric(38,10)) AS budget_spent,
        CAST("quartile_1" AS numeric(38,10)) AS views_25p,
        CAST("quartile_2" AS numeric(38,10)) AS views_50p,
        CAST("quartile_3" AS numeric(38,10)) AS views_75p,
        CAST("quartile_4" AS numeric(38,10)) AS complete_views,
        CAST("true_view_views" AS numeric(38,10)) AS true_views,
        CAST("true_view_views" AS numeric(38,10)) AS video_views,
        CAST("viewable_impressions" AS numeric(38,10)) AS viewable_impressions,
        br.run_id as load_id,
        'Youtube (DV360)' as platform_name
    FROM
        {{ filtered_source('reports', 'dv360_base_reports_youtube') }} br
),

unpivoted_dv360_base_reports_youtube_performance  AS (
    {% set table_name = "dv360_base_reports_youtube_performance" %}
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["impressions", "clicks", "budget_spent", "views_25p", "views_50p", "views_75p", "complete_views",
        "true_views", "video_views", "viewable_impressions"],
        existing_columns=["fact_date","external_ad_group_id",
        "external_ad_id", "external_creative_id", "platform_name",
         "currency_code", "load_id", "platform_name"],
        required_columns=required_columns) }}
)
SELECT * FROM unpivoted_dv360_base_reports_performance
UNION ALL
SELECT * FROM unpivoted_dv360_base_reports_youtube_performance
