
SELECT DISTINCT
        omni."external_id" AS external_campaign_id,
        omni."id" AS internal_campaign_id,
        omni."name" AS campaign_name,
        intgr."currency" AS campaign_currency,
        omni.run_id as load_id,
        '{{ constants("AMAZON") }}' as platform_name
    FROM {{ filtered_source('omni_connector', 'campaigns') }} omni
    jOIN {{ filtered_source('omni_connector', 'tile_settings') }} ts
      ON omni."tile_settings_id" = ts."id"
    jOIN {{ filtered_source('omni_connector', 'integrations') }} intgr
      ON ts."integration_id" = intgr."id"
    WHERE omni."platform" = 'amazon_integrated'