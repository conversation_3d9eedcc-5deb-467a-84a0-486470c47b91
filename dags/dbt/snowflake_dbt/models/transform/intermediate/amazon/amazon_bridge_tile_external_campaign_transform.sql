
SELECT DISTINCT
        omni."id" AS internal_campaign_id,
        ts."tile_id" AS tile_id,
        omni.run_id as load_id,
        '{{ constants("AMAZON") }}' as platform_name
    FROM {{ filtered_source('omni_connector', 'campaigns') }} omni
    jOIN {{ filtered_source('omni_connector', 'tile_settings') }} ts
      ON omni."tile_settings_id" = ts."id"
    WHERE omni."platform" = 'amazon_integrated'