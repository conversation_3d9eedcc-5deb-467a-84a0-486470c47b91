
SELECT
                       omni."id" || '_' || omni."ad_group_id" as surrogate_key,
                       omni."id" as internal_ad_id,
                       omni."external_id" as external_ad_id,
                       omni."name" as ad_name,
                       omni.run_id as load_id,
                       '{{ constants("AMAZON") }}' as platform_name
                       FROM {{ filtered_source('omni_connector', 'ads') }} omni
                       WHERE omni."platform" = 'amazon_integrated'