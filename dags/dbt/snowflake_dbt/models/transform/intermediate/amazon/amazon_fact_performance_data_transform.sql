{% set required_columns = get_druid_required_colunms() %}

WITH

amazon_base_report as 
(
SELECT
    "date" AS fact_date,
    "campaign_id" AS external_campaign_id,
    "ad_group_id" AS external_ad_group_id,
    "ad_id" AS external_ad_id,
    CAST("impressions" AS NUMERIC(38, 10)) AS impressions,
    CAST("clicks" AS NUMERIC(38, 10)) AS clicks,
    CAST("spend" AS NUMERIC(38, 10)) AS spend,
    CAST("completed_views_first" AS NUMERIC(38, 10)) AS completed_views_first,
    CAST("completed_views_mid" AS NUMERIC(38, 10)) AS completed_views_mid,
    CAST("completed_views_third" AS NUMERIC(38, 10)) AS completed_views_third,
    CAST("completed_views_full" AS NUMERIC(38, 10)) AS completed_views_full,
    CAST("video_start" AS NUMERIC(38, 10)) AS video_start,
    apr.run_id AS load_id,
    '{{ constants("AMAZON") }}' AS platform_name
FROM
    {{ filtered_source('REPORTS', constants("AMAZON_AD_LEVEL_PERFORMANCE_REPORTS_TABLE")) }} apr
),

{% set table_name = "amazon_base_report" %}
unpivoted_base_amazon_performance_report as (
        {{ unpivot_table(source_table_name=table_name,
        metric_names=["completed_views_full", "completed_views_first",
         "completed_views_mid", "completed_views_third", "spend", "video_start", "clicks", "impressions"],
        existing_columns=["fact_date", "external_campaign_id", "external_ad_group_id",
                          "external_ad_id","platform_name", "load_id"],
        required_columns=required_columns) }}
)

SELECT * FROM unpivoted_base_amazon_performance_report