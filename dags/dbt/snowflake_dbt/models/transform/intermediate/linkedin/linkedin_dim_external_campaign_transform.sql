
SELECT DISTINCT
    omni."external_id" AS external_campaign_id,
    omni."id" AS internal_campaign_id,
    omni."name" AS campaign_name,
    intgr."currency" AS campaign_currency,
    omni.run_id AS load_id,
    '{{ constants("LINKEDIN") }}' AS platform_name
FROM {{ filtered_source('omni_connector', 'campaigns') }} omni
JOIN {{ filtered_source('omni_connector', 'tile_settings') }} ts
    ON omni."tile_settings_id" = ts."id"
JOIN {{ filtered_source('omni_connector', 'integrations') }} intgr
    ON ts."integration_id" = intgr."id"
WHERE omni."platform" = 'linkedin_integrated'
