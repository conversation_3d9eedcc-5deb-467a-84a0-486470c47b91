{% set required_columns = get_druid_required_colunms() %}

WITH

linkedin_base_report AS (
    SELECT
        "date" AS fact_date,
        "campaign_id" AS external_campaign_id,
        "ad_group_id" AS external_ad_group_id,
        "ad_id" AS external_ad_id,
        CAST("impressions" AS NUMERIC(38, 10)) AS impressions,
        CAST("clicks" AS NUMERIC(38, 10)) AS clicks,
        CAST("spend" AS NUMERIC(38, 10)) AS spend,
        CAST("completed_views_first" AS NUMERIC(38, 10)) AS completed_views_first,
        CAST("completed_views_mid" AS NUMERIC(38, 10)) AS completed_views_mid,
        CAST("completed_views_third" AS NUMERIC(38, 10)) AS completed_views_third,
        CAST("completed_views_full" AS NUMERIC(38, 10)) AS completed_views_full,
        CAST("video_start" AS NUMERIC(38, 10)) AS video_start,
        CAST("video_views" AS NUMERIC(38, 10)) AS video_views,
        CAST("platform_conversions" AS NUMERIC(38, 10)) AS platform_conversions,
        apr.run_id AS load_id,
        '{{ constants("LINKEDIN") }}' AS platform_name
    FROM
        {{ filtered_source('REPORTS', constants("LINKEDIN_AD_LEVEL_PERFORMANCE_REPORTS_TABLE")) }} apr
),

{% set table_name = "linkedin_base_report" %}
unpivoted_base_linkedin_performance_report AS (
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["completed_views_full", "completed_views_first",
        "completed_views_mid", "completed_views_third", "spend", "video_start", "video_views",
        "platform_conversions", "clicks", "impressions"],
        existing_columns=["fact_date", "external_campaign_id", "external_ad_group_id",
        "external_ad_id", "platform_name", "load_id"],
        required_columns=required_columns) }}
)

SELECT * FROM unpivoted_base_linkedin_performance_report
