{% set required_columns = get_druid_required_colunms() %}

WITH

fact_xandr_base_performance AS (
    SELECT
        "date" as fact_date,

        "campaign_id" as external_campaign_id,
        "insertion_order_id" as external_ad_group_id,
        "line_item_id" as external_ad_id,
        "creative_id"::text as external_creative_id,
        CAST("impressions" AS FLOAT) AS impressions,
        CAST("clicks" AS FLOAT) AS clicks,
        CAST("spend" AS FLOAT) AS budget_spent,
        CAST("completed_views_first" AS FLOAT) AS views_25p,
        CAST("completed_views_mid" AS FLOAT) AS views_50p,
        CAST("completed_views_third" AS FLOAT) AS views_75p,
        CAST("completed_views_full" AS FLOAT) AS complete_views,
        CAST("video_start" AS FLOAT) AS active_view_impressions,
        CAST("platform_conversions" AS FLOAT) AS all_conversions,
        run_id as load_id,
        CASE WHEN "conversion_name" = '--' THEN NULL ELSE "conversion_name" END AS conversion_identifier,
        '{{constants("XANDR")  }}' as platform_name,
        "currency" as currency_code,

    FROM
        {{ filtered_source('reports', 'reports_xandr_performance_v3') }} xandr_perf

),

unpivoted_xandr_base_performance AS (
    {% set table_name = "fact_xandr_base_performance" %}
    {{ unpivot_table(source_table_name=table_name,
        metric_names=["impressions", "clicks", "budget_spent", "views_25p", "views_50p", "views_75p", "complete_views", "active_view_impressions", "all_conversions"],
        existing_columns=["fact_date", "external_campaign_id","external_ad_group_id", "external_ad_id","external_creative_id", "load_id","conversion_identifier", "platform_name", "currency_code"],
        required_columns=required_columns) }}
)
SELECT * FROM unpivoted_xandr_base_performance