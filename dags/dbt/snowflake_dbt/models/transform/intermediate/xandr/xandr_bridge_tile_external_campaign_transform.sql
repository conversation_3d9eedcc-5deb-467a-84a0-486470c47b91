SELECT DISTINCT
        camp."id" AS internal_campaign_id,
        ps."tile_id" AS tile_id,
        camp.run_id as load_id,
        '{{ constants("XANDR") }}' AS platform_name
    FROM (
    SELECT *
    FROM {{ filtered_source('omni_connector', 'integrations') }}
    WHERE "platform" = 'xandr_integrated'
) as intgr
    JOIN {{ filtered_source('omni_connector', 'tile_settings') }} as ps
      ON intgr."id" = ps."integration_id"
    JOIN {{ filtered_source('omni_connector', 'campaigns') }} as camp
      ON ps."id" = camp."tile_settings_id" and intgr."platform" = camp."platform"