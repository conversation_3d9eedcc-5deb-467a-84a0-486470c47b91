WITH creative_names as (SELECT DISTINCT "creative_id",
                                        "creative_name",
                                        ROW_NUMBER() OVER (
            PARTITION BY
                "creative_id"
            ORDER BY
                "date"
          ) AS row_num
                        FROM {{ filtered_source('reports', 'reports_xandr_performance_v3') }} QUALIFY
    row_num = 1
    )

SELECT DISTINCT xandr_ads."campaign_id"        as external_campaign_id,
                xandr_ads."insertion_order_id" as external_ad_group_id,
                xandr_ads."line_item_id"       as external_ad_id,
                cn."creative_id"::text as external_creative_id,
                cn."creative_name"::text as creative_name,
                xandr_ads.run_id as load_id,
                '{{ constants("XANDR") }}'     AS platform_name
FROM {{ filtered_source('reports', 'reports_xandr_performance_v3') }} xandr_ads
         LEFT JOIN creative_names cn
                   on xandr_ads."creative_id" = cn."creative_id"