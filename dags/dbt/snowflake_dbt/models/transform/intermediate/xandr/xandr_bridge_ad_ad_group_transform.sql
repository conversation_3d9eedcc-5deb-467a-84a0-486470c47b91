SELECT
                       (xandr_ads."id" || '_' || xandr_ads."ad_group_id")::text as surrogate_key,
                       xandr_ads."id" as internal_ad_id,
                       xandr_ads."ad_group_id" as internal_ad_group_id,
                       xandr_ads.run_id as load_id,
                       '{{ constants("XANDR") }}' AS platform_name
                       FROM (
    SELECT *
    FROM {{ filtered_source('omni_connector', 'ads') }}
    WHERE "platform" = 'xandr_integrated'
)  xandr_ads
