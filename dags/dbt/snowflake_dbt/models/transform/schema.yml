version: 2

models:
  - name: dim_media_plan_transform
    description: >
      Transform model for media plans that maps source fields to standardized fields.
    columns:
      - name: media_plan_id
        description: Unique identifier for the media plan
        tests:
          - not_null
          - unique

      - name: media_plan_name
        description: Name of the media plan
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

  - name: dim_channel_transform
    description: >
      Transform model for channels that maps source fields to standardized fields.
    columns:
      - name: channel_id
        description: Unique identifier for the channel
        tests:
          - not_null
          - unique

      - name: channel_name
        description: Name of the channel
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

  - name: dim_platform_transform
    description: >
      Transform model for platforms that maps source fields to standardized fields.
    columns:
      - name: platform_id
        description: Unique identifier for the platform
        tests:
          - not_null
          - unique

      - name: platform_name
        description: Name of the platform
        tests:
          - not_null
#          need to fix dim_platform_name to include external_platform_name or something similar
#          - accepted_values:
#              values: [ 'Google Ads' , 'Xandr']

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

  - name: dim_tile_transform
    description: >
      Transform model for tiles that maps source fields to standardized fields.
    columns:
      - name: tile_id
        description: Unique identifier for the tile
        tests:
          - not_null
          - unique

      - name: tile_name
        description: Name of the tile
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

  - name: dim_journey_transform
    description: >
      Transform model for journeys that maps source fields to standardized fields.
    columns:
      - name: journey_id
        description: Unique identifier for the journey
        tests:
          - not_null
          - unique

      - name: journey_name
        description: Name of the journey
        tests:
          - not_null

      - name: company_id
        description: Company identifier for the journey
        tests:
          - not_null

      - name: journey_currency
        description: Currency of the journey
        tests:
          - not_null

      - name: is_for_test
        description: Flag indicating if the journey is for testing
        tests:
          - not_null

      - name: is_deleted
        description: Flag indicating if the journey is deleted
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

  - name: dim_ad_group_transform
    description: "Dimension table for ad groups"
    columns:
      - name: internal_ad_group_id
        description: Internal identifier for the ad group
        tests:
          - not_null

      - name: external_ad_group_id
        description: External identifier for the ad group

      - name: ad_group_name
        description: Name of the ad group
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

      - name: platform_name
        description: Name of the platform the ad group belongs to
        tests:
          - not_null
          - accepted_values:
                values: ['Google Ads', 'DV360', 'Meta', 'Microsoft Ads', 'Amazon', 'Zemanta', 'Xandr', 'LinkedIn']

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - internal_ad_group_id
            - platform_name

  - name: bridge_platform_channel_transform
    description: >
      Bridge table linking platforms and channels
    columns:
      - name: channel_id
        description: Foreign key to channel dimension
        tests:
          - not_null

      - name: platform_id
        description: Foreign key to platform dimension
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - channel_id
            - platform_id

  - name: bridge_ad_ad_group_transform
    description: "Bridge table linking ads and ad groups"
    columns:
      - name: surrogate_key
        description: Composite key for the ad and ad group relationship
        tests:
          - not_null

      - name: internal_ad_id
        description: Internal identifier for the ad
        tests:
          - not_null

      - name: internal_ad_group_id
        description: Internal identifier for the ad group
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - internal_ad_id
            - internal_ad_group_id
            - surrogate_key
            - platform_name

  - name: bridge_media_plan_journey_transform
    description: >
      Bridge table linking media plans and journeys
    columns:
      - name: media_plan_id
        description: Foreign key to media plan dimension
        tests:
          - not_null

      - name: journey_id
        description: Foreign key to journey dimension
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - media_plan_id
            - journey_id

  - name: fact_performance_data_transform
    description: "Fact table for performance data"
    columns:
      - name: fact_date
        description: Date of the performance data
        tests:
          - not_null

      - name: external_campaign_id
        description: External identifier for the campaign
        tests:
          - not_null:
              config:
                where: "platform_name not in ('Youtube (DV360)')"

      - name: external_ad_group_id
        description: External identifier for the ad group

      - name: external_ad_id
        description: External identifier for the ad

      - name: conversion_identifier
        description: Unique identifier for the conversion

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

      - name: platform_name
        description: Name of the platform
        tests:
          - not_null
          - accepted_values:
              values: ["search_campaign_performance",
                       "search_campaign_performance_mobile",
                       "reports_adwords_ad_performance",
                       "reports_adwords_ad_conversion_performance",
                       "search_campaign_conversion_performance",
                       "Meta",
                       "DV360",
                       "Youtube (DV360)",
                       'Microsoft Ads',
                       'Zemanta', 
                       'Amazon',
                       'Xandr',
                       'LinkedIn']


      - name: currency_code
        description: Currency code for the performance data

      - name: fact_name
        description: Name of the fact
        tests:
          - not_null

      - name: fact_value
        description: Value of the fact

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - fact_date
            - external_campaign_id
            - external_ad_group_id
            - external_ad_id
            - external_creative_id
            - conversion_identifier
            - currency_code
            - fact_name
            - platform_name

  - name: bridge_ad_group_external_campaign_transform
    description: >
      Bridge table linking ad groups and external campaigns
    columns:
      - name: internal_ad_group_id
        description: Foreign key to ad group dimension
        tests:
          - not_null

      - name: internal_campaign_id
        description: Foreign key to external campaign dimension
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

      - name: platform_name
        description: Name of the platform
        tests:
          - not_null
          - accepted_values:
              values: [ 'Google Ads', 'DV360', 'Meta', 'Microsoft Ads', 'Zemanta', 'Amazon', 'Xandr', 'LinkedIn']

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - internal_ad_group_id
            - internal_campaign_id
            - platform_name

  - name: bridge_media_row_platform_transform
    description: >
      Bridge table linking media rows and platforms
    columns:
      - name: media_row_id
        description: Foreign key to media row dimension
        tests:
          - not_null

      - name: platform_id
        description: Foreign key to platform dimension
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - media_row_id
            - platform_id

  - name: bridge_tile_external_campaign_transform
    description: >
      Bridge table linking tiles and external campaigns
    columns:
      - name: internal_campaign_id
        description: Foreign key to external campaign dimension
        tests:
          - not_null

      - name: tile_id
        description: Foreign key to tile dimension
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

      - name: platform_name
        description: Name of the platform
        tests:
          - not_null
          - accepted_values:
              values: [ 'Google Ads', 'DV360', 'Meta', 'Microsoft Ads', 'Amazon', 'Zemanta', 'Xandr', 'LinkedIn']

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - internal_campaign_id
            - tile_id

  - name: bridge_channel_media_plan_transform
    description: >
      Bridge table linking channels and media plans
    columns:
      - name: media_plan_id
        description: Foreign key to media plan dimension
        tests:
          - not_null

      - name: channel_id
        description: Foreign key to channel dimension
        tests:
          - not_null

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - media_plan_id
            - channel_id

  - name: dim_ad_transform
    description: >
      Transform model for ads that maps source fields to standardized fields.
    columns:
      - name: surrogate_key
        description: Composite key combining internal_ad_id and ad_group_id or just external_ad_id in case of Xandr
        tests:
          - not_null

      - name: internal_ad_id
        description: Internal identifier for the ad can be NULL in case of Xandr

      - name: external_ad_id
        description: External identifier for the ad

      - name: ad_name
        description: Name of the ad

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

      - name: platform_name
        description: Name of the platform the ad belongs to
        tests:
          - not_null
          - accepted_values:
              values: [ 'Google Ads', 'Meta', 'DV360', 'Microsoft Ads', 'Zemanta', 'Amazon', 'Xandr', 'LinkedIn' ]
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - surrogate_key
            - platform_name

  - name: dim_media_row_transform
    description: >
      Transform model for media rows that maps source fields to standardized fields.
    columns:
      - name: media_row_id
        description: Unique identifier for the media row
        tests:
          - not_null

      - name: media_row_name
        description: Name of the media row
        tests:
          - not_null

      - name: media_row_start_date
        description: Start date of the media row

      - name: media_row_end_date
        description: End date of the media row

      - name: media_row_currency
        description: Currency of the media row

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

      - name: tile_id
        description: Foreign key to tile dimension

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - media_row_id
            - tile_id

  - name: dim_external_campaign_transform
    description: "Dimension table for external campaigns"
    columns:
      - name: external_campaign_id
        description: External identifier for the campaign

      - name: internal_campaign_id
        description: Internal identifier for the campaign
        tests:
          - not_null

      - name: campaign_name
        description: Name of the campaign
        tests:
          - not_null

      - name: campaign_currency
        description: Currency of the campaign

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

      - name: platform_name
        description: Name of the platform the campaign belongs to
        tests:
          - not_null
          - accepted_values:
              values: [ 'Google Ads', 'Meta', 'DV360', 'Microsoft Ads', 'Zemanta', 'Amazon', 'Xandr', 'LinkedIn' ]
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - internal_campaign_id
            - platform_name

  - name: dim_conversion_transform
    description: "Dimension table for mapping conversion id and label"
    columns:
    - name: conversion_identifier
      description: Unique identifier of conversion
      tests:
        - not_null

    - name: conversion_label
      description: Conversion label
      tests:
          - not_null

    - name: load_id
      description: ID of the load process that ingested this record
      tests:
        - not_null

    - name: platform_name
      description: Name of the platform the conversion belongs to
      tests:
        - not_null
        - accepted_values:
            values: [ 'Google Ads', 'meta_default_conversions_mapping', 'meta_conversions_report' ]

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - conversion_identifier
            - conversion_label

  - name: dim_creative_transform
    description: "Dimension table for creatives"
    columns:
      - name: external_ad_id
        description: external identifier for the ad
        tests:
          - not_null

      - name: external_creative_id
        description: external identifier for the creative
        tests:
          - not_null

      - name: external_ad_group_id
        description: External identifier for the ad group

      - name: external_campaign_id
        description: External identifier for the campaign

      - name: creative_name
        description: name of creative

      - name: load_id
        description: ID of the load process that ingested this record
        tests:
          - not_null

      - name: platform_name
        description: Name of the platform the ad group belongs to
        tests:
          - not_null
          - accepted_values:
              values: [ 'Youtube (DV360)', 'DV360', 'Xandr']

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - external_campaign_id
            - external_ad_group_id
            - external_ad_id
            - external_creative_id
            - platform_name