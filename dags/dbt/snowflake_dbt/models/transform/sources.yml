version: 2

sources:
  - name: load_metadata
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: LOAD_METADATA
    tables:
      - name: load_table
  - name: DATINTELL
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: DATINTELL
    tables:
      - name: campaign_ghostcampaignpricingitem
      - name: campaign_ghostcampaignpricingsubitem
      - name: campaign_campaigncollection
      - name: builder_mode_mediaplanitem
      - name: campaign_campaign
      - name: builder_mode_mediaplan
      - name: campaign_mediamix
      - name: campaign_ghostcampaign

  - name: ADWORDS
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: ADWORDS
    tables:
      - name: external_services_adwordsexternaldata
      - name: campaign_adwordssearchcampaign
      - name: campaign_adwordssearchrelatedcampaign
      - name: campaign_adwordsadgroup
      - name: campaign_adwordsad
      - name: service_responsivesearchadmodel
      - name: service_admodel
      - name: service_responsivedisplayadmodel
      - name: service_discoverycarouseladmodel
      - name: service_discoverymultiassetadmodel
      - name: service_imageadmodel
      - name: service_html5uploadadmodel
      - name: service_dynamichtml5admodel
      - name: service_videoadmodel
      - name: service_expandeddynamictextadmodel
  - name: DV360
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: DV360
    tables:
      - name: dv360_budgetsegment
      - name: dv360_dv360adgroup
      - name: dv360_dv360adgroupad
      - name: dv360_dv360creative
      - name: dv360_dv360displaycampaign
      - name: dv360_dv360insertionorder
      - name: dv360_dv360insertionorder_budget_segments
      - name: dv360_dv360integration
      - name: dv360_dv360lineitem
  - name: FACEBOOK_INSTAGRAM
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: FACEBOOK_INSTAGRAM
    tables:
      - name: campaign_facebookad
      - name: campaign_facebookadsetmodel
      - name: campaign_facebookcampaign
      - name: account_facebookexternaldata
  - name: BING
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: BING
    tables:
      - name: ads
      - name: ad_groups
      - name: campaigns
      - name: integrations
      - name: platform_settings
  - name: ZEMANTA
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: ZEMANTA
    tables:
      - name: ads
      - name: ad_groups
      - name: campaigns
      - name: integrations
      - name: platform_settings
  - name: REPORTS
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: REPORTS
    tables:
      - name: search_campaign_performance
      - name: reports_adwords_ad_performance
      - name: search_campaign_performance_mobile
      - name: reports_adwords_action_mapping
      - name: reports_adwords_ad_conversion_performance
      - name: search_campaign_conversion_performance
      - name: meta_conversions_report
      - name: social_campaigns_reports_v3
      - name: meta_default_conversions_mapping
      - name: meta_conversions_mapping
      - name: dv360_base_reports
      - name: dv360_base_reports_youtube
      - name: microsoft_advertising_performance_ad_level
      - name: microsoft_advertising_estimated_campaign_level_v2
      - name: reports_zemanta_performance_v2
      - name: reports_amazon_performance
      - name: reports_xandr_performance_v3
      - name: reports_adformconversion_v2
      - name: reports_linkedin_performance
  - name: OMNI_CONNECTOR
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: OMNI_CONNECTOR
    tables:
      - name: ads
      - name: ad_groups
      - name: campaigns
      - name: integrations
      - name: tile_settings
  - name: ADFORM
    database: "{{ env_var('STAGE_DB_NAME') }}"
    schema: ADFORM
    tables:
      - name: adform_adformbannermapping
      - name: adform_adformcampaignmapping
      - name: adform_adformintegration
      - name: adform_adformlineitemmapping
      - name: adform_adformmediamapping
      - name: adform_adformmediamapping_campaign_mapping
      - name: adform_adformsettings
      - name: adform_adformtrackingfiltermapping
