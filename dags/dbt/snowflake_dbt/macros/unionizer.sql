{% macro union_models(model_names, ordered_columns) %}
  {%- set unioned_queries = [] -%}

  {%- for model_name in model_names -%}
    {%- set relation = ref(model_name) -%}
    {%- set cols = adapter.get_columns_in_relation(relation) -%}
    {%- set col_names = cols | map(attribute="name") | list -%}

    {%- set select_clause = [] -%}
    {%- for col in ordered_columns -%}
      {%- if col in col_names -%}
        {%- do select_clause.append('  "' ~ col ~ '"') -%}
      {%- else -%}
        {%- do select_clause.append('  NULL AS "' ~ col ~ '"') -%}
      {%- endif -%}
    {%- endfor -%}

    {%- set query = 'SELECT\n' ~ select_clause | join(',\n') ~ '\nFROM ' ~ relation -%}
    {%- do unioned_queries.append(query) -%}
  {%- endfor -%}

  {{ unioned_queries | join('\nUNION ALL\n') }}

{% endmacro %}
