
{% macro historize_dim(feed_table, target_table, key_columns, scd_type='2a') %}

WITH current_data AS (
    SELECT * FROM {{ target_table }}
),
    incoming_data AS (
    SELECT * FROM {{ feed_table }}
),
active_data AS (Select * from (Select * from current_data where is_active=True) where load_id not in  (select load_id from incoming_data))
{#Wasnt properly tested on last version of project#}
{% if scd_type == '1' %}
-- SCD Type 1: Only update the existing records with the latest values
, upsert_records AS (
    SELECT
        inc.*
    FROM incoming_data inc
    LEFT JOIN current_data cur
    ON {% for col in key_columns %}
        inc.{{ col }} = cur.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
)
, unchanged_records AS (
    -- Keep records that are not in the incoming data as they are
    SELECT
        cur.*
    FROM current_data cur
    LEFT JOIN incoming_data inc
    ON {% for col in key_columns %}
        cur.{{ col }} = inc.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
    WHERE inc.{{ key_columns[0] }} IS NULL
)
SELECT * FROM upsert_records
UNION
SELECT * FROM unchanged_records

{% elif scd_type in ['2a', '2b'] %}
-- Common logic for SCD Types 2a and 2b
, updated_records_old_state AS (
    -- update validity of the old records that got an update
    SELECT
        cur.* REPLACE(CURRENT_TIMESTAMP() AS date_to, false AS is_active)
    FROM active_data cur
    JOIN incoming_data inc
    ON {% for col in key_columns %}
        cur.{{ col }} = inc.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
)
, updated_records_new_state AS (
    -- Insert new versions for the changed records
    SELECT
        inc.*,
        {% if scd_type == '2a' %}
            CURRENT_TIMESTAMP() AS date_from,  -- SCD Type 2a: New record starts from current date
        {% elif scd_type == '2b' %}
            cur.date_from AS date_from,  -- SCD Type 2b: New record keeps the original date_from
        {% endif %}
        NULL AS date_to,
        true AS is_active
    FROM active_data cur
    JOIN incoming_data inc
    ON {% for col in key_columns %}
        cur.{{ col }} = inc.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}

)
, unchanged_records AS (
    -- Keep records that have not changed as they are
    SELECT
        cur.*
    FROM active_data cur
    LEFT JOIN incoming_data inc
    ON {% for col in key_columns %}
        cur.{{ col }} = inc.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
    WHERE inc.{{ key_columns[0] }} IS NULL
)
, new_records AS (
    -- Insert entirely new records that do not exist in current_data
    SELECT
        inc.*,
        CURRENT_TIMESTAMP() AS date_from,
        NULL AS date_to,
        true AS is_active
    FROM incoming_data inc
    LEFT JOIN active_data cur
    ON {% for col in key_columns %}
        inc.{{ col }} = cur.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
    WHERE cur.{{ key_columns[0] }} IS NULL
),
        combined_data as (
SELECT * from current_data WHERE is_active != True
UNION ALL
SELECT * FROM updated_records_old_state
UNION ALL
SELECT * FROM updated_records_new_state
UNION ALL
SELECT * FROM new_records
        {#No need in unchanged_records due to dump#}
{#UNION ALL#}
{#SELECT * FROM unchanged_records#}
        )

SELECT * FROM combined_data

{% endif %}
{% endmacro %}

{% macro init_hist_dim_table(feed_table) %}
-- This macro initializes a dimension table with basic historization columns
SELECT

    *,
    CURRENT_TIMESTAMP() AS date_from,
    NULL AS date_to,
    true AS is_active
FROM {{ feed_table }}
{% endmacro %}
