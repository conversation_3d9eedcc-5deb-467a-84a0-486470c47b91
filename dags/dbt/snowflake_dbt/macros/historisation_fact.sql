
{% macro historize_fact(feed_table, target_table, key_columns) %}
{#    Not sure that checking var is needed anymore#}
{% if var('tests', default=False) %}
  {% set current_time = "TO_TIMESTAMP_NTZ('" ~ var('mock_timestamp_value') ~ "')" %}
{% else %}
  {% set current_time = "CURRENT_TIMESTAMP()" %}
{% endif %}

WITH current_data AS (
    SELECT * FROM {{ target_table }}
),
    incoming_data AS (
    SELECT * FROM {{ feed_table }}
),
active_data AS (Select * from (Select * from current_data where is_active=True) where load_id not in  (select load_id from incoming_data)),
-- Identify records that need to be versioned (i.e., create new versions)
updated_records_old_state AS (
    -- Mark the old version as inactive
    SELECT
        cur.* REPLACE(false AS is_active)
    FROM incoming_data inc
    JOIN active_data cur
    ON {% for col in key_columns %}
        inc.{{ col }} IS NOT DISTINCT FROM cur.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
),
-- Create new versions of the updated records
updated_records_new_state AS (
    -- Insert the new version with updated values from incoming_data
    SELECT
        inc.*,
        {{ current_time }} AS updated_at,
        true AS is_active
    FROM incoming_data inc
    JOIN active_data cur
    ON {% for col in key_columns %}
        inc.{{ col }} IS NOT DISTINCT FROM cur.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
),
-- Identify new records that are not in current_data
new_records AS (
    -- Insert new records that are not present in current_data
    SELECT
        inc.*,
        {{ current_time }} AS updated_at,
        true AS is_active
    FROM incoming_data inc
    LEFT JOIN active_data cur
    ON {% for col in key_columns %}
        inc.{{ col }} IS NOT DISTINCT FROM cur.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
    WHERE cur.{{ key_columns[0] }} IS NULL
),
unchanged_records AS (
    SELECT
        cur.*
    FROM active_data cur
    LEFT JOIN incoming_data inc
    ON {% for col in key_columns %}
        cur.{{ col }} IS NOT DISTINCT FROM inc.{{ col }}{% if not loop.last %} AND {% endif %}
    {% endfor %}
    WHERE inc.{{ key_columns[0] }} IS NULL
)
-- Combine all records: versioned, new versions, new records, and inactive records

Select * from current_data where is_active != True
UNION ALL
SELECT * FROM updated_records_old_state
UNION ALL
SELECT * FROM updated_records_new_state
UNION ALL
SELECT * FROM new_records
{#No need in unchanged_records due to dump#}
{#UNION ALL#}
{#SELECT * FROM unchanged_records#}


{% endmacro %}

{% macro init_hist_fact_table(feed_table) %}
-- This macro initializes a dimension table with basic historization columns
{#    Not sure that checking var is needed anymore#}
    {% if var('tests', default=False) %}
  {% set current_time = "TO_TIMESTAMP_NTZ('" ~ var('mock_timestamp_value') ~ "')" %}
{% else %}
  {% set current_time = "CURRENT_TIMESTAMP()" %}
{% endif %}
SELECT

    *,
    {{ current_time }} AS updated_at,
    true AS is_active
FROM {{ feed_table }}
{% endmacro %}
