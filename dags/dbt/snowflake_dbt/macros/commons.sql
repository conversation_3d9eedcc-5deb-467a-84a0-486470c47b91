{% macro get_last_finished_run_id(db_name, table_name, row_num) %}
-- {# Function to select last run_id for stage table #}
(
    SELECT "RUN_ID" FROM (
        SELECT
            "RUN_ID",
            "TIMESTAMP",
            ROW_NUMBER() OVER (
                PARTITION BY "DB_NAME", "TABLE_NAME"
                ORDER BY "TIMESTAMP" DESC
            ) AS "row_num"
        FROM {{ source('load_metadata', 'load_table') }}
        WHERE
            "DB_NAME" = '{{ db_name }}' AND
            "TABLE_NAME" = '{{ table_name }}' AND
            "STATUS" = 'FINISHED' AND
            "ENTRY_NAME" = 'DATA_UPLOAD'
    )
    WHERE "row_num" = {{ row_num }}
)
{% endmacro %}

-- {#Not sure is still used, this is overwrite of default dbt macro so there is no direct usage in project code#}
{% macro generate_schema_name(custom_schema_name, node) -%}

    {%- set default_schema = target.schema -%}
    {%- if custom_schema_name is none -%}

        {{ default_schema }}

    {%- else -%}

        {{ custom_schema_name | trim }}

    {%- endif -%}

{%- endmacro %}

{#Need to find easier way to check if source exists#}
{% macro source_exists(source_name, table_name) %}
    {% if execute %}
        {% set result = run_query(
            "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '{{ source_name }}' AND TABLE_NAME = '{{ table_name }}'"
        ).columns[0][0] %}
        {{ return(result > 0) }}
    {% else %}
        {{ return(false) }}
    {% endif %}
{% endmacro %}

{% macro filter_active_records(source_name, package_name=None) %}
(
    SELECT *
    FROM {{ ref(source_name) }}
    WHERE is_active = TRUE
    {% if package_name is not none %}
        AND platform_name = '{{ package_name }}'
    {% endif %}
)
{% endmacro %}

{% macro safe_source(source_name, table_name) %}
  {% if execute %}
    {# Build a Relation from your source() call #}
    {% set src = source(source_name, table_name) %}
    {% set exists = adapter.get_relation(src.database, src.schema, src.identifier) %}
    {% if exists %}
      {{ return(src) }}
    {% endif %}
  {% endif %}
  {{ return(none) }}
{% endmacro %}


{% macro resolve_table(schema, table) -%}
    {% set is_test = var('is_test', false) %}
    {% if is_test %}
        {{ ref(('mock_' ~ schema ~ '_' ~ table) | lower) }}
    {% else %}
        {{ source(schema | upper, table) }}

    {% endif %}
{%- endmacro %}

{% macro filtered_source(schema, table) -%}
    {% set from_table = resolve_table(schema, table) %}
    (
    SELECT *
    FROM {{ from_table }}
    WHERE run_id = {{ get_last_finished_run_id(schema | upper, table, 1) }}
    )
{%- endmacro %}

{% macro normalize_columns(columns) %}
    {{ return(columns | map('lower') | sort) }}
{% endmacro %}

{% macro is_subset(subset_list, full_list) %}
    {% set is_subset = true %}
    {% for item in subset_list %}
        {% if item not in full_list %}
            {% set is_subset = false %}
        {% endif %}
    {% endfor %}
    {{ is_subset }}
{% endmacro %}

{% macro test_compare_staging_datamart(model, compare_with, source_filter) %}

{% set datamart_relation = model %}
{% set staging_relation = ref(compare_with) %}

{% set datamart_count = get_row_count(datamart_relation, source_filter) %}
{% set staging_count = get_row_count(staging_relation) %}

{% if datamart_count != staging_count %}
    {{ exceptions.raise_compiler_error(
        "Row counts do not match between " ~ datamart_relation ~ " and " ~ staging_relation
        ~ "\n Datamart count (where " ~ source_filter ~ "): " ~ datamart_count
        ~ "\n Staging count: " ~ staging_count

    ) }}
{% endif %}

{% set datamart_columns = adapter.get_columns_in_relation(datamart_relation)
    | map(attribute='name')
    | reject('equalto', '_DBT_SOURCE_RELATION')
    | sort
%}
{% set staging_columns = adapter.get_columns_in_relation(staging_relation)
    | map(attribute='name')
    | reject('equalto', '_DBT_SOURCE_RELATION')
    | sort
%}

{% set datamart_columns_lower = normalize_columns(datamart_columns) %}
{% set staging_columns_lower = normalize_columns(staging_columns) %}

{% if not is_subset(staging_columns_lower, datamart_columns_lower) %}
    {{ exceptions.raise_compiler_error(
        "Column mismatch between datamart and staging. "
        ~ "Datamart columns: " ~ datamart_columns_lower
        ~ " Staging columns: " ~ staging_columns_lower
    )}}
{% endif %}

-- Identify columns present in both
{% set common_columns = [] %}
{% for col in datamart_columns %}
    {% if col | lower in (staging_columns | map('lower') | list) %}
        {% do common_columns.append(col) %}
    {% endif %}
{% endfor %}

-- List numeric columns for epsilon comparison
{% set numeric_columns = [
    'impressions','clicks','spend','platform_conversions','video_start','video_views',
    'completed_views_first','completed_views_mid','completed_views_third','completed_views_full',
    'platform_installations','in_app_actions','search_impression_share','total_eligible_impressions',
    'conversions', 'clicks_all', 'thruplays', 'landing_page_views', 'page_likes', 'post_comments',
    'post_engagement', 'post_reaction', 'post_saves', 'conversions_revenue'
    'conversions', 'trueviews', 'platform_active_view_viewable_impressions', 'platform_conversions'
] %}

-- Epsilon threshold
{% set epsilon = 0.000001 %}

with datamart_data as (
    select
        {% for col in common_columns %}
            "{{ col }}"
            {% if not loop.last %},{% endif %}
        {% endfor %}
    from {{ datamart_relation }}
    where {{ source_filter }}
),

staging_data as (
    select
        {% for col in common_columns %}
            {% set staging_col = col | lower %}
            "{{ staging_col }}"
            {% if not loop.last %},{% endif %}
        {% endfor %}
    from {{ staging_relation }}
),

datamart_only as (
    select d.*
    from datamart_data d
    left join staging_data s
        on
        {% for col in common_columns %}
            {% set staging_col = col | lower %}
            (
                {% if col | lower in numeric_columns %}
                    abs(
                        coalesce(cast(d.{{ col }} as decimal(38,10)), 0)
                        - coalesce(cast(s."{{ staging_col }}" as decimal(38,10)), 0)
                    ) < {{ epsilon }}
                {% else %}
                    d.{{ col }} is not distinct from s."{{ staging_col }}"
                {% endif %}
            )
            {% if not loop.last %} and {% endif %}
        {% endfor %}
    where s."date" is null
),

/*
    2) "staging_only": Rows in staging with no matching row in datamart.
*/
staging_only as (
    select s.*
    from staging_data s
    left join datamart_data d
        on
        {% for col in common_columns %}
            {% set staging_col = col | lower %}
            (
                {% if col | lower in numeric_columns %}
                    abs(
                        coalesce(cast(d.{{ col }} as decimal(38,10)), 0)
                        - coalesce(cast(s."{{ staging_col }}" as decimal(38,10)), 0)
                    ) < {{ epsilon }}
                {% else %}
                    d.{{ col }} is not distinct from s."{{ staging_col }}"
                {% endif %}
            )
            {% if not loop.last %} and {% endif %}
        {% endfor %}
    where d.date is null
)

select
    'datamart_only' as source,
    *
from datamart_only

union all

select
    'staging_only' as source,
    *
from staging_only

{% endmacro %}

{% macro unpivot_table(source_table_name, metric_names, existing_columns, required_columns) %}
SELECT
    {% for col_name in required_columns %}
        {% if col_name not in existing_columns %}
            NULL AS {{ col_name }},
        {% else %}
            {{ col_name }},
        {% endif %}
    {% endfor %}
    metric_name AS fact_name,
    CAST(SUM(metric_value) AS FLOAT) AS fact_value
FROM {{ source_table_name }}
UNPIVOT INCLUDE NULLS (
    metric_value FOR metric_name IN (
        {% for col in metric_names %}
            {{ col }}{% if not loop.last %}, {% endif %}
        {% endfor %}
    )
) AS unpvt
{#Grouping might not be necessary for every fact that comes in#}
GROUP BY
    -- group by any existing columns you want to preserve
    {% for col_name in required_columns %}
        {% if col_name in existing_columns %}
            {{ col_name}},
        {% endif %}
    {% endfor %}
    metric_name
{% endmacro %}


{% macro get_druid_required_colunms() %}
    {{ return([
    "fact_date",
    "external_campaign_id",
    "external_ad_group_id",
    "external_ad_id",
    "external_creative_id",
    "conversion_identifier",
    "load_id",
    "platform_name",
    "currency_code",
    'include_in_conversions_metric'
    ]) }}
{% endmacro %}


{% macro get_row_count(model, where_clause=None) -%}
    {%- set filter_sql = where_clause and "WHERE " ~ where_clause or "" -%}


    {%- set query %}
        select count(*) from {{ model }} {{ filter_sql }}
    {%- endset -%}

    {{ return(dbt_utils.get_single_value(query)) }}
{%- endmacro %}

-- {#grouping of performance metrics indicating in mapping#}
{% macro group_performance_data(fact_table, group_columns, fact_columns, fact_mapping={}, where_clause=None) %}
  {% set case_statements = [] %}

-- {# Generate renamed fields based on fact_mapping - for columns that should be renamed #}
  {% if fact_mapping %}
    {% for fact_name, alias_name in fact_mapping.items() %}
      {% set line -%}
CAST(SUM(
  CASE WHEN fact_name = '{{ fact_name }}' THEN fact_value ELSE 0 END
), numeric(38,10)) AS {{ alias_name }}
      {%- endset %}
      {% do case_statements.append(line) %}
    {% endfor %}
  {% endif %}

-- {# Generate lowercase fields based on fact_columns - for columns that should be just lowercased without renaming#}
  {% if fact_columns %}
    {% for fact in fact_columns %}
      {% set alias_name = fact | lower %}
      {% set line -%}
SUM(
  CASE WHEN fact_name = '{{ fact }}' THEN fact_value ELSE 0 END
) AS {{ alias_name }}
      {%- endset %}
      {% do case_statements.append(line) %}
    {% endfor %}
  {% endif %}

  {% set full_select      = (group_columns + case_statements) | join(',\n    ') %}
  {% set group_by_clause  = group_columns             | join(', ') %}

  select
    {{ full_select }}
  from {{ fact_table }} perf
  {% if where_clause is not none %}
    {{ where_clause }}
  {% endif %}
  group by {{ group_by_clause }}
{% endmacro %}



{% macro mint_virtual_taxonomy_query() %}
SELECT
  tx.company_id,
  tx.journey_id,
  tx.journey_name,
  tx.media_plan_id,
  tx.media_plan_name,
  tx.channel_id,
  tx.channel_name,
  tx.platform_id,
  tx.platform_name,
  tx.tile_id,
  tx.tile_name,
  tx.media_row_id,
  tx.media_row_name,
  tx.mediarow_currency,
  tx.media_row_end_date,
  tx.media_row_start_date,
  vpi."pricing_item_id" AS pricing_item_id,
  vpi."pricing_item_date_from" AS pricing_item_date_from,
  vpi."pricing_item_date_to" AS pricing_item_date_to,
  vpi."pricing_item_type" AS pricing_item_type,
  vpi."pricing_item_name" AS pricing_item_name,
  vpi."pricing_item_campaign_id" AS pricing_item_campaign_id,
  vct.ghost_campaign_id AS pricing_item_external_campaign_id,
  vpsi."sub_pricing_date" AS sub_pricing_date,
  vpsi."price" AS price
FROM
  ({{ mint_taxonomy_query() }}) AS tx
JOIN {{ filter_active_records('dim_ghost_campaigns_core') }} vct
  ON tx.tile_id = vct.ghost_campaign_id
JOIN {{ filter_active_records('dim_ghost_pricing_items_core') }} vpi
  ON vct.gc_id = vpi."pricing_item_campaign_id"
LEFT JOIN {{ filter_active_records('dim_ghost_pricing_subitems_core') }} vpsi
 ON vpi."pricing_item_id" = vpsi."pricing_item_id"
{% endmacro %}
