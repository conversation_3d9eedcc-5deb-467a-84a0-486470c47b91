{% macro adform_mappings() %}
SELECT DISTINCT
  I."company_id" AS company_id,
  I."integration_id" AS adform_integration_id,
  I."name" AS adform_integration_name,
  I."advertiser_id" AS adform_advertiser_id,
  I."settings_id" AS adform_settings_id,
  I."journey_id" AS adform_journey_id,

  CM."internal_campaign_id" AS adform_campaign_internal_id,
  CM."external_campaign_id" AS adform_campaign_external_id,
  CM."channel_name" AS adform_campaign_channel_name,
  CM."campaign_name" AS adform_campaign_name,
  CM."platform_item_id" AS campaign_level_adform_platform_item_id,
  CM."campaign_platform_name" AS adform_campaign_platform_name,

  CASE
    WHEN CM."campaign_platform_name" IS NOT NULL THEN true
    ELSE false
  END AS adform_is_direct_campaign_join,

  MM."internal_media_id" AS adform_media_internal_id,
  MM."external_media_id" AS adform_media_external_id,
  MM."media_platform_name" AS adform_media_platform_name,
  MM."media_name" AS adform_media_name,

  LIM."internal_line_item_id" AS adform_line_item_internal_id,
  LIM."external_line_item_id" AS adform_line_item_external_id,
  LIM."line_item_name" AS adform_line_item_name,
  LIM."tile_id" AS adform_tile_id,

  BM."internal_banner_id" AS adform_banner_internal_id,
  BM."external_banner_id" AS adform_banner_external_id,
  BM."banner_name" AS adform_banner_name,
  BM."external_tag_id" AS adform_tag_external_id,
  CAST(BM."platform_item_id" AS TEXT) AS adform_platform_item_id,
  CAST(BM."platform_item_external_id" AS TEXT) AS adform_platform_item_external_id,

  TM."internal_tracking_filter_id" AS adform_tracker_internal_id,
  TM."external_tracking_filter_id" AS adform_tracker_external_id,
  TM."tracking_filter_name" AS adform_tracker_name,
  TM."conversion_metric" AS adform_tracker_conversion_metric_label,
  TM."cost_based_metric" AS adform_tracker_conversion_cost_based_metric_label,

  CASE
    WHEN BM."external_tag_id" IS NOT NULL
      OR CM."campaign_name" IS NOT NULL THEN true
    ELSE false
  END AS adfrom_is_mappings_complete

FROM {{ filter_active_records('dim_adform_integrations_core') }} AS I
LEFT JOIN {{ filter_active_records('dim_adform_tracker_core') }} AS TM
  ON I."settings_id" = TM."settings_id"
LEFT JOIN {{ filter_active_records('dim_adform_campaign_core') }} AS CM
  ON I."settings_id" = CM."settings_id"
LEFT JOIN {{ filter_active_records('bridge_adform_media_campaign_mapping_core') }} AS MCA
  ON CM."internal_campaign_id" = MCA."adformcampaignmapping_id"
LEFT JOIN {{ filter_active_records('dim_adform_media_core') }} AS MM
  ON MM."internal_media_id" = MCA."adformmediamapping_id"
LEFT JOIN {{ filter_active_records('dim_adform_line_item_core') }} AS LIM
  ON LIM."media_mapping_id" = MM."internal_media_id"
  AND LIM."campaign_mapping_id" = CM."internal_campaign_id"
LEFT JOIN {{ filter_active_records('dim_adform_banner_core') }} AS BM
  ON LIM."internal_line_item_id" = BM."line_item_mapping_id"
{% endmacro %}


