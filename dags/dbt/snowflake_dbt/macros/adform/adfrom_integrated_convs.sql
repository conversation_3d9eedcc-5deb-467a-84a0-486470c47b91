{% macro adform_integrated_convs_package_model(package_name) %}
-- Returns unified Adform performance data for a given platform package
WITH
    mint_taxonomy AS (
        {{ mint_taxonomy_query() }}
    ),

    adform_reports AS (
        {{
            group_performance_data(
                filter_active_records('fact_performance_data_adform_core'),
                group_columns=[
                    "fact_date",
                    "external_campaign_id",
                    "advertiser_id",
                    "external_tracking_filter_id",
                    "external_tag_id"
                ],
                fact_columns=[
                    "CONVERSIONS",
                    "POST_IMPRESSIONS",
                    "POST_CLICKS"
                ]
            )
        }}
    ),

    adform_mappings AS (
        {{ adform_mappings() }}
    ),

    taxonomy_data AS (
    SELECT * FROM (
        {{ unified_package_taxonomy_query(package_name, "campaign") }}
    )
    UNION ALL
    SELECT * FROM (
        {{ unified_package_taxonomy_query(package_name, "ad_group") }}
    )
    UNION ALL
    SELECT * FROM (
        {{ unified_package_taxonomy_query(package_name, "ad") }}
    )
    ),

    campaign_level AS (
        SELECT DISTINCT
            tx.company_id,
            tx.journey_id,
            tx.journey_name,
            tx.media_plan_id,
            tx.media_plan_name,
            tx.channel_id,
            tx.channel_name,
            tx.platform_id,
            tx.platform_name,
            tx.tile_id,
            tx.tile_name,
            tx.media_row_id,
            tx.media_row_name,
            tax.platform_campaign_external_id AS platform_campaign_id,
            tax.platform_campaign_name,
            tax.platform_campaign_external_group_id AS platform_campaign_group_id,
            tax.platform_campaign_group_name,
            tax.platform_ad_external_id AS ad_asset_id,
            tax.platform_ad_name AS ad_asset,
            amap.adform_advertiser_id AS adserver_advertiser_id,
            amap.adform_campaign_external_id AS adserver_level_1_id,
            amap.adform_campaign_name AS adserver_level_1_name,
            amap.adform_media_external_id AS adserver_level_2_id,
            amap.adform_media_name AS adserver_level_2_name,
            amap.adform_line_item_external_id AS adserver_level_3_id,
            amap.adform_line_item_name AS adserver_level_3_name,
            amap.adform_banner_external_id AS adserver_level_4_id,
            amap.adform_banner_name AS adserver_level_4_name,
            amap.adform_tag_external_id AS adserver_level_5_id,
            amap.adform_tracker_external_id AS adserver_level_6_id,
            amap.adform_tracker_name AS adserver_level_6_name,
            tax.currency,
            amap.adform_tracker_conversion_metric_label AS conversion_metric,
            amap.adform_tracker_conversion_cost_based_metric_label AS cost_based_metric,
            '{{ constants("ADFORM") }}' AS data_source,
            '{{ constants("ADFORM_PERFORMANCE_REPORTS_TABLE") }}' AS data_source_details,
            arep.fact_date AS date,
            arep.CONVERSIONS AS conversions,
            arep.POST_IMPRESSIONS AS view_through_conversions,
            arep.POST_CLICKS AS click_through_conversions
        FROM mint_taxonomy tx
        JOIN taxonomy_data tax ON tx.tile_id = tax.tile_id
        JOIN adform_mappings amap
          ON tax.platform_campaign_internal_id = amap.adform_platform_item_id
         AND tax.platform_campaign_external_id = amap.adform_platform_item_external_id
        JOIN adform_reports arep
          ON amap.adform_advertiser_id = arep.advertiser_id
         AND amap.adform_campaign_external_id = arep.external_campaign_id
         AND amap.adform_tag_external_id = arep.external_tag_id
         AND amap.adform_tracker_external_id = arep.external_tracking_filter_id
        WHERE arep.fact_date BETWEEN tx.media_row_start_date AND tx.media_row_end_date
    ),

    adgroup_level AS (
        SELECT DISTINCT
            tx.company_id,
            tx.journey_id,
            tx.journey_name,
            tx.media_plan_id,
            tx.media_plan_name,
            tx.channel_id,
            tx.channel_name,
            tx.platform_id,
            tx.platform_name,
            tx.tile_id,
            tx.tile_name,
            tx.media_row_id,
            tx.media_row_name,
            tax.platform_campaign_external_id AS platform_campaign_id,
            tax.platform_campaign_name,
            tax.platform_campaign_external_group_id AS platform_campaign_group_id,
            tax.platform_campaign_group_name,
            tax.platform_ad_external_id AS ad_asset_id,
            tax.platform_ad_name AS ad_asset,
            amap.adform_advertiser_id AS adserver_advertiser_id,
            amap.adform_campaign_external_id AS adserver_level_1_id,
            amap.adform_campaign_name AS adserver_level_1_name,
            amap.adform_media_external_id AS adserver_level_2_id,
            amap.adform_media_name AS adserver_level_2_name,
            amap.adform_line_item_external_id AS adserver_level_3_id,
            amap.adform_line_item_name AS adserver_level_3_name,
            amap.adform_banner_external_id AS adserver_level_4_id,
            amap.adform_banner_name AS adserver_level_4_name,
            amap.adform_tag_external_id AS adserver_level_5_id,
            amap.adform_tracker_external_id AS adserver_level_6_id,
            amap.adform_tracker_name AS adserver_level_6_name,
            tax.currency,
            amap.adform_tracker_conversion_metric_label AS conversion_metric,
            amap.adform_tracker_conversion_cost_based_metric_label AS cost_based_metric,
            '{{ constants("ADFORM") }}' AS data_source,
            '{{ constants("ADFORM_PERFORMANCE_REPORTS_TABLE") }}' AS data_source_details,
            arep.fact_date AS date,
            arep.CONVERSIONS AS conversions,
            arep.POST_IMPRESSIONS AS view_through_conversions,
            arep.POST_CLICKS AS click_through_conversions
        FROM mint_taxonomy tx
        JOIN taxonomy_data tax ON tx.tile_id = tax.tile_id
        JOIN adform_mappings amap
          ON tax.platform_campaign_internal_group_id = amap.adform_platform_item_id
         AND tax.platform_campaign_external_group_id = amap.adform_platform_item_external_id
        JOIN adform_reports arep
          ON amap.adform_advertiser_id = arep.advertiser_id
         AND amap.adform_campaign_external_id = arep.external_campaign_id
         AND amap.adform_tag_external_id = arep.external_tag_id
         AND amap.adform_tracker_external_id = arep.external_tracking_filter_id
        WHERE arep.fact_date BETWEEN tx.media_row_start_date AND tx.media_row_end_date
    ),

    ad_level AS (
        SELECT DISTINCT
            tx.company_id,
            tx.journey_id,
            tx.journey_name,
            tx.media_plan_id,
            tx.media_plan_name,
            tx.channel_id,
            tx.channel_name,
            tx.platform_id,
            tx.platform_name,
            tx.tile_id,
            tx.tile_name,
            tx.media_row_id,
            tx.media_row_name,
            tax.platform_campaign_external_id AS platform_campaign_id,
            tax.platform_campaign_name,
            tax.platform_campaign_external_group_id AS platform_campaign_group_id,
            tax.platform_campaign_group_name,
            tax.platform_ad_external_id AS ad_asset_id,
            tax.platform_ad_name AS ad_asset,
            amap.adform_advertiser_id AS adserver_advertiser_id,
            amap.adform_campaign_external_id AS adserver_level_1_id,
            amap.adform_campaign_name AS adserver_level_1_name,
            amap.adform_media_external_id AS adserver_level_2_id,
            amap.adform_media_name AS adserver_level_2_name,
            amap.adform_line_item_external_id AS adserver_level_3_id,
            amap.adform_line_item_name AS adserver_level_3_name,
            amap.adform_banner_external_id AS adserver_level_4_id,
            amap.adform_banner_name AS adserver_level_4_name,
            amap.adform_tag_external_id AS adserver_level_5_id,
            amap.adform_tracker_external_id AS adserver_level_6_id,
            amap.adform_tracker_name AS adserver_level_6_name,
            tax.currency,
            amap.adform_tracker_conversion_metric_label AS conversion_metric,
            amap.adform_tracker_conversion_cost_based_metric_label AS cost_based_metric,
            '{{ constants("ADFORM") }}' AS data_source,
            '{{ constants("ADFORM_PERFORMANCE_REPORTS_TABLE") }}' AS data_source_details,
            arep.fact_date AS date,
            arep.CONVERSIONS AS conversions,
            arep.POST_IMPRESSIONS AS view_through_conversions,
            arep.POST_CLICKS AS click_through_conversions
        FROM mint_taxonomy tx
        JOIN taxonomy_data tax ON tx.tile_id = tax.tile_id
        JOIN adform_mappings amap
          ON tax.platform_ad_internal_id = amap.adform_platform_item_id
         AND tax.platform_ad_external_id = amap.adform_platform_item_external_id
        JOIN adform_reports arep
          ON amap.adform_advertiser_id = arep.advertiser_id
         AND amap.adform_campaign_external_id = arep.external_campaign_id
         AND amap.adform_tag_external_id = arep.external_tag_id
         AND amap.adform_tracker_external_id = arep.external_tracking_filter_id
        WHERE arep.fact_date BETWEEN tx.media_row_start_date AND tx.media_row_end_date
    )

SELECT * FROM campaign_level
UNION ALL
SELECT * FROM adgroup_level
UNION ALL
SELECT * FROM ad_level
{% endmacro %}