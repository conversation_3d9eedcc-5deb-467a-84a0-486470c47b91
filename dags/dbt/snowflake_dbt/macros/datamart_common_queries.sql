{% macro mint_taxonomy_query() %}
SELECT
  j.company_id,
  j.journey_id,
  j.journey_name,
  mp.media_plan_id,
  mp.media_plan_name,
  ch.channel_id,
  ch.channel_name,
  p.platform_id,
  p.platform_name,
  dim_tile.tile_id AS tile_id,
  dim_tile.tile_name AS tile_name,
  mr.media_row_id,
  mr.media_row_name,
  COALESCE(mr.media_row_currency, j.journey_currency) AS mediarow_currency,
  mr.media_row_end_date,
  mr.media_row_start_date,
FROM {{ filter_active_records('dim_journey_core') }} j
JOIN {{ filter_active_records('bridge_media_plan_journey_core') }} bmp
  ON bmp.journey_id = j.journey_id
JOIN {{ filter_active_records('dim_media_plan_core') }} mp
  ON mp.media_plan_id = bmp.media_plan_id
JOIN {{ filter_active_records('bridge_channel_media_plan_core') }} bpc
  ON bpc.media_plan_id = mp.media_plan_id
JOIN {{ filter_active_records('dim_channel_core') }} ch
  ON ch.channel_id = bpc.channel_id
JOIN {{ filter_active_records('bridge_platform_channel_core') }} bcp
  ON bcp.channel_id = ch.channel_id
JOIN {{ filter_active_records('dim_platform_core') }} p
  ON p.platform_id = bcp.platform_id
JOIN {{ filter_active_records('bridge_media_row_platform_core') }} bpcamp
  ON bpcamp.platform_id = p.platform_id
JOIN {{ filter_active_records('dim_media_row_core') }} mr
  ON mr.media_row_id = bpcamp.media_row_id
JOIN {{ filter_active_records('dim_tile_core') }} dim_tile
  ON mr.tile_id = dim_tile.tile_id
WHERE j.is_deleted = False and j.is_for_test = False
{% endmacro %}


{% macro package_taxonomy_query(package_name) %}
SELECT
    dim_tile.tile_id,
    camp.campaign_currency,
    camp.external_campaign_id,
    camp.campaign_name,
    camp.platform_name,
    camp.internal_campaign_id,
    adgrp.external_ad_group_id,
    adgrp.internal_ad_group_id,
    adgrp.ad_group_name,
    ad.internal_ad_id,
    ad.external_ad_id,
    ad.ad_name
FROM {{ filter_active_records('dim_tile_core') }} dim_tile
    JOIN {{ filter_active_records('bridge_tile_external_campaign_core', package_name) }} bridge_tile_external_campaign
        ON dim_tile.tile_id = bridge_tile_external_campaign.tile_id
    JOIN {{ filter_active_records('dim_external_campaign_core', package_name) }} camp
        ON bridge_tile_external_campaign.internal_campaign_id = camp.internal_campaign_id
    LEFT  JOIN {{ filter_active_records('bridge_ad_group_external_campaign_core', package_name) }} bcag
        ON bcag.internal_campaign_id = camp.internal_campaign_id
    LEFT  JOIN {{ filter_active_records('dim_ad_group_core', package_name) }} adgrp
        ON adgrp.internal_ad_group_id = bcag.internal_ad_group_id
    LEFT  JOIN {{ filter_active_records('bridge_ad_ad_group_core', package_name) }} baat
        ON baat.internal_ad_group_id = adgrp.internal_ad_group_id
    LEFT  JOIN {{ filter_active_records('dim_ad_core', package_name) }} ad
        ON ad.surrogate_key = baat.surrogate_key
{% endmacro %}

{% macro unified_package_taxonomy_query(package_name, level) %}
    {#
        Returns a normalized set of taxonomy fields across campaign, ad_group, and ad levels
        from a given marketing data package.

        Arguments:
        - package_name (string): The identifier used to resolve the taxonomy source via `constants(package_name)`
        - level (string): One of 'campaign', 'ad_group', or 'ad'. This controls filtering and column nullability.

        Output:
        - A SELECT query with a consistent set of columns:
            - tile_id
            - platform_campaign_internal_id
            - platform_campaign_external_id
            - platform_campaign_name
            - currency
            - platform_campaign_external_group_id
            - platform_campaign_internal_group_id
            - platform_campaign_group_name
            - platform_ad_internal_id
            - platform_ad_name

        Notes:
        - For lower levels (e.g., campaign), columns related to ad_group or ad will return NULLs.
        - Filtering ensures only rows relevant to the requested level are returned.
    #}

    SELECT DISTINCT
        tile_id,
        internal_campaign_id AS platform_campaign_internal_id,
        external_campaign_id AS platform_campaign_external_id,
        campaign_name AS platform_campaign_name,
        campaign_currency AS currency,

        {% if level in ['ad_group', 'ad'] %}
            external_ad_group_id AS platform_campaign_external_group_id,
            internal_ad_group_id AS platform_campaign_internal_group_id,
            ad_group_name AS platform_campaign_group_name,
        {% else %}
            NULL AS platform_campaign_external_group_id,
            NULL AS platform_campaign_internal_group_id,
            NULL AS platform_campaign_group_name,
        {% endif %}

        {% if level == 'ad' %}
            external_ad_id AS platform_ad_external_id,
            internal_ad_id AS platform_ad_internal_id,
            ad_name AS platform_ad_name
        {% else %}
            NULL AS platform_ad_external_id,
            NULL AS platform_ad_internal_id,
            NULL AS platform_ad_name
        {% endif %}

    FROM (
        {{ package_taxonomy_query(constants(package_name)) }}
    )

    WHERE
        {% if level == 'campaign' %}
            external_campaign_id IS NOT NULL
        {% elif level == 'ad_group' %}
            external_ad_group_id IS NOT NULL
        {% elif level == 'ad' %}
            external_ad_id IS NOT NULL
        {% else %}
            FALSE -- invalid level
        {% endif %}
{% endmacro %}


{% macro google_ad_assets() %}
    SELECT ca."id"          AS "internal_ad_id", ca."external_id" AS "external_ad_id",
    ca."ad_group_id",
    ca."headline_part1"::text AS "ad_name",
    ca.run_id,
    FROM {{ filtered_source('adwords', 'campaign_adwordsad') }} ca
    {#Probably there is a way to not just create duplicates#}
    UNION

    SELECT rs."id"          AS "internal_ad_id",
    rs."external_id" AS "external_ad_id",
    rs."ad_group_id",
    COALESCE(PARSE_JSON(rs."headlines")[1]::TEXT, PARSE_JSON(rs."headlines")[0]::TEXT) AS "ad_name",
    rs.run_id,

    FROM {{ filtered_source('adwords', 'service_responsivesearchadmodel') }} rs

    UNION

    SELECT sa."id"          AS "internal_ad_id",
    sa."external_id" AS "external_ad_id",
    sa."ad_group_id",
    vd."name"::text AS "ad_name",
    sa.run_id,

    FROM {{ filtered_source('adwords', 'service_admodel') }} sa
             JOIN {{ filtered_source('adwords', 'service_videoadmodel') }} vd
                  ON sa."id" = vd."ad_id"

    UNION

    SELECT sa."id"          AS "internal_ad_id",
    sa."external_id" AS "external_ad_id",
    sa."ad_group_id",
    COALESCE(PARSE_JSON(rd."headlines")[1]::TEXT, PARSE_JSON(rd."headlines")[0]::TEXT) AS "ad_name",
    sa.run_id,

    FROM {{ filtered_source('adwords', 'service_admodel') }} sa
             JOIN {{ filtered_source('adwords', 'service_responsivedisplayadmodel') }} rd
                  ON sa."id" = rd."ad_id"

    UNION

    SELECT sa."id"          AS "internal_ad_id",
    sa."external_id" AS "external_ad_id",
    sa."ad_group_id",
    dc."headline"::text AS "ad_name",
    sa.run_id,

    FROM {{ filtered_source('adwords', 'service_admodel') }} sa
             JOIN {{ filtered_source('adwords', 'service_discoverycarouseladmodel') }} dc
                  ON sa."id" = dc."ad_id"

    UNION

    SELECT sa."id"          AS "internal_ad_id",
    sa."external_id" AS "external_ad_id",
    sa."ad_group_id",
    COALESCE(PARSE_JSON(dm."headlines")[1]::TEXT, PARSE_JSON(dm."headlines")[0]::TEXT) AS "ad_name",
    sa.run_id,

    FROM {{ filtered_source('adwords', 'service_admodel') }} sa
             JOIN {{ filtered_source('adwords', 'service_discoverymultiassetadmodel') }} dm
                  ON sa."id" = dm."ad_id"

    UNION

    SELECT sa."id"          AS "internal_ad_id",
    sa."external_id" AS "external_ad_id",
    sa."ad_group_id",
    im."name"::text AS "ad_name",
    sa.run_id,

    FROM {{ filtered_source('adwords', 'service_admodel') }} sa
             JOIN {{ filtered_source('adwords', 'service_imageadmodel') }} im
                  ON sa."id" = im."ad_id"

    UNION

    SELECT sa."id"          AS "internal_ad_id",
    sa."external_id" AS "external_ad_id",
    sa."ad_group_id",
    h5."name"::text AS "ad_name",
    sa.run_id,

    FROM {{ filtered_source('adwords', 'service_admodel') }} sa
             JOIN {{ filtered_source('adwords', 'service_html5uploadadmodel') }} h5
                  ON sa."id" = h5."ad_id"

    UNION

    SELECT sa."id"          AS "internal_ad_id",
    sa."external_id" AS "external_ad_id",
    sa."ad_group_id",
    dhtml5."name"::text AS "ad_name", sa.run_id,

    FROM {{ filtered_source('adwords', 'service_admodel') }} sa
             JOIN {{ filtered_source('adwords', 'service_dynamichtml5admodel') }} dhtml5
                  ON sa."id" = dhtml5."ad_id"

    UNION

    SELECT ed."id"          AS "internal_ad_id",
    ed."external_id" AS "external_ad_id",
    ed."ad_group_id",
    'Dynamically generated headline'::text AS "ad_name", ed.run_id,
    FROM {{ filtered_source('adwords', 'service_expandeddynamictextadmodel') }} ed

    UNION

    SELECT sa."id"          AS "internal_ad_id",
    sa."external_id" AS "external_ad_id",
    sa."ad_group_id",
    sa."direct_name"::text AS "ad_name", sa.run_id,
    FROM {{ filtered_source('adwords', 'service_admodel') }} sa
    WHERE sa."type" NOT IN (
        'RESPONSIVE_DISPLAY_AD',
        'DEMAND_GEN_CAROUSEL_AD',
        'DEMAND_GEN_MULTI_ASSET_AD',
        'HTML5_UPLOAD_AD',
        'DYNAMIC_HTML5_AD',
        'IMAGE_AD',
        'VIDEO_TRUEVIEW_IN_STREAM_AD',
        'VIDEO_RESPONSIVE_AD',
        'DISCOVERY_MULTI_ASSET_AD',
        'IN_FEED_VIDEO_AD',
        'VIDEO_NON_SKIPPABLE_IN_STREAM_AD',
        'VIDEO_BUMPER_AD',
        'DISCOVERY_CAROUSEL_AD'
      )
{% endmacro %}

{% macro meta_conversions() %}
    (
        SELECT DISTINCT
            CAST("conversion_name" AS VARCHAR) AS conversion_identifier,
            CAST("conversion_label" AS VARCHAR) AS conversion_label,
            run_id AS load_id,
            'meta_default_conversions_mapping' AS platform_name
        FROM {{ filtered_source('REPORTS', 'meta_default_conversions_mapping') }} dcm
        WHERE dcm."is_needed" = True
    )
    UNION ALL
    (
        SELECT DISTINCT
            CAST("conversion_name" AS VARCHAR) AS conversion_identifier,
            COALESCE(CAST(con_mapping."conversion_label" AS VARCHAR), CAST(con_report."conversion_name" AS VARCHAR)) AS conversion_label,
            con_report.run_id AS load_id,
            'meta_conversions_report' AS platform_name
        FROM {{ filtered_source('REPORTS', 'meta_conversions_report') }} con_report
        LEFT JOIN {{ filtered_source('REPORTS', 'meta_conversions_mapping') }} con_mapping
            ON con_report."account_id" = REPLACE(con_mapping."account_id", 'act_', '')
            AND REGEXP_SUBSTR(con_report."conversion_name", '[^.]+$', 1) = con_mapping."conversion_id"
    )
{% endmacro %}
