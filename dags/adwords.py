import datetime
import json
import logging
import time
import requests
import pandas as pd

from copy import deepcopy
from dataclasses import dataclass
from typing import List
from pandas import DataFrame
from pandas import json_normalize

from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook

from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.constants import (ADWORDS_ETL_ENDPOINT, ADWORDS_DOMAIN_URL, GOOGLE_OAUTH2_TOKEN_URL,
                                       EXECUTOR_STABLE_NODES, HISTORICAL_LOG_TABLE)
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.exceptions import MintFlowFailed
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.datadog import increment_metric, init_datadog
from myn_airflow_lib.commons import (EtlEndpointProcessorWithParams, failure_callback, filter_out_standard_queries,
                                     set_execution_dates, _get_db_value, safe_cast_to_float, safe_cast_to_int,
                                     iter_batch, set_config, enable_hist_log_check_based_on_conf,
                                     get_date_to_execute_on)


pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True
FLOW_NAME = 'Adwords'
CAMPAIGN_TYPE = 'adwords'
WAIT_SLEEP = 100
WAIT_SLEEP_AFTER_GETTING_NEW_TOKEN = 1
HISTORICAL_BATCH_SIZE = 30
query_body_tpls = {
    'Ad_Performance':
        "SELECT campaign.id,segments.date,metrics.impressions,metrics.clicks,metrics.average_cpm,"
        "metrics.conversions,metrics.average_cpc,metrics.ctr,metrics.cost_micros,"
        "metrics.conversions_value,metrics.all_conversions,"
        "metrics.conversions_from_interactions_rate,metrics.search_impression_share,"
        "metrics.video_views,metrics.active_view_impressions,metrics.video_quartile_p25_rate,"
        "metrics.video_quartile_p50_rate,metrics.video_quartile_p75_rate,metrics.video_quartile_p100_rate,"
        "metrics.search_budget_lost_impression_share "
        "FROM campaign "
        "WHERE ",
    "Performance_Mobile":
        "SELECT campaign.id,ad_group.id,ad_group_ad.ad.id,asset.id,segments.date,"
        "metrics.biddable_app_post_install_conversions,metrics.biddable_app_install_conversions "
        "FROM ad_group_ad_asset_view "
        "WHERE ",
    "Ad_performance_ad_level":
        "SELECT campaign.id, ad_group.id, ad_group_ad.ad.id,segments.date,"
        "metrics.impressions,metrics.clicks,metrics.average_cpm,"
        "metrics.conversions,metrics.average_cpc,metrics.ctr,"
        "metrics.cost_micros,metrics.conversions_value,"
        "metrics.all_conversions,metrics.conversions_from_interactions_rate,"
        "metrics.video_views,metrics.active_view_impressions,"
        "metrics.video_quartile_p25_rate,metrics.video_quartile_p50_rate,"
        "metrics.video_quartile_p75_rate,metrics.video_quartile_p100_rate, customer.currency_code " 
        "FROM ad_group_ad "
        "WHERE ",
    'Ad_Conversion_Performance':
        "SELECT segments.date,campaign.id,segments.conversion_action,metrics.all_conversions,metrics.conversions, "
        "metrics.conversions_value,metrics.all_conversions_value, customer.currency_code "
        "FROM campaign "
        "WHERE ",
    "Ad_Conversion_Performance_Ad_Level":
        "SELECT segments.date,campaign.id, ad_group.id, ad_group_ad.ad.id,segments.conversion_action,"
        "metrics.all_conversions, customer.currency_code,metrics.conversions,metrics.conversions_value, " 
        "metrics.all_conversions_value "
        "FROM ad_group_ad "
        "WHERE "
}

env_resolver = EnvironmentResolver()
env_type = env_resolver.environment
state = {
    'dates': {},
    'access_tokens': {},
}
dates = state['dates']
access_tokens = state['access_tokens']
REPORTS_PERFORMANCE_TABLE = 'search_campaign_performance'
REPORTS_PERFORMANCE_MOBILE_TABLE = 'search_campaign_performance_mobile'
REPORTS_AD_PERFORMANCE_TABLE = 'reports_adwords_ad_performance'
REPORTS_CONVERSION_PERFORMANCE_TABLE = 'search_campaign_conversion_performance'
REPORTS_AD_CONVERSION_PERFORMANCE_TABLE = 'reports_adwords_ad_conversion_performance'
REPORTS_CONVERSION_ACTION_MAPPING_TABLE = 'reports_adwords_action_mapping'
historical_log_cached_campaign_details = None
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
conv_value = 'metrics.conversionsValue'
currency_code = 'customer.currencyCode'


@dataclass
class QueryData:
    query_type: str  # 'standard' or 'historical'
    query_name: str
    profile: dict
    campaigns: List[dict]
    query_body_tpl: str
    time_filter: str
    campaigns_filter: str

    @property
    def campaign_details(self):
        return (
            json.dumps({
                'query_name': self.query_name,
                'campaign_id': campaign['external_id'],
                'client_customer_id': self.profile['client_customer_id']
            }, sort_keys=True)
            for campaign in self.campaigns
        )

    @property
    def company_id(self):
        return self.profile['company_id']

    @property
    def query_body(self):
        return self.query_body_tpl + self.time_filter + self.campaigns_filter

    def to_dict(self):
        if 'campaigns' in self.profile:
            # don't include campaigns data. otherwise XCOM grows 1000x in size
            del self.profile['campaigns']
        return {
            'query_type': self.query_type,
            'query_name': self.query_name,
            'profile': self.profile,
            'campaigns': self.campaigns,
            'query_body_tpl': self.query_body_tpl,
            'time_filter': self.time_filter,
            'campaigns_filter': self.campaigns_filter
        }


def get_profiles(**kwargs):
    processor = EtlEndpointProcessorWithParams(ADWORDS_ETL_ENDPOINT, **kwargs)
    # test data
    mocked_resp = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    if mocked_resp:
        logging.info('endpoing mock was found using that...')
        raw_profiles = mocked_resp
    else:
        raw_profiles = processor.apply_all()
    return raw_profiles


def process_conversion_mapping(profiles, **kwargs):
    """Requests conversion meta data for specified customer_id, handles and pushes report"""
    logging.info(f"Processing conversions campaign mapping based on {len(profiles)} profiles")
    query_body = "SELECT conversion_action.id,conversion_action.name,conversion_action.include_in_conversions_metric FROM conversion_action"
    customer_ids_creds = []

    # create unique credentials per customer_id
    for profile in profiles:
        # process if client_manager_id exists
        if profile.get('client_manager_id'):
            customer_creds_dict = dict(client_id=profile['client_id'], refresh_token=profile['refresh_token'],
                                       client_secret=profile['client_secret'], developer_token=profile['developer_token'],
                                       client_manager_id=profile['client_manager_id'].replace('-', ''),
                                       client_customer_id=profile['client_customer_id'].replace('-', ''))
            customer_ids_creds.append(customer_creds_dict)
    customer_ids_creds_unique = [dict(s) for s in set(frozenset(d.items()) for d in customer_ids_creds)]

    logging.info(f"Total unique customer ids with credentials - {len(customer_ids_creds_unique)}")
    for i, customer_dict in enumerate(customer_ids_creds_unique):
        logging.info(f"Processing customer_id - customer_id -> {i+1} / {len(customer_ids_creds_unique)}")
        url = f"{ADWORDS_DOMAIN_URL}/{customer_dict['client_customer_id']}/googleAds:searchStream"
        access_token = get_access_token(customer_dict['client_id'],
                                        customer_dict['refresh_token'],
                                        customer_dict['client_secret'])
        headers = {
            'Authorization': f"Bearer {access_token}",
            'developer-token': customer_dict['developer_token'],
            'login-customer-id': customer_dict['client_manager_id'],
            'url': url
        }
        logging.info(f"Requesting conversion mapping data based on data - {customer_dict}")
        try:
            response = requests.request(method='POST', url=url, headers=headers, data={"query": query_body})
            bad_statuses = [503, 429, 500, 403, 404, 401, 400]
            if response.status_code in bad_statuses:
                time.sleep(5)
                logging.error(f"Received status code - {response.status_code} requesting data {customer_dict}")
            else:
                report_data = response.json()
                time.sleep(1)
                if report_data != []:
                    report_results = []
                    for report_data_chunk in report_data:
                        report_results.extend(report_data_chunk['results'])
                    logging.info("Parsing report data")
                    report = parse_report_response_conversion_mapping(split_text=report_results,
                                                                      customer_id=customer_dict['client_customer_id'])
                    logging.info("Putting report to DB")
                    put_report_to_db_conversion_mapping(report=report)
        except Exception as e:
            logging.error(f"Exception while processing query {customer_dict}; Exception - {e}")


def get_standard_google_queries(profile, campaigns, campaigns_filter, **kwargs):
    last_week_time_filter = get_last_week_time_filter(**kwargs)
    standard_queries = []
    for query_name, query_body_tpl in query_body_tpls.items():
        # composing standard query
        query = QueryData('standard', query_name, profile, campaigns,
                          query_body_tpl, last_week_time_filter, campaigns_filter)
        standard_queries.append(query)
    return standard_queries


def filter_out_existing_queries(new_batch, queries):
    get_query_key = lambda q: (
        q.campaign_details, q.query_type, q.time_filter,
        q.profile['refresh_token'], q.profile['developer_token'],
    )
    existing_keys = {
        get_query_key(q) for q in queries
    }
    filtered_queries = []
    for query in new_batch:
        key = get_query_key(query)
        if key not in existing_keys:
            filtered_queries.append(query)
            existing_keys.add(key)
    return filtered_queries


def batch_historical_queries(historical_queries, batch_size=HISTORICAL_BATCH_SIZE):
    # query_name must be consistent within one batched request
    groupped_by_query_name_and_time_filter = {}

    def _get_batch_key(q):
        return q.query_name + q.time_filter

    for q in historical_queries:
        if _get_batch_key(q) not in groupped_by_query_name_and_time_filter:
            groupped_by_query_name_and_time_filter[_get_batch_key(q)] = []
        groupped_by_query_name_and_time_filter[_get_batch_key(q)].append(q)

    batched_queries = []
    for _, queries in groupped_by_query_name_and_time_filter.items():
        for queries_batch in iter_batch(queries, batch_size):
            batch_campaigns = []
            for q in queries_batch:
                batch_campaigns.extend(q.campaigns)
            first_query = queries_batch[0]
            batched_query = QueryData(
                first_query.query_type, first_query.query_name,
                first_query.profile, batch_campaigns,
                first_query.query_body_tpl, first_query.time_filter,
                get_campaigns_filter(batch_campaigns)
            )
            batched_queries.append(batched_query)
    return batched_queries


@set_config(state)
@set_execution_dates(dates)
def get_google_queries(profiles, **kwargs):
    logging.info(f"get_google_queries from {len(profiles)} profiles {profiles}")
    historical_range_mock = state['dag_run'].conf.get('historical_range_in_days')
    standard_queries_count, historical_queries_count, batched_historical_queries_count = 0, 0, 0
    queries = []
    for profile in profiles:
        logging.info(f"Processing profile {profile}")
        # getting filters to append to query
        campaigns = profile['campaigns']
        campaigns_filter = get_campaigns_filter(campaigns)
        if campaigns_filter is None:
            logging.info("No campaigns with external_id."
                         " No queries will be generated for this profile")
            continue
        standard_queries = get_standard_google_queries(profile, campaigns, campaigns_filter, **kwargs)
        standard_queries = filter_out_existing_queries(standard_queries, queries)
        queries.extend(standard_queries)
        standard_queries_count += len(standard_queries)

        # historical queires. first we create 1 per each campaign; then batch several into 1 request
        historical_queries = []
        for i, campaign in enumerate(campaigns):
            logging.info(f"Processing campaign {i} / {len(campaigns)}")
            if not campaign.get('external_id'):
                # skip if no external_id
                continue
            campaign_historical_queries = get_historical_queries(campaign, profile, historical_range_mock)
            campaign_historical_queries = filter_out_existing_queries(campaign_historical_queries,
                                                                      historical_queries)
            historical_queries.extend(campaign_historical_queries)
        batched_historical_queries = batch_historical_queries(historical_queries)
        queries.extend(batched_historical_queries)
        batched_historical_queries_count += len(batched_historical_queries)
        historical_queries_count += len(historical_queries)

    logging.info(f"Generated {len(queries)} queries total."
                 f" {standard_queries_count} standard and {batched_historical_queries_count} historical"
                 f" (raw historical campaign count {historical_queries_count})")
    return filter_out_standard_queries([
        query.to_dict() for query in queries
    ], **kwargs)


def get_historical_queries(campaign, profile, historical_range_mock):
    # changing campaign types to match values of Google API
    change_campaign_type(campaign)
    # getting filters to append to query
    campaign_filter = get_campaigns_filter([campaign])
    historical_time_filters = get_historical_time_filter(historical_range_mock)
    historical_queries = {}
    for query_name, query_body_tpl in query_body_tpls.items():
        # composing query
        # take each time filter
        for historical_time_filter in historical_time_filters:
            query = QueryData('historical', query_name, profile, [campaign],
                              query_body_tpl, historical_time_filter, campaign_filter)
            # check if already loaded this query in the past
            hist_log_exist = does_hist_log_exist(query)
            logging.info(f"historical_log_exist = {hist_log_exist}")
            if not hist_log_exist:
                # make sure we are not adding the same query twice
                query_dedup_key = (query.query_body, profile['refresh_token'])
                if query_dedup_key not in historical_queries:
                    historical_queries[query_dedup_key] = query
    return list(historical_queries.values())


def get_campaigns_filter(campaigns):
    campaign_ids_str = ", ".join(f"'{campaign['external_id']}'"
                                 for campaign in campaigns
                                 if campaign.get('external_id'))
    if not campaign_ids_str:
        return None  # there are no campaigns with external_id
    return (f" AND campaign.id IN ({campaign_ids_str})"
            f" PARAMETERS omit_unselected_resource_names = true")


def get_last_week_time_filter(**kwargs):
    last_week = get_date_to_execute_on(dates, '%Y-%m-%d', **kwargs)
    today = dates['TODAY'].strftime('%Y-%m-%d')
    query_date_range = "segments.date >= '{}' AND segments.date <= '{}'".format(last_week, today)
    return query_date_range


def get_historical_time_filter(historical_range_mock):
    two_years_ago = dates['TWO_YEARS_AGO']
    year_ago = dates['YEAR_AGO']
    six_month_ago = dates['SIX_MINTHS_AGO']
    one_year_a_half_year_ago = dates['ONE_AND_A_HALF_YEAR_AGO']
    today = dates['TODAY'].strftime("%Y-%m-%d")
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        date_from = dates['TODAY'] - datetime.timedelta(historical_range_mock)
        return [f"segments.date >= '{date_from}' AND segments.date <= '{today}'"]
    two_years_ago = two_years_ago.strftime('%Y-%m-%d')
    year_ago = year_ago.strftime('%Y-%m-%d')
    six_month_ago = six_month_ago.strftime('%Y-%m-%d')
    one_year_a_half_year_ago = one_year_a_half_year_ago.strftime('%Y-%m-%d')
    query_date_range_two_years = f"segments.date >= '{two_years_ago}' AND segments.date <= '{one_year_a_half_year_ago}'"
    query_date_range_one_and_half = f"segments.date >= '{one_year_a_half_year_ago}' AND segments.date <= '{year_ago}'"
    query_date_range_one_year = f"segments.date >= '{year_ago}' AND segments.date <= '{six_month_ago}'"
    query_date_range_half_a_year = f"segments.date >= '{six_month_ago}' AND segments.date <= '{today}'"
    return [query_date_range_two_years, query_date_range_one_year, query_date_range_half_a_year, query_date_range_one_and_half]


def change_campaign_type(campaign):
    type_flow = campaign['type']
    if type_flow == ('SEARCH'):
        campaign['type'] = "adwords"
    else:
        campaign['type'] = "gdn"
    return campaign


def get_access_token(client_id, refresh_token, client_secret, force_update=False,
                     retries=3):
    if refresh_token in access_tokens and not force_update:
        return access_tokens[refresh_token]
    exception = None
    response = None
    while retries > 0:
        retries -= 1
        try:
            response = requests.post(
                GOOGLE_OAUTH2_TOKEN_URL,
                data={
                    'client_id': client_id,
                    'client_secret': client_secret,
                    'refresh_token': refresh_token,
                    'grant_type': 'refresh_token'
                }
            )
            break
        except Exception as e:
            exception_message = (
                f'Network error occurred while getting'
                f' access_token (refresh_token={refresh_token[:30]}...) {e}')
            logging.error(exception_message)
            exception = MintFlowFailed(exception_message)
            time.sleep(10)
    if exception is not None:
        return False
    logging.info('Access token resp received')
    if 'access_token' not in response.json():
        logging.error(f"No `access_token_key` in resp {response.text}")
        return None
    access_tokens[refresh_token] = response.json()['access_token']
    return access_tokens[refresh_token]


def request_report(access_token, query, **kwargs):
    url = f"{ADWORDS_DOMAIN_URL}/{query.profile['client_customer_id'].replace('-', '')}/googleAds:searchStream"
    data = {"query": query.query_body}
    response = send_google_request(
        access_token, query.profile['client_id'], query.profile['client_secret'],
        query.profile['developer_token'], query.profile['refresh_token'],
        query.profile['client_manager_id'].replace('-', ''),
        url, 'POST', data, report_type=query.query_type, company_id=query.profile.get('company_id'),
        **kwargs
    )
    return response.json()


def send_google_request(access_token: str, client_id: str, client_secret: str, dev_token: str, refresh_token: str,
                        client_manager: str, url: str, method: str, json_data: dict = None, report_type: str = None,
                        company_id: int = None, **kwargs):
    """
        Exponential error backoff - 5*2+5*2*2+5*2*2*2+5*2*2*2*2+5*2*2*2*2*2 = 310 seconds
    """
    error_sleep_time = 5
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    bad_statuses = [503, 429, 500, 403, 404, 401, 400]
    headers = {
        'Authorization': f"Bearer {access_token}",
        'developer-token': dev_token,
        'login-customer-id': client_manager,
        'url': url
    }
    last_status_code = None
    for i_retry in range(5):
        response = requests.request(method=method, url=url, headers=headers, data=json_data)
        error_sleep_time *= 2
        last_status_code = response.status_code
        if response.status_code in bad_statuses:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:error",
                                              f"client:{company_id}", f'run_number:{current_run_number}'])
            if response.status_code == 401:
                logging.info('Need New Access Token')
                access_token_retry = get_access_token(client_id, refresh_token, client_secret,
                                                      force_update=True)
                headers['Authorization'] = f"Bearer {access_token_retry}"
                time.sleep(WAIT_SLEEP_AFTER_GETTING_NEW_TOKEN)
            else:
                logging.warning(f'send request with data = {json.dumps(headers)} \n'
                                f'received {response.status_code} {response.text}')
                if response.status_code == 403:
                    break
                time.sleep(error_sleep_time)
        else:
            increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                             additional_tags=[f"request_type:{report_type}", "request_status:success",
                                              f"client:{company_id}", f'run_number:{current_run_number}'])
            return response
    data_to_log = deepcopy(headers)
    data_to_log['Authorization'] = data_to_log['Authorization'][:30]
    data_to_log['last_status_code'] = last_status_code
    raise MintFlowFailed(f"{FLOW_NAME} can't process the request, {i_retry + 1} tries done."
                         f" {json.dumps(data_to_log)}")


@set_execution_dates(dates)
def process_queries(generated_batch: List[dict], **kwargs):
    logging.info(f"process_queries {len(generated_batch)} {generated_batch}")
    errors_count = 0
    historical_errors_count = 0
    standard_errors_count = 0
    errors_occurred_with_this_profiles = set()
    for i, query_data in enumerate(generated_batch):
        logging.info(f"Current query_data [{i} / {len(generated_batch)}] {query_data}")
        query = QueryData(**query_data)
        try:
            process_query(query, **kwargs)
            logging.info("Processing Query Done")
        except Exception as e:
            logging.error(f"Exception while processing query {query_data} ; {e}")
            errors_count += 1
            if query.query_type == 'historical':
                historical_errors_count += 1
            else:
                standard_errors_count += 1
            errors_occurred_with_this_profiles.add(
                json.dumps(query.profile, indent=4, sort_keys=True)
            )
    logging.info(f"processed {len(generated_batch)} queries. There were {errors_count} errors")
    logging.info(f"historical_errors_count={historical_errors_count};")
    logging.info(f"standard_errors_count={standard_errors_count};")
    logging.info(f"errors_occurred_with_this_profiles={errors_occurred_with_this_profiles};")
    return [{"total": len(generated_batch), "errors": errors_count,
             "historical_errors_count": historical_errors_count,
             "standard_errors_count": standard_errors_count,
             "errors_occurred_with_this_profiles": list(errors_occurred_with_this_profiles)}]


def print_processing_summary(results):
    total, errors = 0, 0
    historical_errors_count, standard_errors_count = 0, 0
    errors_occurred_with_this_profiles = set()
    for row in results:
        total += row['total']
        errors += row['errors']
        historical_errors_count += row['historical_errors_count']
        standard_errors_count += row['standard_errors_count']
        errors_occurred_with_this_profiles.update(row['errors_occurred_with_this_profiles'])
    errors_occurred_with_this_profiles = [
        json.loads(p) for p in errors_occurred_with_this_profiles
    ]
    logging.info(f"Processed {total} queries. Of which there were {errors} errors"
                 f" ({historical_errors_count} historical errors & {standard_errors_count} standard errors)")
    logging.info(f"errors_occurred_with_this_profiles (count={len(errors_occurred_with_this_profiles)})"
                 f" {json.dumps(errors_occurred_with_this_profiles, indent=4)}")


def _does_hist_log_exist(campaign_detail: str):
    global historical_log_cached_campaign_details
    if historical_log_cached_campaign_details is None:
        query = f"""
            select campaign_detail
            from {HISTORICAL_LOG_TABLE}
            where campaign_type = '{CAMPAIGN_TYPE}'
        """
        df = pg_hook.get_pandas_df(query)
        if df.empty:
            historical_log_cached_campaign_details = set()
        else:
            df['campaign_detail'] = df['campaign_detail'].apply(
                lambda cd: json.dumps(cd, sort_keys=True))
            historical_log_cached_campaign_details = set(df['campaign_detail'])
    return campaign_detail in historical_log_cached_campaign_details


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(query: QueryData):
    return all(
        _does_hist_log_exist(campaign_detail)
        for campaign_detail in query.campaign_details
    )


def log_hist_request(query: QueryData):
    company_id = query.company_id
    for campaign_detail in query.campaign_details:
        query = f"""
                INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
                Values (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
                """
        data = {'company_id': company_id,
                'campaign_type': CAMPAIGN_TYPE,
                'campaign_detail': campaign_detail}
        pg_hook.run(query, True, data)


def _get_sql_columns_and_values(report, columns_order):
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    values_str = ', '.join(map(lambda x: f'({", ".join(x)})', fixed_rows))
    return columns_str, values_str


def parse_report_response_ad_performance(split_text):
    report = json_normalize(split_text)
    logging.info(f"Len report = {len(report)}")
    report.rename(
        inplace=True,
        columns={
            'campaign.id': 'campaign_id', 'metrics.clicks': 'clicks',
            'metrics.searchImpressionShare': 'search_impression_share',
            'metrics.conversionsFromInteractionsRate': 'conversion_rate',
            conv_value: 'conversions_value',
            'metrics.conversions': 'conversions', 'metrics.costMicros': 'budget_spent',
            'metrics.ctr': 'ctr', 'metrics.allConversions': 'all_conversions',
            'metrics.averageCpc': 'cpc', 'metrics.averageCpm': 'ecpm',
            'metrics.impressions': 'impression', 'segments.date': 'date',
            'metrics.videoViews': 'video_views',
            'metrics.activeViewImpressions': 'active_view_impressions',
            'metrics.videoQuartileP25Rate': 'video_quartile_p25_rate',
            'metrics.videoQuartileP50Rate': 'video_quartile_p50_rate',
            'metrics.videoQuartileP75Rate': 'video_quartile_p75_rate',
            'metrics.videoQuartileP100Rate': 'video_quartile_p100_rate',
            'metrics.searchBudgetLostImpressionShare': 'search_budget_lost_impression_share',
        }
    )
    if report.empty:
        logging.info("empty report parsed")
    else:
        report = report.replace('', None) \
                        .replace('(not set)', None)
        for optional_col in ['cpc', 'ecpm', 'conversion_rate', 'search_impression_share',
                             'budget_spent', 'impression', 'search_budget_lost_impression_share',
                             'ctr', 'conversions_value',
                             'clicks', 'conversions', 'all_conversions',
                             'video_quartile_p25_rate', 'video_quartile_p50_rate',
                             'video_quartile_p75_rate', 'video_quartile_p100_rate',
                             'video_views', 'complete_views', 'active_view_impressions']:
            if optional_col not in report.columns:
                report[optional_col] = None
            else:
                report[optional_col] = report[optional_col].apply(safe_cast_to_float)
        report['ecpm'] = report['ecpm'] / 1000000
        report['cpc'] = report['cpc'] / 1000000
        report['conversion_rate'] = \
            report['conversion_rate'].apply(safe_cast_to_float) * 100
        report['budget_spent'] = report['budget_spent'].apply(safe_cast_to_float) / 1000000
        report['search_impression_share'] = \
            report['search_impression_share'].apply(safe_cast_to_float) * 100
        report['impression'] = report['impression'].apply(safe_cast_to_int)
        report['video_views'] = report['video_views'].apply(safe_cast_to_int)
        report['views_25p'] = report['impression'] * report['video_quartile_p25_rate']
        report['views_50p'] = report['impression'] * report['video_quartile_p50_rate']
        report['views_75p'] = report['impression'] * report['video_quartile_p75_rate']
        report['complete_views'] = report['impression'] * report['video_quartile_p100_rate']
        report['active_view_impressions'] = report['active_view_impressions'].apply(safe_cast_to_int)

        def _get_total_eligible_impressions(row):
            impression = row.impression or 0
            if row.search_impression_share and not pd.isna(row.search_impression_share):
                return round((impression / row.search_impression_share) * 100)
            else:
                return 0
        report['total_eligible_impressions'] = report.apply(_get_total_eligible_impressions, axis=1)

        # fillna 0. because 0 is default on the table
        for col in report.columns:
            if col not in ['date', 'campaign_id', 'search_budget_lost_impression_share']:
                report[col] = report[col].fillna(0)

        # all rows are saved with campaign_type == 'adwords'
        report['campaign_type'] = 'adwords'
        report['avg_position'] = 0
    return report


def put_report_to_db_ad_performance(report: DataFrame):
    columns_order = ['date', 'campaign_id', 'impression',
                     'clicks', 'ecpm', 'conversions', 'conversions_value', 'cpc', 'ctr',
                     'conversion_rate', 'budget_spent', 'search_impression_share',
                     'all_conversions', 'total_eligible_impressions', 'campaign_type',
                     'avg_position', 'search_budget_lost_impression_share',
                     'video_views', 'active_view_impressions', 'views_25p',
                     'views_50p', 'views_75p', 'complete_views']
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        sql = f"""
            INSERT INTO {REPORTS_PERFORMANCE_TABLE} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (date, campaign_type, campaign_id)
            DO UPDATE
                SET clicks = Excluded.clicks,
                    impression = Excluded.impression,
                    ctr = Excluded.ctr,
                    cpc = Excluded.cpc,
                    ecpm = Excluded.ecpm,
                    conversions =  Excluded.conversions,
                    conversion_rate = Excluded.conversion_rate,
                    conversions_value = Excluded.conversions_value,
                    budget_spent = Excluded.budget_spent,
                    search_impression_share = Excluded.search_impression_share,
                    all_conversions = Excluded.all_conversions,
                    avg_position = Excluded.avg_position,
                    total_eligible_impressions = Excluded.total_eligible_impressions,
                    video_views = Excluded.video_views,
                    active_view_impressions = Excluded.active_view_impressions,
                    views_25p = Excluded.views_25p,
                    views_50p = Excluded.views_50p,
                    views_75p = Excluded.views_75p,
                    complete_views = Excluded.complete_views,
                    search_budget_lost_impression_share = Excluded.search_budget_lost_impression_share
            ;
        """
        pg_hook.run(sql, True)


def parse_report_response_performance_mobile(split_text):
    report = json_normalize(split_text)
    logging.info(f"Len report = {len(report)}")
    report.rename(
        inplace=True,
        columns={
            'segments.date': 'date',
            'campaign.id': 'campaign_id',
            'adGroup.id': 'ad_group_id',
            'adGroupAd.ad.id': 'ad_id',
            'asset.id': 'asset_id',
            'metrics.biddableAppInstallConversions': 'installations',
            'metrics.biddableAppPostInstallConversions': 'in_app_actions'
        }
    )
    if report.empty:
        logging.info("empty report parsed")
    else:
        report = report.replace('', None) \
                        .replace('(not set)', None)
        for optional_col in ['installations', 'in_app_actions']:
            if optional_col not in report.columns:
                report[optional_col] = None
            else:
                report[optional_col] = report[optional_col].apply(safe_cast_to_int)
        # all rows are saved with campaign_type == 'adwords'
        report['campaign_type'] = 'adwords'
    return report


def put_report_to_db_performance_mobile(report: DataFrame):
    columns_order = ['date', 'campaign_type', 'campaign_id', 'ad_group_id', 'ad_id',
                     'asset_id', 'installations', 'in_app_actions']
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        sql = f"""
            INSERT INTO {REPORTS_PERFORMANCE_MOBILE_TABLE} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (
                date, campaign_type, campaign_id, ad_group_id, ad_id, asset_id
            )
            DO UPDATE
                SET installations = Excluded.installations,
                    in_app_actions = Excluded.in_app_actions
            ;
        """
        pg_hook.run(sql, True)


def parse_report_response_ad_performance_ad_level(split_text):
    report = json_normalize(split_text)
    logging.info(f"Len report = {len(report)}")
    report.rename(
        inplace=True,
        columns={
            'campaign.id': 'campaign_id', 'adGroupAd.ad.id': 'ad_id',
            'metrics.clicks': 'clicks', 'adGroup.id': 'adgroup_id',
            'metrics.conversionsFromInteractionsRate': 'conversion_rate',
            conv_value: 'conversions_value',
            'metrics.conversions': 'conversions', 'metrics.costMicros': 'budget_spent',
            'metrics.ctr': 'ctr', 'metrics.allConversions': 'all_conversions',
            'metrics.averageCpc': 'cpc', 'metrics.averageCpm': 'ecpm',
            'metrics.impressions': 'impression', 'segments.date': 'date',
            'metrics.videoViews': 'video_views',
            'metrics.activeViewImpressions': 'active_view_impressions',
            'metrics.videoQuartileP25Rate': 'video_quartile_p25_rate',
            'metrics.videoQuartileP50Rate': 'video_quartile_p50_rate',
            'metrics.videoQuartileP75Rate': 'video_quartile_p75_rate',
            'metrics.videoQuartileP100Rate': 'video_quartile_p100_rate',
            'metrics.searchBudgetLostImpressionShare': 'search_budget_lost_impression_share',
            currency_code: 'currency_code',
        }
    )
    if report.empty:
        logging.info("empty report parsed")
    else:
        report = report.replace('', None) \
            .replace('(not set)', None)
        for optional_col in ['conversion_rate',
                             'budget_spent', 'impression',
                             'clicks', 'conversions', 'all_conversions',
                             'video_quartile_p25_rate', 'video_quartile_p50_rate',
                             'video_quartile_p75_rate', 'video_quartile_p100_rate',
                             'video_views', 'complete_views', 'active_view_impressions']:
            if optional_col not in report.columns:
                report[optional_col] = None
            else:
                report[optional_col] = report[optional_col].apply(
                    safe_cast_to_float)
        report['ecpm'] = report['ecpm'] / 1000000
        report['cpc'] = report['cpc'] / 1000000
        report['conversion_rate'] = \
            report['conversion_rate'].apply(safe_cast_to_float) * 100
        report['budget_spent'] = report['budget_spent'].apply(
            safe_cast_to_float) / 1000000
        report['impression'] = report['impression'].apply(safe_cast_to_int)
        report['video_views'] = report['video_views'].apply(safe_cast_to_int)
        report['views_25p'] = report['impression'] * \
            report['video_quartile_p25_rate']
        report['views_50p'] = report['impression'] * \
            report['video_quartile_p50_rate']
        report['views_75p'] = report['impression'] * \
            report['video_quartile_p75_rate']
        report['complete_views'] = report['impression'] * \
            report['video_quartile_p100_rate']
        report['active_view_impressions'] = report['active_view_impressions'].apply(
            safe_cast_to_int)

        # fillna 0. because 0 is default on the table
        for col in report.columns:
            if col not in ['date', 'campaign_id', 'search_budget_lost_impression_share']:
                report[col] = report[col].fillna(0)

        # all rows are saved with campaign_type == 'adwords'
        report['campaign_type'] = 'adwords'
    return report


def put_report_to_db_ad_performance_ad_level(report: DataFrame):
    columns_order = ['date', 'campaign_id', 'adgroup_id', 'ad_id', 'impression',
                     'clicks', 'conversions', 'conversions_value',
                     'budget_spent', 'all_conversions', 'campaign_type',
                     'video_views', 'active_view_impressions', 'views_25p',
                     'views_50p', 'views_75p', 'complete_views', 'currency_code']
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        sql = f"""
            INSERT INTO {REPORTS_AD_PERFORMANCE_TABLE} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (date, campaign_type, campaign_id, adgroup_id, ad_id)
            DO UPDATE
                SET clicks = Excluded.clicks,
                    impression = Excluded.impression,
                    conversions =  Excluded.conversions,
                    conversions_value = Excluded.conversions_value,
                    budget_spent = Excluded.budget_spent,
                    all_conversions = Excluded.all_conversions,
                    video_views = Excluded.video_views,
                    active_view_impressions = Excluded.active_view_impressions,
                    views_25p = Excluded.views_25p,
                    views_50p = Excluded.views_50p,
                    views_75p = Excluded.views_75p,
                    complete_views = Excluded.complete_views,
                    currency_code = Excluded.currency_code
            ;
        """
        pg_hook.run(sql, True)


def parse_report_response_ad_conversion_performance(split_text):
    report = json_normalize(split_text)
    logging.info(f"Len report = {len(report)}")
    report.rename(
        inplace=True,
        columns={
            'segments.date': 'date',
            'campaign.id': 'campaign_id',
            'segments.conversionAction': 'conversion_action_id',
            'metrics.allConversions': 'all_conversions',
            'metrics.conversions': 'conversions',
            'metrics.allConversionsValue': 'all_conversions_revenue',
            currency_code: 'currency_code',
            conv_value: 'conversions_revenue'})
    if report.empty:
        logging.info("empty report parsed")
    else:
        report = report.replace('', None) \
                        .replace('(not set)', None)
        report['conversion_action_id'] = report['conversion_action_id'].apply(lambda x: x.split('/')[-1])
        report['conversion_action_id'] = report['conversion_action_id'].apply(safe_cast_to_int)
        for optional_col in ['all_conversions', 'conversions', 'all_conversions_revenue', 'conversions_revenue']:
            if optional_col not in report.columns:
                report[optional_col] = None
    return report


def put_report_to_db_ad_conversion_performance(report: DataFrame):
    columns_order = ['date', 'campaign_id', 'conversion_action_id', 'all_conversions', 'conversions',
                     'all_conversions_revenue', 'conversions_revenue', 'currency_code']
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        sql = f"""
            INSERT INTO {REPORTS_CONVERSION_PERFORMANCE_TABLE} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (date, campaign_id, conversion_action_id)
            DO UPDATE
                SET all_conversions = Excluded.all_conversions,
                    conversions = Excluded.conversions,
                    currency_code = Excluded.currency_code,
                    all_conversions_revenue = Excluded.all_conversions_revenue,
                    conversions_revenue = Excluded.conversions_revenue;"""
        pg_hook.run(sql, True)


def parse_report_response_ad_conversion_performance_ad_level(split_text):
    report = json_normalize(split_text)
    logging.info(f"Len report = {len(report)}")
    report.rename(
        inplace=True,
        columns={
            'segments.date': 'date',
            'campaign.id': 'campaign_id',
            'adGroup.id': 'adgroup_id',
            'adGroupAd.ad.id': 'ad_id',
            'segments.conversionAction': 'conversion_action_id',
            'metrics.allConversions': 'all_conversions',
            currency_code: 'currency_code',
            'metrics.conversions': 'conversions',
            'metrics.allConversionsValue': 'all_conversions_revenue',
            conv_value: 'conversions_revenue'})
    if report.empty:
        logging.info("empty report parsed")
    else:
        report = report.replace('', None) \
                        .replace('(not set)', None)
        report['conversion_action_id'] = report['conversion_action_id'].apply(lambda x: x.split('/')[-1])
        report['conversion_action_id'] = report['conversion_action_id'].apply(safe_cast_to_int)
        for optional_col in ['all_conversions', 'conversions', 'all_conversions_revenue', 'conversions_revenue']:
            if optional_col not in report.columns:
                report[optional_col] = None
    return report


def put_report_to_db_ad_conversion_performance_ad_level(report: DataFrame):
    columns_order = ['date', 'campaign_id', 'adgroup_id', 'ad_id', 'conversion_action_id', 'all_conversions',
                     'currency_code', 'conversions', 'all_conversions_revenue', 'conversions_revenue']
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        sql = f"""
            INSERT INTO {REPORTS_AD_CONVERSION_PERFORMANCE_TABLE} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (date, campaign_id, adgroup_id, ad_id, conversion_action_id)
            DO UPDATE
                SET all_conversions = Excluded.all_conversions,
                    currency_code = Excluded.currency_code,
                    conversions = Excluded.conversions,
                    all_conversions_revenue = Excluded.all_conversions_revenue,
                    conversions_revenue = Excluded.conversions_revenue;"""
        pg_hook.run(sql, True)


def parse_report_response_conversion_mapping(split_text, customer_id):
    report = json_normalize(split_text)
    logging.info(f"Len report = {len(report)}")
    report.rename(
        inplace=True,
        columns={
            'conversionAction.id': 'conversion_action_id',
            'conversionAction.name': 'conversion_action_name',
            'conversionAction.includeInConversionsMetric': 'include_in_conversions_metric'})
    if report.empty:
        logging.info("empty report parsed")
    else:
        report = report.replace('', None) \
                        .replace('(not set)', None)
        report['customer_id'] = customer_id.replace('-', '')
        report['customer_id'] = report['customer_id'].apply(safe_cast_to_int)
        report['conversion_action_id'] = report['conversion_action_id'].apply(safe_cast_to_int)
    return report


def put_report_to_db_conversion_mapping(report: DataFrame):
    columns_order = ['customer_id', 'conversion_action_id', 'conversion_action_name', 'include_in_conversions_metric']
    report = report[columns_order]
    columns_str = ', '.join(report.columns)
    rows = list(report.itertuples(index=False, name=None))
    fixed_rows = [tuple(map(_get_db_value, row)) for row in rows]
    batch_size = 5000
    for i, batch_rows in enumerate(iter_batch(fixed_rows, batch_size)):
        logging.info(f"Inserting batch[max_size={batch_size}] #{i}")
        values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
        sql = f"""
            INSERT INTO {REPORTS_CONVERSION_ACTION_MAPPING_TABLE} ({columns_str})
            VALUES {values_str}
            ON CONFLICT (customer_id,conversion_action_id)
            DO UPDATE
                SET conversion_action_name = Excluded.conversion_action_name,
                    include_in_conversions_metric = Excluded.include_in_conversions_metric;"""
        pg_hook.run(sql, True)


def process_query(query, **kwargs):
    logging.info("process_query")
    access_token = get_access_token(query.profile['client_id'],
                                    query.profile['refresh_token'],
                                    query.profile['client_secret'])
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    if not access_token:
        # this can happen when etl endpoint contains invalid or expired credentials
        logging.critical(f'Endpoint has invalid creds for the {query}')
        return
    report_data = request_report(access_token, query, **kwargs)
    company_id = query.profile.get('company_id')
    if report_data != []:
        # saving reports in db
        logging.info(f"Saving report")
        logging.info(query.query_name)
        report_results = []
        for report_data_chunk in report_data:
            report_results.extend(report_data_chunk['results'])
        if query.query_name == 'Ad_Performance':
            report = parse_report_response_ad_performance(report_results)
            if not report.empty:
                put_report_to_db_ad_performance(report)
                increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                                 [f"table:{REPORTS_PERFORMANCE_TABLE}", f"client:{company_id}", f'run_number:{current_run_number}'])

        elif query.query_name == 'Performance_Mobile':
            report = parse_report_response_performance_mobile(report_results)
            if not report.empty:
                put_report_to_db_performance_mobile(report)
                increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                                 [f"table:{REPORTS_PERFORMANCE_MOBILE_TABLE}", f"client:{company_id}", f'run_number:{current_run_number}'])
        elif query.query_name == 'Ad_performance_ad_level':
            report = parse_report_response_ad_performance_ad_level(report_results)
            if not report.empty:
                put_report_to_db_ad_performance_ad_level(report)
                increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                                 [f"table:{REPORTS_AD_PERFORMANCE_TABLE}", f"client:{company_id}", f'run_number:{current_run_number}'])
        elif query.query_name == 'Ad_Conversion_Performance':
            report = parse_report_response_ad_conversion_performance(report_results)
            if not report.empty:
                put_report_to_db_ad_conversion_performance(report)
                increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                                 [f"table:{REPORTS_CONVERSION_PERFORMANCE_TABLE}", f"client:{company_id}", f'run_number:{current_run_number}'])
        elif query.query_name == 'Ad_Conversion_Performance_Ad_Level':
            report = parse_report_response_ad_conversion_performance_ad_level(report_results)
            if not report.empty:
                put_report_to_db_ad_conversion_performance_ad_level(report)
                increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                                 [f"table:{REPORTS_AD_CONVERSION_PERFORMANCE_TABLE}", f"client:{company_id}", f'run_number:{current_run_number}'])
        logging.info("Report processed")
    else:
        logging.info("Empty report response")
    if query.query_type == 'historical':
        # log historical to historical_log
        logging.info("Writing historical query to historical_log")
        log_hist_request(query)


with DAG(FLOW_NAME,
         description='adwords flow dag',
         schedule_interval=None,
         tags=['ingestion_flow'],
         params=DagConfigSetter(FLOW_NAME).get_params(),
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False,
         on_failure_callback=failure_callback) as dag:
    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )

    conversion_mapping = PythonOperator(
        task_id='process_conversion_mapping',
        on_failure_callback=failure_callback,
        python_callable=process_conversion_mapping,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id='get_google_queries',
        on_failure_callback=failure_callback,
        python_callable=get_google_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="process_queries",
        on_failure_callback=failure_callback,
        python_callable=process_queries,
        op_kwargs={"generated_batch": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    print_processing_summary = PythonOperator(
        task_id='print_processing_summary',
        on_failure_callback=failure_callback,
        python_callable=print_processing_summary,
        op_kwargs={"results": XComArg(t3)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> conversion_mapping >> t2 >> t3 >> print_processing_summary >> cleanup_xcoms
