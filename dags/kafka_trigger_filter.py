import json
import logging

from typing import Optional, Dict

def filter_ready(msg_bytes: bytes) -> Optional[Dict]:
    try:
        logging.info(f"Received Kafka message: {msg_bytes}")
        data = json.loads(msg_bytes.decode("utf-8"))
        logging.info(f"Parsed message: {data}")

        if data.get("status") == "READY":
            logging.info("Message status is READY, triggering DAG")
            return data

        else:
            logging.info(f"Ignoring message with status: {data.get('status')}")
            return None

    except Exception as e:
        logging.error(f"Error processing Kafka message: {e}")

        return None
