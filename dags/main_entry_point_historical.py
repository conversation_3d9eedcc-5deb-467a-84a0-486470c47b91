
import datetime
from airflow import DAG
from airflow_common.dag_config_setter import DagConfigSetter
from myn_airflow_lib.commons import failure_callback, get_main_dags
from myn_airflow_lib.datadog import init_datadog
from myn_airflow_lib.resolvers import EnvironmentResolver
from airflow.operators.python import <PERSON><PERSON><PERSON>honOperator, PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.task_group import TaskGroup
from myn_airflow_lib.constants import TRIGGER_DAG_NODES, BRANCH_PODS
from main_entry_point import list_ingestion_dags_to_run, post_dag_metadata_push
env_resolver = EnvironmentResolver()

FLOW_NAME = "main_dag_historical"
ingestion_flow_tag = 'main_flow'  # "ingestion_flow"
init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
all_main_dags = get_main_dags()


def setting_config(**kwargs):
    """
    function sets current dag config 'historical_only' value to true
    """
    kwargs['dag_run'].conf['historical_only'] = True
    kwargs['dag_run'].conf['current_run_number'] = 'historical'

with DAG(
        dag_id=FLOW_NAME,
        schedule_interval="0 18 * * *",
        description="main dag to perform historicall pulls",
        start_date=datetime.datetime(2022, 1, 1),
        catchup=False,
        on_failure_callback=failure_callback,
        params=DagConfigSetter(FLOW_NAME).get_params(),
        max_active_runs=1,
        render_template_as_native_obj=True
) as dag:

    setting_config_operator = PythonOperator(
        task_id='setting_config',
        dag=dag,
        python_callable=setting_config
    )

    post_dag_task = PythonOperator(
        task_id='post_dag',
        dag=dag,
        python_callable=post_dag_metadata_push,
        op_args=['setting_config', 'post_dag']
    )


    with TaskGroup(group_id="running_airflow_dags") as running_airflow_dags:

        airflow_branching = BranchPythonOperator(
            task_id='airflow_branching',
            python_callable=list_ingestion_dags_to_run,
            op_args=[all_main_dags, '{{ dag_run.conf }}'],
            executor_config=BRANCH_PODS,
        )

        for dag_id in all_main_dags:
            trigger_task = TriggerDagRunOperator(
                task_id=f'trigger_{dag_id}',
                trigger_dag_id=dag_id,
                dag=dag,
                retries=0,
                wait_for_completion=True,
                conf='{{ dag_run.conf }}',
                executor_config=TRIGGER_DAG_NODES,
            )
            airflow_branching >> trigger_task

    setting_config_operator >> running_airflow_dags >> post_dag_task
