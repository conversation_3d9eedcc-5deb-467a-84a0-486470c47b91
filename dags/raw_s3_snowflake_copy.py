from myn_airflow_lib.snowflake.datasource_copy import SnowflakeSourceProcessor, DatalakeS3Path, DatalakeSource
from airflow import DAG
from myn_airflow_lib.commons import failure_callback
import datetime
from airflow.operators.python import PythonOperator
from concurrent.futures import ThreadPoolExecutor, as_completed


FLOW_NAME = 'SNOWFLAKE_RAW_FILE_COPY'

renaming_map = {
    'historical_budget_non_partitioned': 'historical_budget'
}

PARTITIONED = ['final/historical_budget']
SOURCES_TO_INGEST = [
    "final/adwords_impression_share",
    "base/bing_campaign_dim",
    "final/bing_impression_share",
    "ds/conversions_per_level",
    "ds/druid_v2_extended",
    "final/druid_v2",
    "base/dv360_campaign_dim",
    "base/dv360_io_dim",
    "base/dv360_li_dim",
    "base/facebook_ad_set_dim",
    "base/facebook_campaign_dim",
    "ds/facebook_level",
    "ds/ga/conversions_main",
    "base/ghost_campaign_dim",
    "base/google_campaign_dim",
    "final/historical_budget_non_partitioned",
    "final/historical_recommendations",
    "final/historical_rules_engine_alerts",
    "ai/media_dim",
    "base/media_plan_client_dim",
    "final/conversions_campaign_level",
    "ds/mediaplan_dim_extended",
    "base/mediaplan_dim",
    "base/media_plan_journey_dim",
    "base/campaignmargins",
    "final/offline_tv",
    "ai/performance_conversions",
    "ai/performance",
    "final/recommendations_settings",
    # sources for test YK
    "base/integrated_adform",
    "base/integrated_appsflyer",
    "base/integrated_cm",
    "base/integrated_cm_linkage",
    "base/integrated_cm_paid_search",
    "base/integrated_ga4",
    "base/integrated_omni_platform",
    "base/integrated_platform",
    "final/pivot_static",
]



def ingest_all():
    sources = []

    for layer_and_name in SOURCES_TO_INGEST:
        if layer_and_name not in PARTITIONED:
            sources.append(DatalakeS3Path(layer_and_name))
        else:
            sources.append(DatalakeS3Path(layer_and_name, True))

    sources = [DatalakeSource(source, rename_dict=renaming_map) for source in sources]
    snow_proc = SnowflakeSourceProcessor('datalake_sources')
    errors = []

    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_source = {executor.submit(snow_proc.process_datasource, source): source for source in sources}

        for future in as_completed(future_to_source):
            result = future.exception()
            if result:
                errors.append(f"Error processing {future_to_source[future].name}: {result}")

    # Display errors after all threads finish
    if errors:
        print("\n".join(errors))
        raise ChildProcessError('Not all sources were ingested!')


with DAG(
        dag_id=FLOW_NAME,
        schedule_interval=None,
        description="dag to copy raw parquet files to snowflake",
        start_date=datetime.datetime(2022, 1, 1),
        on_failure_callback=failure_callback,
) as dag:
    
    ingest = PythonOperator(
        task_id='ingest_sources',
        python_callable=ingest_all
    )