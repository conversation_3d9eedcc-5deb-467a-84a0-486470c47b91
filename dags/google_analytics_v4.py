# DOCS - https://mint-ai.atlassian.net/wiki/spaces/TEAMJ/pages/**********/Pre-IMPL+Doc+GA4
# API - https://mint-ai.atlassian.net/wiki/spaces/TEAMJ/pages/**********/GOOGLE+ANALITICS+4+GA4+API+investigation
import datetime
import json
import logging
import time
import traceback
import pandas as pd
from dataclasses import dataclass
from typing import Optional
from airflow import DAG, XComArg
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from pandas import DataFrame
from re import sub

from airflow_common.dag_config_setter import DagConfigSetter
from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.oauth2.credentials import Credentials

from myn_airflow_lib.commons import (EtlEndpointProcessor, failure_callback, filter_out_standard_queries, get_date_to_execute_on, iter_batch,
                                     set_execution_dates, set_config, enable_hist_log_check_based_on_conf,
                                     split_date_range_period)
from myn_airflow_lib.constants import (EXECUTOR_STABLE_NODES, GA4_ETL_ENDPOINT, GA4_SCOPES, GA4_TOKEN_URI,
                                       HISTORICAL_LOG_TABLE)
from myn_airflow_lib.custom_operators import CleanupXcomsOperator
from myn_airflow_lib.resolvers import EnvironmentResolver
from myn_airflow_lib.datadog import increment_metric, init_datadog

CUSTOM_METRIC_MAPPING = {284939608: [{'name': 'customEvent:revenue_for_mint'}]}

pg_hook = PostgresHook(postgres_conn_id='uri_reports')
pg_hook.log.disabled = True
FLOW_NAME = 'GOOGLE_ANALYTICS_V4'
CAMPAIGN_TYPE_TO_LOG = 'GA4_V2'
env_resolver = EnvironmentResolver()
state = {'dates': {}}
dates = state['dates']

env_type = env_resolver.environment
# REPORTS_BASE_TABLE_NAME & REPORTS_AUTOTAG_TABLE_NAME -> for conversions and active_users only
REPORTS_BASE_TABLE_NAME = 'reports_ga4_base_performance_v2'
REPORTS_AUTOTAG_TABLE_NAME = 'reports_ga4_autotag_performance_v2'
# REPORTS_BASE_SESSIONS_TABLE_NAME & REPORTS_AUTOTAG_SESSIONS_TABLE_NAME -> for sessions only
REPORTS_BASE_SESSIONS_TABLE_NAME = 'reports_ga4_base_sessions_performance_v2'
REPORTS_AUTOTAG_SESSIONS_TABLE_NAME = 'reports_ga4_autotag_sessions_performance_v2'

init_datadog(env_resolver.datadog_host, env_resolver.datadog_port)
ROWS_LIMIT = 200000
WAITING_TIME = 5


def get_db_value(value) -> str:
    """Stringify value specially for GA4 removing all non-Ascii characters"""
    if isinstance(value, (dict, list)):
        return f"'{json.dumps(value)}'"
    elif isinstance(value, str):
        # removes �
        ascii_value = value.replace('�', '').replace('\x00', '')
        fixed_value = ascii_value.replace("'", "''")
        return f"'{fixed_value}'"
    elif pd.isna(value):
        return 'NULL'
    else:
        return str(value)


@dataclass
class QueryData:
    query_body: dict
    query_type: str
    profile: dict
    campaign_type: str

    @property
    def campaign_detail(self):
        return json.dumps({'property_id': self.profile['property_id'],
                           'campaign_type': self.campaign_type})

    @property
    def client_id(self):
        return self.profile['client_id']

    @property
    def client_secret(self):
        return self.profile['client_secret']

    @property
    def refresh_token(self):
        return self.profile['refresh_token']

    @property
    def company_id(self):
        return self.profile['company_id']

    @property
    def property_id(self):
        return self.profile['property_id']

    def to_dict(self):
        return {'query_body': self.query_body,
                'query_type': self.query_type,
                'profile': self.profile,
                'campaign_type': self.campaign_type}


def get_profiles(**kwargs):
    """Get profiles from etl"""
    config_mock = kwargs['dag_run'].conf.get('etl_endpoint_mock')
    processor = EtlEndpointProcessor(GA4_ETL_ENDPOINT, **kwargs)
    profiles = processor.apply_all()

    if config_mock:
        profiles = config_mock
    return profiles


def get_query_template():
    """Get query template from json file"""
    with open('static/ga_v4_template.json') as file:
        query_template = json.load(file)
    return query_template


@enable_hist_log_check_based_on_conf(state)
def does_hist_log_exist(hist_request: QueryData):
    """Filters historical request if it has been already processed"""
    campaign_detail = hist_request.campaign_detail
    query = f"""
                select id from {HISTORICAL_LOG_TABLE}
                where campaign_type = '{CAMPAIGN_TYPE_TO_LOG}'
                    and campaign_detail = '{campaign_detail}'
                limit 1;
            """
    logging.info(f"Filtering historical data with query - {query}")
    df = pg_hook.get_pandas_df(query)
    return not df.empty


def get_historical_queries(profile: dict, historical_range_mock: Optional[int]) -> list:
    """Get historical queries for each profile"""
    start_date = dates['TWO_YEARS_AGO']
    end_date = dates['TODAY'].strftime('%Y-%m-%d')
    if historical_range_mock:
        logging.info('historical_range_mock is set. Using it')
        start_date = dates['TODAY'] - datetime.timedelta(historical_range_mock)
    start_date = start_date.strftime('%Y-%m-%d')
    # --- add historical chunks
    hist_queries = []
    historical_periods = split_date_range_period(date_from=start_date, date_to=end_date, period_days=30)
    for period in historical_periods:
        hist_req_data = create_queries(profile, 'historical', period[0], period[1])
        hist_queries.extend(hist_req_data)

    # check historical log if exists and filter historical queries
    return [
        query for query in hist_queries
        if not does_hist_log_exist(query)
    ]


def get_standard_queries(profile: dict, **kwargs) -> list:
    """Get standard queries for each profile"""
    today_format = dates['TODAY'].strftime('%Y-%m-%d')
    seven_days_ago_format = get_date_to_execute_on(dates, '%Y-%m-%d', **kwargs)
    date_from = seven_days_ago_format
    date_to = today_format
    queries = create_queries(profile, 'standard', date_from, date_to)
    return queries


def create_queries(profile: dict, query_type: str, start_date: str, end_date: str) -> list:
    """Create queries template body for each profile"""
    queries_list = []
    query_template = get_query_template()
    property_id = profile["property_id"]
    for q_key, q_val in query_template.items():
        q_val['property'] = f'properties/{property_id}'
        q_val['date_ranges'] = [{'start_date': start_date, 'end_date': end_date}]

        # add custom metrics to request body for specific property_id and for ['ga4_base', 'ga4_auto'] queries only
        if property_id in CUSTOM_METRIC_MAPPING.keys() and q_key in ['ga4_base', 'ga4_auto']:
            metrics = q_val['metrics'].copy()
            q_val['metrics'] = metrics + CUSTOM_METRIC_MAPPING[property_id]
        queries_list.append(QueryData(q_val, query_type, profile, q_key))
    return queries_list


@set_config(state)
@set_execution_dates(dates)
def generate_queries(profiles, **kwargs):
    """Generate all queries for each profile"""
    all_queries = []
    historical_range_mock = state['dag_run'].conf.get('historical_range_in_days')
    for profile in profiles:
        historical_queries = get_historical_queries(profile, historical_range_mock)
        if historical_queries is not None:
            all_queries.extend(historical_queries)
        standard_queries = get_standard_queries(profile, **kwargs)
        if standard_queries:
            all_queries.extend(standard_queries)
    dict_queries = list(map(lambda x: x.to_dict(), all_queries))
    dict_queries = filter_out_standard_queries(dict_queries, **kwargs)
    return dict_queries


def request_report(query: QueryData, **kwargs):
    """Request report from GA4 API using BetaAnalyticsDataClient"""
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    responses = []
    try:
        # init client with creds
        client = BetaAnalyticsDataClient(
            credentials=Credentials(
                token=None,
                refresh_token=query.refresh_token,
                client_id=query.client_id,
                client_secret=query.client_secret,
                scopes=(GA4_SCOPES,),
                token_uri=GA4_TOKEN_URI))
        request_body = query.query_body
        logging.info('Requesting report')
        resp = client.run_report(request_body)
        logging.info("Sleeping 10 seconds after requesting report")
        time.sleep(WAITING_TIME)
        responses.append(resp)
        row_count = resp.row_count

        # pagination logic if row_count > max_rows to retrieve
        if row_count > ROWS_LIMIT:
            logging.info(f"Row count {row_count} is more than {ROWS_LIMIT} rows. Going to paginate")
            for offset_item in range(1, row_count//ROWS_LIMIT+1):
                request_body['offset'] = offset_item * ROWS_LIMIT
                resp = client.run_report(request_body)
                logging.info("Sleeping 10 seconds after requesting report")
                time.sleep(WAITING_TIME)
                responses.append(resp)
    except Exception as e:
        logging.error(f"Error {e} on query {query.campaign_detail}")

        increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                         additional_tags=[f"request_type:{query.query_type}", "request_status:error",
                                          f"client:{query.company_id}", f'run_number:{current_run_number}'])
        raise e
    else:

        increment_metric('airflow_request_counter.increment', env_type, FLOW_NAME,
                         additional_tags=[f"request_type:{query.query_type}", "request_status:success",
                                          f"client:{query.company_id}", f'run_number:{current_run_number}'])
        logging.info(f"Successful request on query {query.campaign_detail}")
        return responses


def parse_report_data(responses: list, property_id: int):
    """Parse report responses into pandas DataFrame: STANDARD METRICS + CUSTOM METRICS"""
    # Extract GA4 data into pandas DataFrame
    logging.info(f"Got {len(responses)} responses. Starting to parse")

    # STANDARD REPORTS PARSING WITH STANDARD METRICS
    dfs = []
    for resp in responses:
        data = []
        for row in resp.rows:
            dimension_values = [value.value for value in row.dimension_values]
            metric_values = [value.value for value in row.metric_values]
            data.append(dimension_values + metric_values)

        columns = [dimension.name for dimension in resp.dimension_headers] + [
            metric.name for metric in resp.metric_headers]
        df = pd.DataFrame(data, columns=columns)
        dfs.append(df)
    dfs_filtered = [df for df in dfs if df is not None]
    report = pd.concat(dfs_filtered, ignore_index=True)

    # CUSTOM REPORTS PARSING WITH CUSTOM METRICS
    custom_columns = CUSTOM_METRIC_MAPPING.get(property_id)
    custom_reports = []
    if custom_columns:

        # example -> custom_column_names = ['customEvent:revenue_for_mint']
        custom_column_names = [key['name'] for key in custom_columns]

        # check if just 1 custom_metrics column is in report columns - enough to proceed
        if custom_column_names[0] in report.columns:
            for cust_col in custom_column_names:

                # filter out all zeros data
                custom_report = report[report[cust_col] != '0']

                # example -> cust_col_rename_map = {'customEvent:revenue_for_mint': 'revenue_for_mint'}
                cust_col_rename_map = {cust_col: cust_col.split(':')[1]}
                custom_report = custom_report.rename(columns=cust_col_rename_map)
                renamed_cust_col = cust_col_rename_map[cust_col]

                # populate eventName by custom col name
                custom_report['eventName'] = renamed_cust_col

                # populate keyEvents by custom col values
                custom_report['keyEvents'] = custom_report[renamed_cust_col]
                custom_report = custom_report.drop(columns=[renamed_cust_col])
                custom_reports.append(custom_report)
    custom_reports_filtered = [df for df in custom_reports if df is not None]

    # handle standard and custom reports even if it's absent
    report = pd.concat(custom_reports_filtered + [report], ignore_index=True) if custom_reports_filtered else report

    # mapping for all kinds of report columns
    col_mapping = {'sessionSource': 'source', 'sessionMedium': 'medium', 'sessionCampaignName': 'campaign_name',
                   'sessionManualTerm': 'manual_term', 'sessionManualAdContent': 'ad_content',
                   'eventName': 'event_name', 'sessionGoogleAdsAdGroupId': 'adwords_adgroup_id',
                   'sessionGoogleAdsCampaignId': 'adwords_campaign_id', 'sessionGoogleAdsCreativeId': 'adwords_ad_id',
                   'keyEvents': 'conversions', 'totalRevenue': 'total_revenue', 'engagedSessions': 'engaged_sessions',
                   'grossPurchaseRevenue': 'gross_purchase_revenue', 'activeUsers': 'active_users'}

    final_report = report.rename(columns=col_mapping)
    final_report = final_report.drop_duplicates()
    final_report['property_id'] = int(property_id)
    return final_report


def delete_rows(report: DataFrame, property_id: int, db_table: str):
    """Delete rows from specified db table based on property_id and dates"""
    logging.info(f"Deleting rows from table {db_table}")
    delete_property_id = int(property_id)

    # create tuple of strings for dates to use in delete query
    delete_dates = tuple([str(d) for d in sorted(report['date'].unique())])

    # if there is only one date, we need to remove redundant comma in the end
    dates_to_delete = f"('{delete_dates[0]}')" if len(delete_dates) == 1 else delete_dates

    delete_query = f"DELETE from {db_table} where property_id = {delete_property_id} and date in {dates_to_delete};"
    pg_hook.run(delete_query, True)
    time.sleep(0.5)
    logging.info(f"Deleting rows was finished for table {db_table}")


def put_report_to_db(report: DataFrame, campaign_type: str, company_id: int, property_id: int, **kwargs):
    """Puts data to DB table in 2 stages:
        - delete rows from db table based on property_id and dates
        - insert new data to db table
    """
    current_run_number = kwargs['dag_run'].conf.get('current_run_number')
    """Put report to db based on campaign type"""
    logging.info("Starting to put report to db")
    if campaign_type == 'ga4_base':
        db_table = REPORTS_BASE_TABLE_NAME
        columns_order = ['date', 'property_id', 'source', 'medium', 'campaign_name', 'manual_term', 'ad_content',
                         'event_name', 'conversions', 'active_users']
        report = report[columns_order]

        # delete rows from db table
        delete_rows(report=report, property_id=property_id, db_table=db_table)

        # prepare data to push in DB table
        columns = ', '.join(report.columns)
        rows = list(report.itertuples(index=False, name=None))
        fixed_rows = [tuple(map(get_db_value, row)) for row in rows]

        # batching data to push in DB
        for i, batch_rows in enumerate(iter_batch(fixed_rows, 50000)):
            logging.info(f"Inserting batch[max_size=50000] #{i}")
            values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
            query = f"""INSERT INTO {db_table} ({columns}) VALUES {values_str};"""
            pg_hook.run(query, True)
    elif campaign_type == 'ga4_auto':
        db_table = REPORTS_AUTOTAG_TABLE_NAME
        columns_order = ['date', 'property_id', 'event_name', 'adwords_adgroup_id', 'adwords_campaign_id',
                         'adwords_ad_id', 'conversions', 'active_users']
        report = report[columns_order]

        # delete rows from db table
        delete_rows(report=report, property_id=property_id, db_table=db_table)

        # prepare data to push in DB table
        columns = ', '.join(report.columns)
        rows = list(report.itertuples(index=False, name=None))
        fixed_rows = [tuple(map(get_db_value, row)) for row in rows]

        # batching data to push in DB
        for i, batch_rows in enumerate(iter_batch(fixed_rows, 50000)):
            logging.info(f"Inserting batch[max_size=50000] #{i}")
            values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
            query = f"""INSERT INTO {db_table} ({columns}) VALUES {values_str};"""
            pg_hook.run(query, True)
    elif campaign_type == 'ga4_base_sessions':
        db_table = REPORTS_BASE_SESSIONS_TABLE_NAME
        columns_order = ['date', 'property_id', 'source', 'medium', 'campaign_name', 'manual_term', 'ad_content',
                         'sessions', 'total_revenue', 'transactions', 'gross_purchase_revenue', 'engaged_sessions']
        report = report[columns_order]

        # delete rows from db table
        delete_rows(report=report, property_id=property_id, db_table=db_table)

        # prepare data to push in DB table
        columns = ', '.join(report.columns)
        rows = list(report.itertuples(index=False, name=None))
        fixed_rows = [tuple(map(get_db_value, row)) for row in rows]

        # batching data to push in DB
        for i, batch_rows in enumerate(iter_batch(fixed_rows, 50000)):
            logging.info(f"Inserting batch[max_size=50000] #{i}")
            values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
            query = f"""INSERT INTO {db_table} ({columns}) VALUES {values_str};"""
            pg_hook.run(query, True)
    elif campaign_type == 'ga4_auto_sessions':
        db_table = REPORTS_AUTOTAG_SESSIONS_TABLE_NAME
        columns_order = ['date', 'property_id', 'adwords_adgroup_id', 'adwords_campaign_id', 'adwords_ad_id',
                         'sessions', 'total_revenue', 'transactions', 'gross_purchase_revenue', 'engaged_sessions']
        report = report[columns_order]

        # delete rows from db table
        delete_rows(report=report, property_id=property_id, db_table=db_table)

        # prepare data to push in DB table
        columns = ', '.join(report.columns)
        rows = list(report.itertuples(index=False, name=None))
        fixed_rows = [tuple(map(get_db_value, row)) for row in rows]

        # batching data to push in DB
        for i, batch_rows in enumerate(iter_batch(fixed_rows, 50000)):
            logging.info(f"Inserting batch[max_size=50000] #{i}")
            values_str = ', '.join(map(lambda x: f'({", ".join(x)})', batch_rows))
            query = f"""INSERT INTO {db_table} ({columns}) VALUES {values_str};"""
            pg_hook.run(query, True)

    increment_metric('airflow_data_pushed.increment', env_type, FLOW_NAME, report.shape[0],
                     [f"table:{db_table}", f"client:{company_id}", f'run_number:{current_run_number}'])


def log_hist_request(query: QueryData):
    campaign_detail = query.campaign_detail
    company_id = query.company_id
    query = f"""
            INSERT INTO {HISTORICAL_LOG_TABLE}(company_id, campaign_type, campaign_detail)
            Values (%(company_id)s, %(campaign_type)s, %(campaign_detail)s)
            """
    data = {'company_id': company_id,
            'campaign_type': CAMPAIGN_TYPE_TO_LOG,
            'campaign_detail': campaign_detail}
    pg_hook.run(query, True, data)


def process_query(query_data: dict, **kwargs):
    """Process query data"""
    logging.info("Processing query")
    query = QueryData(**query_data)
    report_data = request_report(query, **kwargs)
    # saving reports in db
    report = parse_report_data(report_data, query.property_id)
    logging.info(f"Parsed report of len {len(report)}")
    if report.empty:
        logging.info("Empty report")
    else:
        put_report_to_db(report, query.campaign_type, query.company_id, query.property_id, **kwargs)
        logging.info('Report is pushed')
    if query.query_type == 'historical':
        log_hist_request(query)
        logging.info('Log is pushed')


def run_reports(queries: list, **kwargs):
    """Run reports for each query"""
    errors = {"historical": 0,
              "standard": 0}
    logging.info(f'Going to process {len(queries)} queries')
    for query_index, query_data in enumerate(queries):
        logging.info(f'Going to process {query_index} / {len(queries)} queries')
        try:
            process_query(query_data, **kwargs)
        except Exception as e:
            errors.setdefault(query_data['query_type'], 0)
            errors[query_data['query_type']] += 1
            error_message = f'had an error {e} on {json.dumps(query_data)} item \n' + traceback.format_exc()
            logging.error(error_message)
    for error_type, error_count in errors.items():
        all_requests_count = len(list(filter(lambda x: x["query_type"] == error_type, queries)))
        logging.info(f'{error_type} errors {error_count}/{all_requests_count}')


with DAG(FLOW_NAME,
         description='ga4 flow dag',
         schedule_interval=None,
         params=DagConfigSetter(FLOW_NAME).get_params(),
         start_date=datetime.datetime(2022, 1, 1),
         catchup=False,
         tags=['ingestion_flow']) as dag:
    t1 = PythonOperator(
        task_id='get_profiles',
        on_failure_callback=failure_callback,
        python_callable=get_profiles,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t2 = PythonOperator(
        task_id='generate_queries',
        on_failure_callback=failure_callback,
        python_callable=generate_queries,
        op_kwargs={"profiles": XComArg(t1)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    t3 = PythonOperator(
        task_id="run_reports",
        on_failure_callback=failure_callback,
        python_callable=run_reports,
        op_kwargs={"queries": XComArg(t2)},
        executor_config=EXECUTOR_STABLE_NODES
    )

    cleanup_xcoms = CleanupXcomsOperator(
        task_id="cleanup_xcoms",
        on_failure_callback=failure_callback,
        executor_config=EXECUTOR_STABLE_NODES
    )

    t1 >> t2 >> t3 >> cleanup_xcoms
