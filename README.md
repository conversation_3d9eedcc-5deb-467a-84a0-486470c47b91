### Airflow service repository

### How to run locally 

1. Follow guide https://airflow.apache.org/docs/apache-airflow/stable/howto/docker-compose/index.html
2. After setting up the docker-compose.yaml add there volume with aws credensials
```
  volumes:
    - ./dags:/opt/airflow/dags
    - ./logs:/opt/airflow/logs
    - ./plugins:/opt/airflow/plugins
    - ./lib:/opt/airflow/lib
    - ./static:/opt/airflow/static
    - .~/.aws/:/root/.aws:ro (*this one*)
```
3. add `_PIP_ADDITIONAL_REQUIREMENTS: ${_PIP_ADDITIONAL_REQUIREMENTS:-}` key value pair to the docker-compose.yaml so that docker will install additional dependencies (from env variable)
```
environment:
    &airflow-common-env
    AIRFLOW__CORE__EXECUTOR: CeleryExecutor
    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
    # For backward compatibility, with Airflow <2.3
    AIRFLOW__CORE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
    AIRFLOW__CELERY__RESULT_BACKEND: db+*********************************************
    AIRFLOW__CELERY__BROKER_URL: redis://:@redis:6379/0
    AIRFLOW__CORE__FERNET_KEY: ''
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
    AIRFLOW__CORE__LOAD_EXAMPLES: 'true'
    AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.basic_auth'
    _PIP_ADDITIONAL_REQUIREMENTS: ${_PIP_ADDITIONAL_REQUIREMENTS:-}
```
4. add to each service in docker-compose yaml file
```
    env_file:
    - .env
```

5. Contact any member of team J to get a copy of .env file so it can be used to run dags locally

### How to run tests
Run tests locally:
```
If you don't have Python packages locally:

$ pip install -r requirements.txt

Then:

$ python -m pytest lib/omni/tests -p no:cacheprovider
```