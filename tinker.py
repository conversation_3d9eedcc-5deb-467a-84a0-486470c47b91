import json
import logging
from kafka import KafkaProducer
from kafka.errors import KafkaError
from kafka.admin import KafkaAdminClient, NewTopic

# Configuration for the test
kafka_broker = "kafka-0.test.dev.mint.ai:9092,kafka-1.test.dev.mint.ai:9092,kafka-2.test.dev.mint.ai:9092"
topic = "DATASOURCE_NOTIFICATION"
message = {"test": "message"}

# Set up logging
logging.basicConfig(level=logging.INFO)

def create_topic_if_not_exists(kafka_broker, topic):
    try:
        admin_client = KafkaAdminClient(bootstrap_servers=kafka_broker)
        # Check if topic exists
        topic_list = admin_client.list_topics()
        if topic not in topic_list:
            logging.info(f"Topic '{topic}' does not exist. Creating the topic...")
            new_topic = NewTopic(name=topic, num_partitions=1, replication_factor=1)
            admin_client.create_topics(new_topics=[new_topic], validate_only=False)
            logging.info(f"Topic '{topic}' created successfully.")
        else:
            logging.info(f"Topic '{topic}' already exists.")
    except KafkaError as e:
        logging.error(f"Failed to check or create topic '{topic}': {e}")
    except Exception as e:
        logging.exception(f"Unexpected error while creating Kafka topic: {e}")

try:
    # Initialize Kafka producer
    if isinstance(kafka_broker, str):
        kafka_broker = kafka_broker.split(',')
    logging.info(f"Connecting to Kafka broker at {kafka_broker}")

    # Create topic if it doesn't exist
    create_topic_if_not_exists(kafka_broker, topic)

    # Initialize the Kafka producer
    producer = KafkaProducer(
        bootstrap_servers=kafka_broker,
        value_serializer=lambda v: json.dumps(v).encode('utf-8'),
    )

    # Send a test message
    logging.info(f"Sending message to topic '{topic}'")
    future = producer.send(topic, value=message)
    
    # Wait for the result (with a timeout)
    record_metadata = future.get(timeout=120)

    # Log the result
    logging.info(f"Message sent successfully: topic={record_metadata.topic} "
                 f"partition={record_metadata.partition} offset={record_metadata.offset}")
    
except KafkaError as e:
    logging.error(f"Failed to send message to Kafka: {e}")
except Exception as e:
    logging.exception(f"Unexpected error while sending Kafka message: {e}")
finally:
    if 'producer' in locals():
        producer.flush()
        producer.close()
        logging.info("Kafka producer closed.")
