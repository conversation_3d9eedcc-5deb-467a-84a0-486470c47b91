def escape_for_json_sql(value: str) -> str:
    """
    Escapes a string for use in a JSON-embedded SQL formula.
    Ensures single quotes are surrounded by '\'' and internal single quotes are doubled.
    """
    if value is None:
        return "NULL"
    # Replace single quotes with doubled single quotes for SQL, then wrap in '\''.
    escaped_value = value.replace("'", "''")
    return "'\\''" + escaped_value + "\\'''"



def generate_imply_template(suffix: str, index: int, currency: str, prefix: str, metric: str, goal_no: int, label_column: str, data_source_filter: str) -> str:
    """
    Generates an IMPLY template with properly escaped SQL for JSON compatibility.
    """
    escaped_metric = escape_for_json_sql(metric)
    escaped_filter = escape_for_json_sql(data_source_filter)  # Ensure consistent escaping for filters
    return f"""
        {{
            "name": "SUM_auto_generated_currency_{suffix.replace(" ", "_")}_{index}_in_{currency}_metric",
            "title": "{prefix} {metric} in {currency} ({suffix})",
            "formula": "SUM(t.currency_{goal_no}_spend) / SUM(t.conversions) FILTER (WHERE t.{label_column} = {escaped_metric} {escaped_filter})",
            "numberStyle": {{
                "type": "raw",
                "leading": 1,
                "minDecimals": 2,
                "maxDecimals": 2,
                "abbreviationSpace": false
            }}
        }}"""

# Example usage
suffix = "example_suffix"
index = 1
currency = "USD"
prefix = "Total"
metric = "Installazioni Prénatal (Android) alla data e all'ora seguenti: 2024-07-16T09:03:02.193"
goal_no = 1
label_column = "conversion_metric"
data_source_filter = "AND t.data_source = 'Google Ads'"

imply_template = generate_imply_template(suffix, index, currency, prefix, metric, goal_no, label_column, data_source_filter)
print(imply_template)
